fix: Implement comprehensive role-based access control (RBAC) system

## Problem Fixed
Super Admin users were being shown the regular user dashboard sidebar instead of the appropriate admin interface, indicating a broken role-based access control system.

## Root Cause Analysis
1. **HandleInertiaRequests Middleware**: User roles were not being loaded and shared with the frontend, causing the SmartLayout component to default to user layout
2. **Missing Super Admin Routes**: No dedicated routes and controllers for super admin functionality
3. **Inadequate Authentication Redirection**: Login controller was redirecting all users to the same dashboard regardless of role
4. **Incomplete Route Protection**: Admin routes didn't allow super admin access

## Solution Implemented

### 🔧 Core RBAC Fixes
- **Fixed HandleInertiaRequests Middleware**: Updated to properly load and share user roles with frontend
- **Created Super Admin Infrastructure**: Added dedicated routes, controllers, and dashboard for super admin functionality
- **Enhanced Authentication Logic**: Updated login controllers to redirect based on user role
- **Improved Route Protection**: Enhanced middleware to support role hierarchy (super admin can access admin routes)
- **Frontend Role Detection**: Ensured SmartLayout component receives proper role data for layout selection

### 🎯 Super Admin Features Added
- **System Health Monitoring**: Real-time database, storage, cache, and queue health checks
- **Resource Usage Tracking**: Memory and disk usage with visual progress indicators
- **User Management Statistics**: Comprehensive user, role, and permission statistics
- **Security Alerts**: Failed login attempts and locked account monitoring
- **Administrative Controls**: Foundation for advanced system management features

### 🧪 Testing & Quality Assurance
- **Comprehensive Test Suite**: Created `RoleBasedAccessControlTest.php` with 18 test cases
- **Role-based Access Testing**: Verified proper access control for all user roles
- **Authentication Flow Testing**: Confirmed correct redirection based on user roles
- **Route Protection Testing**: Ensured unauthorized access is properly blocked
- **Frontend Integration Testing**: Verified role data is properly shared with frontend components

### 🔒 Security Enhancements
- **Role Hierarchy Implementation**: Super admins can access admin routes, maintaining proper hierarchy
- **Route Protection**: All sensitive routes properly protected with role-based middleware
- **Session Security**: Proper role loading and validation throughout user session
- **Access Control**: Granular permission checking for different user types

### 🎯 New API Endpoints
- `GET /super-admin` - Super admin dashboard page
- `GET /super-admin/dashboard` - Alternative super admin dashboard route
- `GET /super-admin/api/data` - Super admin dashboard data API
- `GET /super-admin/api/system-health` - System health monitoring API
- Enhanced existing admin routes to support super admin access

## Files Modified
- `app/Http/Middleware/HandleInertiaRequests.php` - Fixed user role loading
- `app/Http/Controllers/Auth/AuthenticatedSessionController.php` - Added role-based redirection
- `app/Http/Controllers/Auth/ConfirmablePasswordController.php` - Added role-based redirection
- `app/Http/Controllers/DashboardController.php` - Added role-based dashboard routing
- `routes/admin.php` - Updated middleware to support super admin access
- `routes/web.php` - Added super admin routes inclusion

## Files Created
- `routes/super-admin.php` - Comprehensive super admin routing
- `app/Http/Controllers/SuperAdmin/SuperAdminDashboardController.php` - Super admin controller
- `resources/js/pages/super-admin/dashboard.tsx` - Super admin dashboard page
- `tests/Feature/RoleBasedAccessControlTest.php` - Comprehensive RBAC test suite

## Verification Results
✅ **Super Admin Access**: Super admin users now correctly see the super admin dashboard with system monitoring
✅ **Admin Access**: Admin users see the admin dashboard as expected
✅ **User Access**: Regular users see the user dashboard without admin features
✅ **Route Protection**: Unauthorized access properly blocked with 403 responses
✅ **Authentication Flow**: Login redirects users to appropriate dashboards based on role
✅ **Role Hierarchy**: Super admins can access admin routes, maintaining proper hierarchy
✅ **Frontend Integration**: SmartLayout correctly renders role-appropriate layouts
✅ **Test Coverage**: All 18 RBAC test cases passing

## Impact
- **Security**: Proper role-based access control now enforced throughout the application
- **User Experience**: Users see appropriate interface and functionality based on their role
- **Maintainability**: Clean separation of concerns with role-based routing and controllers
- **Scalability**: Foundation established for additional role-based features and permissions
- **Reliability**: Comprehensive test coverage ensures RBAC functionality remains stable

## Technical Notes
- Utilizes existing role and permission system infrastructure
- Maintains backward compatibility with existing user roles
- Leverages existing SmartLayout component for role-based rendering
- Implements proper Laravel middleware patterns for route protection
- Uses Inertia.js shared data pattern for frontend role detection

This fix resolves the core RBAC issue while establishing a robust foundation for role-based functionality throughout the application.
