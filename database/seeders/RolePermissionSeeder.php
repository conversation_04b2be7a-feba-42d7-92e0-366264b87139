<?php

namespace Database\Seeders;

use App\Models\Permission;
use App\Models\Role;
use Illuminate\Database\Seeder;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // User Management
            ['name' => 'users.view', 'display_name' => 'View Users', 'group_name' => 'User Management'],
            ['name' => 'users.create', 'display_name' => 'Create Users', 'group_name' => 'User Management'],
            ['name' => 'users.edit', 'display_name' => 'Edit Users', 'group_name' => 'User Management'],
            ['name' => 'users.delete', 'display_name' => 'Delete Users', 'group_name' => 'User Management'],
            
            // Product Management
            ['name' => 'products.view', 'display_name' => 'View Products', 'group_name' => 'Product Management'],
            ['name' => 'products.create', 'display_name' => 'Create Products', 'group_name' => 'Product Management'],
            ['name' => 'products.edit', 'display_name' => 'Edit Products', 'group_name' => 'Product Management'],
            ['name' => 'products.delete', 'display_name' => 'Delete Products', 'group_name' => 'Product Management'],
            
            // Order Management
            ['name' => 'orders.view', 'display_name' => 'View Orders', 'group_name' => 'Order Management'],
            ['name' => 'orders.edit', 'display_name' => 'Edit Orders', 'group_name' => 'Order Management'],
            ['name' => 'orders.delete', 'display_name' => 'Delete Orders', 'group_name' => 'Order Management'],
            
            // File Management
            ['name' => 'files.view', 'display_name' => 'View Files', 'group_name' => 'File Management'],
            ['name' => 'files.upload', 'display_name' => 'Upload Files', 'group_name' => 'File Management'],
            ['name' => 'files.delete', 'display_name' => 'Delete Files', 'group_name' => 'File Management'],
            
            // System Management
            ['name' => 'system.manage', 'display_name' => 'Manage System', 'group_name' => 'System Management'],
            ['name' => 'system.settings', 'display_name' => 'System Settings', 'group_name' => 'System Management'],
            ['name' => 'system.logs', 'display_name' => 'View System Logs', 'group_name' => 'System Management'],
            
            // Payment Management
            ['name' => 'payments.view', 'display_name' => 'View Payments', 'group_name' => 'Payment Management'],
            ['name' => 'payments.refund', 'display_name' => 'Process Refunds', 'group_name' => 'Payment Management'],
            
            // Security Management
            ['name' => 'security.monitor', 'display_name' => 'Security Monitoring', 'group_name' => 'Security Management'],
            ['name' => 'security.ip_management', 'display_name' => 'IP Management', 'group_name' => 'Security Management'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // Create roles
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'Super Administrator',
                'description' => 'Full system access',
                'is_system' => true,
            ],
            [
                'name' => 'admin',
                'display_name' => 'Administrator',
                'description' => 'Administrative access',
                'is_system' => false,
            ],
            [
                'name' => 'manager',
                'display_name' => 'Manager',
                'description' => 'Management access',
                'is_system' => false,
            ],
            [
                'name' => 'customer',
                'display_name' => 'Customer',
                'description' => 'Customer access',
                'is_system' => false,
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );

            // Assign permissions to roles
            if ($roleData['name'] === 'super_admin') {
                // Super admin gets all permissions
                $role->permissions()->sync(Permission::all()->pluck('id'));
            } elseif ($roleData['name'] === 'admin') {
                // Admin gets most permissions except system management
                $adminPermissions = Permission::whereNotIn('group_name', ['System Management'])->pluck('id');
                $role->permissions()->sync($adminPermissions);
            } elseif ($roleData['name'] === 'manager') {
                // Manager gets view and edit permissions
                $managerPermissions = Permission::whereIn('name', [
                    'products.view', 'products.edit',
                    'orders.view', 'orders.edit',
                    'files.view', 'files.upload',
                    'payments.view'
                ])->pluck('id');
                $role->permissions()->sync($managerPermissions);
            } elseif ($roleData['name'] === 'customer') {
                // Customer gets minimal permissions
                $customerPermissions = Permission::whereIn('name', [
                    'products.view'
                ])->pluck('id');
                $role->permissions()->sync($customerPermissions);
            }
        }
    }
}
