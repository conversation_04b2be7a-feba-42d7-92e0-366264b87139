<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create root categories
        $categories = [
            [
                'name' => 'Software',
                'description' => 'Digital software products and applications',
                'sort_order' => 1,
                'children' => [
                    ['name' => 'Desktop Applications', 'description' => 'Software for desktop computers'],
                    ['name' => 'Mobile Apps', 'description' => 'Applications for mobile devices'],
                    ['name' => 'Web Applications', 'description' => 'Browser-based applications'],
                    ['name' => 'Plugins & Extensions', 'description' => 'Browser and software plugins'],
                ]
            ],
            [
                'name' => 'Digital Media',
                'description' => 'Digital media files and content',
                'sort_order' => 2,
                'children' => [
                    ['name' => 'Audio Files', 'description' => 'Music, sound effects, and audio content'],
                    ['name' => 'Video Files', 'description' => 'Movies, tutorials, and video content'],
                    ['name' => 'Images & Graphics', 'description' => 'Photos, illustrations, and graphic files'],
                    ['name' => 'Documents', 'description' => 'PDFs, eBooks, and document files'],
                ]
            ],
            [
                'name' => 'Templates & Themes',
                'description' => 'Design templates and website themes',
                'sort_order' => 3,
                'children' => [
                    ['name' => 'Website Templates', 'description' => 'HTML, CSS, and JavaScript templates'],
                    ['name' => 'WordPress Themes', 'description' => 'Themes for WordPress websites'],
                    ['name' => 'Presentation Templates', 'description' => 'PowerPoint and Keynote templates'],
                    ['name' => 'Design Assets', 'description' => 'Icons, fonts, and design elements'],
                ]
            ],
            [
                'name' => 'Educational Content',
                'description' => 'Learning materials and educational resources',
                'sort_order' => 4,
                'children' => [
                    ['name' => 'Online Courses', 'description' => 'Video courses and tutorials'],
                    ['name' => 'eBooks', 'description' => 'Digital books and guides'],
                    ['name' => 'Study Materials', 'description' => 'Notes, worksheets, and study guides'],
                    ['name' => 'Certification Prep', 'description' => 'Exam preparation materials'],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = ProductCategory::create([
                'name' => $categoryData['name'],
                'description' => $categoryData['description'],
                'sort_order' => $categoryData['sort_order'],
                'is_active' => true,
            ]);

            // Create child categories
            if (isset($categoryData['children'])) {
                foreach ($categoryData['children'] as $index => $childData) {
                    ProductCategory::create([
                        'parent_id' => $category->id,
                        'name' => $childData['name'],
                        'description' => $childData['description'],
                        'sort_order' => $index + 1,
                        'is_active' => true,
                    ]);
                }
            }
        }

        // Create some sample products
        $this->createSampleProducts();
    }

    /**
     * Create sample products for testing.
     */
    private function createSampleProducts(): void
    {
        $user = User::first(); // Get the first user as creator
        
        $products = [
            [
                'category' => 'Desktop Applications',
                'name' => 'Photo Editor Pro',
                'description' => 'Professional photo editing software with advanced features for photographers and designers.',
                'short_description' => 'Professional photo editing software',
                'price' => 99.99,
                'sale_price' => 79.99,
                'featured' => true,
            ],
            [
                'category' => 'Mobile Apps',
                'name' => 'Task Manager Mobile',
                'description' => 'Comprehensive task management application for iOS and Android devices.',
                'short_description' => 'Task management for mobile',
                'price' => 4.99,
                'featured' => true,
            ],
            [
                'category' => 'WordPress Themes',
                'name' => 'Business Pro Theme',
                'description' => 'Modern and responsive WordPress theme perfect for business websites.',
                'short_description' => 'Professional WordPress theme',
                'price' => 59.99,
                'sale_price' => 39.99,
                'featured' => false,
            ],
            [
                'category' => 'Online Courses',
                'name' => 'Web Development Masterclass',
                'description' => 'Complete web development course covering HTML, CSS, JavaScript, and modern frameworks.',
                'short_description' => 'Complete web development course',
                'price' => 199.99,
                'featured' => true,
            ],
            [
                'category' => 'Audio Files',
                'name' => 'Royalty-Free Music Pack',
                'description' => 'Collection of 50 high-quality royalty-free music tracks for commercial use.',
                'short_description' => 'Royalty-free music collection',
                'price' => 29.99,
                'featured' => false,
            ],
            [
                'category' => 'eBooks',
                'name' => 'Digital Marketing Guide',
                'description' => 'Comprehensive guide to digital marketing strategies and best practices.',
                'short_description' => 'Digital marketing strategies guide',
                'price' => 19.99,
                'featured' => false,
            ],
        ];

        foreach ($products as $productData) {
            $category = ProductCategory::where('name', $productData['category'])->first();
            
            if ($category) {
                Product::create([
                    'category_id' => $category->id,
                    'name' => $productData['name'],
                    'description' => $productData['description'],
                    'short_description' => $productData['short_description'],
                    'price' => $productData['price'],
                    'sale_price' => $productData['sale_price'] ?? null,
                    'status' => 'active',
                    'featured' => $productData['featured'],
                    'digital' => true,
                    'downloadable' => true,
                    'download_limit' => 5,
                    'download_expiry_days' => 30,
                    'created_by' => $user?->id,
                ]);
            }
        }
    }
}
