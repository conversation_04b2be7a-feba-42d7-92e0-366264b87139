<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->uuid('uuid')->unique()->after('id');
            $table->string('first_name', 100)->nullable()->after('name');
            $table->string('last_name', 100)->nullable()->after('first_name');
            $table->string('phone', 20)->nullable()->after('last_name');
            $table->string('avatar')->nullable()->after('phone');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active')->after('avatar');
            $table->string('email_verification_token')->nullable()->after('email_verified_at');
            $table->string('password_reset_token')->nullable()->after('password');
            $table->timestamp('password_reset_expires_at')->nullable()->after('password_reset_token');
            $table->timestamp('last_login_at')->nullable()->after('password_reset_expires_at');
            $table->string('last_login_ip', 45)->nullable()->after('last_login_at');
            $table->integer('failed_login_attempts')->default(0)->after('last_login_ip');
            $table->timestamp('locked_until')->nullable()->after('failed_login_attempts');
            $table->boolean('two_factor_enabled')->default(false)->after('locked_until');
            $table->string('two_factor_secret')->nullable()->after('two_factor_enabled');
            $table->softDeletes()->after('updated_at');
            
            // Add indexes for performance
            $table->index('email');
            $table->index('status');
            $table->index('uuid');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['email']);
            $table->dropIndex(['status']);
            $table->dropIndex(['uuid']);
            $table->dropSoftDeletes();
            $table->dropColumn([
                'uuid', 'first_name', 'last_name', 'phone', 'avatar', 'status',
                'email_verification_token', 'password_reset_token', 'password_reset_expires_at',
                'last_login_at', 'last_login_ip', 'failed_login_attempts', 'locked_until',
                'two_factor_enabled', 'two_factor_secret'
            ]);
        });
    }
};
