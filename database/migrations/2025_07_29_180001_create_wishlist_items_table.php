<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wishlist_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('wishlist_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('price_when_added', 10, 2)->nullable(); // Track price when added
            $table->text('notes')->nullable(); // User notes about the item
            $table->integer('priority')->default(0); // Priority/order in wishlist
            $table->boolean('notify_on_sale')->default(false); // Notify when on sale
            $table->boolean('notify_on_restock')->default(false); // Notify when back in stock
            $table->timestamps();
            
            // Indexes for performance
            $table->index('wishlist_id');
            $table->index('product_id');
            $table->index('priority');
            $table->index('notify_on_sale');
            $table->index('notify_on_restock');
            $table->index(['wishlist_id', 'priority']);
            
            // Ensure unique product per wishlist
            $table->unique(['wishlist_id', 'product_id'], 'unique_wishlist_product');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wishlist_items');
    }
};
