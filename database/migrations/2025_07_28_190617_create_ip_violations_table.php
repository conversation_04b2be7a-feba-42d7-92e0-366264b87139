<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ip_violations', function (Blueprint $table) {
            $table->id();
            $table->string('ip_address', 45);
            $table->string('violation_type', 100);
            $table->integer('count')->default(1);
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('file_id')->nullable()->constrained()->onDelete('set null');
            $table->json('details')->nullable();
            $table->timestamp('first_violation_at')->useCurrent();
            $table->timestamp('last_violation_at')->useCurrent();
            
            // Unique constraint to prevent duplicate entries
            $table->unique(['ip_address', 'violation_type'], 'unique_ip_violation');
            
            // Indexes for performance
            $table->index('ip_address');
            $table->index('violation_type');
            $table->index('count');
            $table->index('user_id');
            $table->index('last_violation_at');
            $table->index(['ip_address', 'count']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ip_violations');
    }
};
