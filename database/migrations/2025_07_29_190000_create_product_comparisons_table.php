<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_comparisons', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // Null for guest users
            $table->string('session_id', 255)->nullable(); // For guest users
            $table->string('name', 255)->default('Product Comparison');
            $table->json('product_ids'); // Array of product IDs being compared
            $table->json('settings')->nullable(); // Comparison settings like which attributes to show
            $table->boolean('is_public')->default(false);
            $table->timestamp('expires_at')->nullable(); // For guest comparisons
            $table->timestamps();
            
            // Indexes for performance
            $table->index('user_id');
            $table->index('session_id');
            $table->index('is_public');
            $table->index('expires_at');
            $table->index(['user_id', 'created_at']);
            $table->index(['session_id', 'expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_comparisons');
    }
};
