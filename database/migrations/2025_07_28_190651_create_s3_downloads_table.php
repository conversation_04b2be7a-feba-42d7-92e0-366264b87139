<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('s3_downloads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('file_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->string('download_token')->nullable();
            $table->string('ip_address', 45);
            $table->text('user_agent')->nullable();
            $table->string('device_fingerprint')->nullable();
            $table->string('s3_key', 500);
            $table->string('s3_bucket');
            $table->timestamp('presigned_url_expires_at')->nullable();
            $table->integer('download_count')->default(1);
            $table->bigInteger('file_size')->unsigned()->nullable();
            $table->integer('download_duration')->nullable(); // in seconds
            $table->boolean('is_active')->default(true);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('expires_at')->nullable();
            
            // Indexes for performance and analytics
            $table->index('file_id');
            $table->index('user_id');
            $table->index('order_id');
            $table->index('ip_address');
            $table->index('created_at');
            $table->index('expires_at');
            $table->index('s3_bucket');
            $table->index(['file_id', 'user_id']);
            $table->index(['ip_address', 'created_at']);
            $table->index(['s3_bucket', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('s3_downloads');
    }
};
