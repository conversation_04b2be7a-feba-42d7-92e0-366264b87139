<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('download_tokens', function (Blueprint $table) {
            $table->id();
            $table->string('token')->unique();
            $table->foreignId('file_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->string('device_fingerprint')->nullable();
            $table->timestamp('expires_at');
            $table->integer('download_count')->default(0);
            $table->integer('max_downloads')->default(1);
            $table->boolean('is_active')->default(true);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('used_at')->nullable();
            
            // Indexes for performance
            $table->index('token');
            $table->index('file_id');
            $table->index('user_id');
            $table->index('order_id');
            $table->index('expires_at');
            $table->index('ip_address');
            $table->index(['token', 'is_active']);
            $table->index(['file_id', 'user_id']);
            $table->index(['expires_at', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('download_tokens');
    }
};
