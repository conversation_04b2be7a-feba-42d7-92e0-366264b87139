<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('product_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('original_name');
            $table->string('file_name');
            $table->string('file_path', 500)->nullable();
            $table->bigInteger('file_size')->unsigned()->nullable();
            $table->string('mime_type', 100)->nullable();
            $table->string('file_hash', 64)->nullable();
            $table->enum('storage_type', ['local', 's3', 'hybrid'])->default('local');
            $table->string('s3_bucket')->nullable();
            $table->string('s3_key', 500)->nullable();
            $table->string('s3_region', 50)->nullable();
            $table->json('s3_metadata')->nullable();
            $table->integer('download_count')->default(0);
            $table->boolean('is_active')->default(true);
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index('product_id');
            $table->index('storage_type');
            $table->index('file_hash');
            $table->index('is_active');
            $table->index('uuid');
            $table->index(['product_id', 'is_active']);
            $table->index(['storage_type', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
