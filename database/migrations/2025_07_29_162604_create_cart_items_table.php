<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('cart_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('product_name'); // Snapshot for consistency
            $table->string('product_sku', 100)->nullable();
            $table->integer('quantity')->default(1);
            $table->decimal('unit_price', 10, 2);
            $table->decimal('total_price', 10, 2);
            $table->json('product_data')->nullable(); // Store product snapshot
            $table->json('options')->nullable(); // For product variations/options
            $table->timestamps();

            // Indexes for performance
            $table->index('cart_id', 'idx_cart_items_cart_id');
            $table->index('product_id', 'idx_cart_items_product_id');
            $table->index(['cart_id', 'product_id'], 'idx_cart_items_cart_product');

            // Unique constraint to prevent duplicate items in same cart
            $table->unique(['cart_id', 'product_id'], 'cart_product_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cart_items');
    }
};
