<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carts', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('session_id')->nullable()->index(); // For guest carts
            $table->decimal('subtotal', 10, 2)->default(0.00);
            $table->decimal('tax_amount', 10, 2)->default(0.00);
            $table->decimal('discount_amount', 10, 2)->default(0.00);
            $table->decimal('total_amount', 10, 2)->default(0.00);
            $table->string('currency', 3)->default('USD');
            $table->json('metadata')->nullable(); // For storing additional cart data
            $table->timestamp('expires_at')->nullable(); // For guest cart expiration
            $table->timestamps();

            // Indexes for performance
            $table->index('user_id', 'idx_carts_user_id');
            $table->index('session_id', 'idx_carts_session_id');
            $table->index('expires_at', 'idx_carts_expires_at');
            $table->index(['user_id', 'session_id'], 'idx_carts_user_session');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carts');
    }
};
