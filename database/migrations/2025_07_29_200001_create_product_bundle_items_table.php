<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_bundle_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bundle_id')->constrained('product_bundles')->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('quantity')->default(1);
            $table->decimal('price_override', 10, 2)->nullable(); // Override individual product price in bundle
            $table->integer('sort_order')->default(0);
            $table->boolean('is_required')->default(true); // Whether this item is required in the bundle
            $table->boolean('is_optional')->default(false); // Whether this item is optional
            $table->timestamps();
            
            // Indexes for performance
            $table->index('bundle_id');
            $table->index('product_id');
            $table->index('sort_order');
            $table->index('is_required');
            $table->index('is_optional');
            $table->index(['bundle_id', 'sort_order']);
            
            // Ensure unique product per bundle
            $table->unique(['bundle_id', 'product_id'], 'unique_bundle_product');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_bundle_items');
    }
};
