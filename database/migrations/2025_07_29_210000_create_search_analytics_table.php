<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('search_analytics', function (Blueprint $table) {
            $table->id();
            $table->string('query', 255);
            $table->string('normalized_query', 255)->index(); // Normalized version for better analytics
            $table->integer('result_count')->default(0);
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id', 255)->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent', 500)->nullable();
            $table->string('referer', 500)->nullable();
            $table->json('filters_applied')->nullable(); // Filters used in search
            $table->string('sort_by', 50)->nullable();
            $table->string('sort_order', 10)->nullable();
            $table->integer('page')->default(1);
            $table->integer('per_page')->default(12);
            $table->decimal('search_duration', 8, 3)->nullable(); // Time taken for search in seconds
            $table->boolean('had_results')->default(true);
            $table->boolean('clicked_result')->default(false);
            $table->integer('clicked_position')->nullable(); // Position of clicked result
            $table->foreignId('clicked_product_id')->nullable()->constrained('products')->onDelete('set null');
            $table->timestamp('clicked_at')->nullable();
            $table->timestamps();
            
            // Indexes for performance
            $table->index('query', 'idx_search_query');
            $table->index('user_id', 'idx_search_user');
            $table->index('session_id', 'idx_search_session');
            $table->index('had_results', 'idx_search_results');
            $table->index('clicked_result', 'idx_search_clicked');
            $table->index(['created_at', 'had_results'], 'idx_search_date_results');
            $table->index(['query', 'created_at'], 'idx_search_query_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('search_analytics');
    }
};
