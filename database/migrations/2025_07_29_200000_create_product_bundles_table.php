<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_bundles', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name', 255);
            $table->string('slug', 255)->unique();
            $table->text('description')->nullable();
            $table->text('short_description')->nullable();
            $table->decimal('original_price', 10, 2); // Sum of individual product prices
            $table->decimal('bundle_price', 10, 2); // Discounted bundle price
            $table->decimal('discount_amount', 10, 2)->default(0); // Absolute discount
            $table->decimal('discount_percentage', 5, 2)->default(0); // Percentage discount
            $table->string('discount_type', 20)->default('percentage'); // 'percentage' or 'fixed'
            $table->string('image')->nullable();
            $table->json('gallery')->nullable();
            $table->string('status', 20)->default('active'); // active, inactive, draft
            $table->boolean('featured')->default(false);
            $table->integer('sort_order')->default(0);
            $table->date('starts_at')->nullable(); // Bundle availability start date
            $table->date('ends_at')->nullable(); // Bundle availability end date
            $table->integer('max_purchases')->nullable(); // Maximum number of times this bundle can be purchased
            $table->integer('purchases_count')->default(0); // Track how many times purchased
            $table->json('settings')->nullable(); // Additional settings
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->json('tags')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            // Indexes for performance
            $table->index('slug');
            $table->index('status');
            $table->index('featured');
            $table->index('sort_order');
            $table->index(['status', 'featured']);
            $table->index(['starts_at', 'ends_at']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_bundles');
    }
};
