<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('session_id', 255)->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent', 500)->nullable();
            $table->string('referer', 500)->nullable();
            $table->string('source', 100)->nullable(); // search, category, featured, etc.
            $table->integer('view_duration')->nullable(); // Time spent viewing in seconds
            $table->boolean('bounced')->default(false); // Left immediately
            $table->timestamps();
            
            // Indexes for performance
            $table->index('product_id', 'idx_views_product');
            $table->index('user_id', 'idx_views_user');
            $table->index('session_id', 'idx_views_session');
            $table->index('created_at', 'idx_views_date');
            $table->index(['product_id', 'created_at'], 'idx_views_product_date');
            $table->index(['user_id', 'created_at'], 'idx_views_user_date');
            $table->index('source', 'idx_views_source');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_views');
    }
};
