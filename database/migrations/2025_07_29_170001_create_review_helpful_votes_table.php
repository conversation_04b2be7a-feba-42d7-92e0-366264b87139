<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('review_helpful_votes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('review_id')->constrained('product_reviews')->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('is_helpful'); // true = helpful, false = not helpful
            $table->string('ip_address', 45)->nullable();
            $table->timestamps();
            
            // Indexes for performance
            $table->index('review_id');
            $table->index('user_id');
            $table->index('is_helpful');
            $table->index(['review_id', 'is_helpful']);
            
            // Ensure one vote per user per review
            $table->unique(['user_id', 'review_id'], 'unique_user_review_vote');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('review_helpful_votes');
    }
};
