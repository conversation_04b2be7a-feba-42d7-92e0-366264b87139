<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->decimal('average_rating', 3, 2)->default(0.00)->after('downloadable');
            $table->integer('total_reviews')->default(0)->after('average_rating');
            $table->integer('total_ratings')->default(0)->after('total_reviews');
            $table->json('rating_breakdown')->nullable()->after('total_ratings'); // Store count for each rating (1-5)
            $table->boolean('reviews_enabled')->default(true)->after('rating_breakdown');
            
            // Indexes for performance
            $table->index('average_rating');
            $table->index('total_reviews');
            $table->index('reviews_enabled');
            $table->index(['average_rating', 'total_reviews']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex(['products_average_rating_index']);
            $table->dropIndex(['products_total_reviews_index']);
            $table->dropIndex(['products_reviews_enabled_index']);
            $table->dropIndex(['products_average_rating_total_reviews_index']);
            
            $table->dropColumn([
                'average_rating',
                'total_reviews',
                'total_ratings',
                'rating_breakdown',
                'reviews_enabled'
            ]);
        });
    }
};
