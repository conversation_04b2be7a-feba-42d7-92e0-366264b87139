<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_reviews', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('order_id')->nullable()->constrained()->onDelete('set null');
            $table->string('title', 255);
            $table->text('content');
            $table->tinyInteger('rating')->unsigned(); // 1-5 stars
            $table->boolean('is_verified_purchase')->default(false);
            $table->boolean('is_approved')->default(false);
            $table->boolean('is_featured')->default(false);
            $table->json('metadata')->nullable(); // Additional data like helpful votes, etc.
            $table->string('status', 50)->default('pending'); // pending, approved, rejected, hidden
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('admin_notes')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes for performance
            $table->index('product_id');
            $table->index('user_id');
            $table->index('rating');
            $table->index('is_approved');
            $table->index('is_verified_purchase');
            $table->index('status');
            $table->index('created_at');
            $table->index(['product_id', 'is_approved']);
            $table->index(['product_id', 'rating']);
            $table->index(['user_id', 'product_id']); // Prevent duplicate reviews
            $table->index(['is_approved', 'created_at']);
            
            // Ensure one review per user per product
            $table->unique(['user_id', 'product_id'], 'unique_user_product_review');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_reviews');
    }
};
