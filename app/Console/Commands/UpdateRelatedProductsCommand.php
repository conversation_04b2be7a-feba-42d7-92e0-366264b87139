<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Services\RelatedProductsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class UpdateRelatedProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:update-related 
                            {--clear-cache : Clear all related products cache}
                            {--product-id= : Update related products for specific product ID}
                            {--limit=50 : Number of products to process at once}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update related products cache and recommendations';

    protected RelatedProductsService $relatedProductsService;

    public function __construct(RelatedProductsService $relatedProductsService)
    {
        parent::__construct();
        $this->relatedProductsService = $relatedProductsService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting related products update...');

        if ($this->option('clear-cache')) {
            $this->clearAllCaches();
            return 0;
        }

        if ($productId = $this->option('product-id')) {
            $this->updateSingleProduct($productId);
            return 0;
        }

        $this->updateAllProducts();
        return 0;
    }

    /**
     * Clear all related products caches.
     */
    protected function clearAllCaches(): void
    {
        $this->info('Clearing all related products caches...');
        
        $this->relatedProductsService->clearAllRecommendationCaches();
        
        $this->info('✅ All caches cleared successfully');
    }

    /**
     * Update related products for a single product.
     */
    protected function updateSingleProduct(int $productId): void
    {
        $product = Product::find($productId);
        
        if (!$product) {
            $this->error("Product with ID {$productId} not found");
            return;
        }

        $this->info("Updating related products for: {$product->name}");

        // Clear existing cache
        $this->relatedProductsService->clearRelatedProductsCache($productId);

        // Warm up cache by getting related products
        $relatedProducts = $this->relatedProductsService->getRelatedProducts($product, 6);
        
        $this->info("✅ Found {$relatedProducts->count()} related products for {$product->name}");
    }

    /**
     * Update related products for all products.
     */
    protected function updateAllProducts(): void
    {
        $limit = (int) $this->option('limit');
        
        $totalProducts = Product::active()->count();
        $this->info("Processing {$totalProducts} active products...");

        $bar = $this->output->createProgressBar($totalProducts);
        $bar->start();

        $processed = 0;
        $errors = 0;

        Product::active()
            ->chunk($limit, function ($products) use ($bar, &$processed, &$errors) {
                foreach ($products as $product) {
                    try {
                        // Clear existing cache
                        $this->relatedProductsService->clearRelatedProductsCache($product->id);

                        // Warm up cache
                        $this->relatedProductsService->getRelatedProducts($product, 6);
                        
                        $processed++;
                    } catch (\Exception $e) {
                        $errors++;
                        $this->error("Error processing product {$product->id}: " . $e->getMessage());
                    }

                    $bar->advance();
                }
            });

        $bar->finish();
        $this->newLine();

        $this->info("✅ Processing complete!");
        $this->info("Processed: {$processed} products");
        
        if ($errors > 0) {
            $this->warn("Errors: {$errors} products failed");
        }

        // Update trending products cache
        $this->info('Updating trending products cache...');
        $this->relatedProductsService->getTrendingProducts(20);
        $this->info('✅ Trending products cache updated');
    }

    /**
     * Display statistics about related products.
     */
    protected function displayStats(): void
    {
        $stats = $this->relatedProductsService->getRecommendationStats();

        $this->info('Related Products Statistics:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Active Products', $stats['total_products']],
                ['Products with Categories', $stats['products_with_categories']],
                ['Products with Tags', $stats['products_with_tags']],
                ['Products with Orders', $stats['products_with_orders']],
                ['Avg Related Products', $stats['avg_related_products']],
            ]
        );
    }
}
