<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class SystemSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'group',
        'is_public',
        'is_encrypted',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
    ];

    /**
     * Cache key prefix for settings.
     */
    const CACHE_PREFIX = 'system_setting_';

    /**
     * Cache duration in seconds (1 hour).
     */
    const CACHE_DURATION = 3600;

    /**
     * Get the user who last updated this setting.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get public settings.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get settings by group.
     */
    public function scopeGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get a setting value by key.
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . $key;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->getCastedValue();
        });
    }

    /**
     * Set a setting value.
     */
    public static function set(string $key, $value, ?User $user = null): self
    {
        $setting = static::firstOrNew(['key' => $key]);

        // Determine type if not set
        if (!$setting->exists) {
            $setting->type = static::determineType($value);
        }

        $setting->value = static::prepareValue($value, $setting->is_encrypted);
        $setting->updated_by = $user?->id;
        $setting->save();

        // Clear cache
        Cache::forget(self::CACHE_PREFIX . $key);

        return $setting;
    }

    /**
     * Get multiple settings by group.
     */
    public static function getGroup(string $group): array
    {
        $cacheKey = self::CACHE_PREFIX . 'group_' . $group;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($group) {
            return static::group($group)
                         ->get()
                         ->pluck('casted_value', 'key')
                         ->toArray();
        });
    }

    /**
     * Get all public settings for frontend.
     */
    public static function getPublicSettings(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'public';

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () {
            return static::public()
                         ->get()
                         ->pluck('casted_value', 'key')
                         ->toArray();
        });
    }

    /**
     * Get the casted value based on type.
     */
    public function getCastedValue()
    {
        $value = $this->is_encrypted ? Crypt::decryptString($this->value) : $this->value;

        return match ($this->type) {
            'boolean' => filter_var($value, FILTER_VALIDATE_BOOLEAN),
            'integer' => (int) $value,
            'float' => (float) $value,
            'json' => json_decode($value, true),
            'array' => is_string($value) ? explode(',', $value) : $value,
            default => $value,
        };
    }

    /**
     * Get the casted value attribute.
     */
    public function getCastedValueAttribute()
    {
        return $this->getCastedValue();
    }

    /**
     * Prepare value for storage.
     */
    private static function prepareValue($value, bool $encrypt = false): string
    {
        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        } else {
            $value = (string) $value;
        }

        return $encrypt ? Crypt::encryptString($value) : $value;
    }

    /**
     * Determine the type of a value.
     */
    private static function determineType($value): string
    {
        return match (true) {
            is_bool($value) => 'boolean',
            is_int($value) => 'integer',
            is_float($value) => 'float',
            is_array($value) || is_object($value) => 'json',
            default => 'string',
        };
    }

    /**
     * Clear all settings cache.
     */
    public static function clearCache(): void
    {
        $keys = [
            'public',
            'general',
            'payment',
            'security',
            'email',
            'storage',
            'api',
        ];

        foreach ($keys as $key) {
            Cache::forget(self::CACHE_PREFIX . 'group_' . $key);
        }

        Cache::forget(self::CACHE_PREFIX . 'public');
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($setting) {
            // Clear relevant caches
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget(self::CACHE_PREFIX . 'group_' . $setting->group);
            
            if ($setting->is_public) {
                Cache::forget(self::CACHE_PREFIX . 'public');
            }
        });

        static::deleted(function ($setting) {
            // Clear relevant caches
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget(self::CACHE_PREFIX . 'group_' . $setting->group);
            
            if ($setting->is_public) {
                Cache::forget(self::CACHE_PREFIX . 'public');
            }
        });
    }

    /**
     * Get default system settings.
     */
    public static function getDefaults(): array
    {
        return [
            // General Settings
            'app_name' => ['value' => 'Digital Download Platform', 'type' => 'string', 'group' => 'general', 'is_public' => true],
            'app_description' => ['value' => 'Professional digital download e-commerce platform', 'type' => 'string', 'group' => 'general', 'is_public' => true],
            'app_url' => ['value' => config('app.url'), 'type' => 'string', 'group' => 'general', 'is_public' => true],
            'app_timezone' => ['value' => config('app.timezone'), 'type' => 'string', 'group' => 'general', 'is_public' => true],
            'app_currency' => ['value' => 'USD', 'type' => 'string', 'group' => 'general', 'is_public' => true],
            
            // Security Settings
            'max_login_attempts' => ['value' => 5, 'type' => 'integer', 'group' => 'security'],
            'session_timeout' => ['value' => 120, 'type' => 'integer', 'group' => 'security'],
            'download_token_expiry' => ['value' => 24, 'type' => 'integer', 'group' => 'security'],
            'max_downloads_per_token' => ['value' => 5, 'type' => 'integer', 'group' => 'security'],
            'enable_ip_blacklist' => ['value' => true, 'type' => 'boolean', 'group' => 'security'],
            
            // Payment Settings
            'default_currency' => ['value' => 'USD', 'type' => 'string', 'group' => 'payment', 'is_public' => true],
            'tax_rate' => ['value' => 0.0, 'type' => 'float', 'group' => 'payment'],
            'enable_tax' => ['value' => false, 'type' => 'boolean', 'group' => 'payment'],
            
            // Storage Settings
            'default_storage' => ['value' => 'local', 'type' => 'string', 'group' => 'storage'],
            'max_file_size' => ['value' => *********, 'type' => 'integer', 'group' => 'storage'], // 100MB
            'allowed_file_types' => ['value' => 'pdf,zip,mp3,mp4,jpg,png', 'type' => 'array', 'group' => 'storage'],
        ];
    }

    /**
     * Seed default settings.
     */
    public static function seedDefaults(): void
    {
        foreach (static::getDefaults() as $key => $config) {
            static::firstOrCreate(
                ['key' => $key],
                array_merge($config, [
                    'description' => "Default {$key} setting",
                ])
            );
        }
    }
}
