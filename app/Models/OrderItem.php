<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'quantity',
        'unit_price',
        'total_price',
        'product_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'integer',
        'unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'product_data' => 'array',
    ];

    /**
     * Get the order that owns the order item.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product that owns the order item.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the formatted unit price.
     */
    public function getFormattedUnitPriceAttribute(): string
    {
        return '$' . number_format($this->unit_price, 2);
    }

    /**
     * Get the formatted total price.
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return '$' . number_format($this->total_price, 2);
    }

    /**
     * Get the product name (from snapshot if product is deleted).
     */
    public function getProductNameAttribute(): string
    {
        return $this->attributes['product_name'] ?? 
               $this->product?->name ?? 
               'Product no longer available';
    }

    /**
     * Check if the product is still available.
     */
    public function isProductAvailable(): bool
    {
        return $this->product && $this->product->isAvailable();
    }

    /**
     * Check if this is a digital product.
     */
    public function isDigital(): bool
    {
        return $this->product?->digital ?? 
               $this->product_data['digital'] ?? 
               true; // Default to digital for this platform
    }

    /**
     * Check if this product is downloadable.
     */
    public function isDownloadable(): bool
    {
        return $this->product?->downloadable ?? 
               $this->product_data['downloadable'] ?? 
               true; // Default to downloadable for this platform
    }

    /**
     * Get the product snapshot data.
     */
    public function getProductSnapshot(): array
    {
        return $this->product_data ?? [];
    }

    /**
     * Update the total price based on quantity and unit price.
     */
    public function updateTotalPrice(): void
    {
        $this->total_price = $this->quantity * $this->unit_price;
        $this->save();
    }

    /**
     * Get item statistics.
     */
    public function getItemStats(): array
    {
        return [
            'product_name' => $this->product_name,
            'product_sku' => $this->product_sku,
            'quantity' => $this->quantity,
            'unit_price' => $this->unit_price,
            'total_price' => $this->total_price,
            'is_digital' => $this->isDigital(),
            'is_downloadable' => $this->isDownloadable(),
            'product_available' => $this->isProductAvailable(),
        ];
    }
}
