<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Wishlist extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'is_public',
        'is_default',
        'settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_public' => 'boolean',
        'is_default' => 'boolean',
        'settings' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($wishlist) {
            if (empty($wishlist->uuid)) {
                $wishlist->uuid = (string) Str::uuid();
            }
            
            // Ensure only one default wishlist per user
            if ($wishlist->is_default) {
                static::where('user_id', $wishlist->user_id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
        });

        static::updating(function ($wishlist) {
            // Ensure only one default wishlist per user
            if ($wishlist->is_default && $wishlist->isDirty('is_default')) {
                static::where('user_id', $wishlist->user_id)
                    ->where('id', '!=', $wishlist->id)
                    ->where('is_default', true)
                    ->update(['is_default' => false]);
            }
        });
    }

    /**
     * Get the user that owns the wishlist.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the wishlist items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(WishlistItem::class);
    }

    /**
     * Get the products in this wishlist.
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'wishlist_items')
            ->withPivot(['price_when_added', 'notes', 'priority', 'notify_on_sale', 'notify_on_restock', 'created_at'])
            ->withTimestamps()
            ->orderBy('wishlist_items.priority', 'desc')
            ->orderBy('wishlist_items.created_at', 'desc');
    }

    /**
     * Scope for public wishlists.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for default wishlists.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get the default wishlist for a user.
     */
    public static function getDefaultForUser(int $userId): self
    {
        $defaultWishlist = static::where('user_id', $userId)
            ->where('is_default', true)
            ->first();

        if (!$defaultWishlist) {
            $defaultWishlist = static::create([
                'user_id' => $userId,
                'name' => 'My Wishlist',
                'is_default' => true,
            ]);
        }

        return $defaultWishlist;
    }

    /**
     * Add a product to the wishlist.
     */
    public function addProduct(Product $product, array $options = []): WishlistItem
    {
        // Check if product already exists in wishlist
        $existingItem = $this->items()->where('product_id', $product->id)->first();
        
        if ($existingItem) {
            // Update existing item if needed
            $existingItem->update(array_filter([
                'notes' => $options['notes'] ?? null,
                'priority' => $options['priority'] ?? null,
                'notify_on_sale' => $options['notify_on_sale'] ?? null,
                'notify_on_restock' => $options['notify_on_restock'] ?? null,
            ]));
            
            return $existingItem;
        }

        return $this->items()->create([
            'product_id' => $product->id,
            'price_when_added' => $product->sale_price ?: $product->price,
            'notes' => $options['notes'] ?? null,
            'priority' => $options['priority'] ?? 0,
            'notify_on_sale' => $options['notify_on_sale'] ?? false,
            'notify_on_restock' => $options['notify_on_restock'] ?? false,
        ]);
    }

    /**
     * Remove a product from the wishlist.
     */
    public function removeProduct(Product $product): bool
    {
        return $this->items()->where('product_id', $product->id)->delete() > 0;
    }

    /**
     * Check if a product is in the wishlist.
     */
    public function hasProduct(Product $product): bool
    {
        return $this->items()->where('product_id', $product->id)->exists();
    }

    /**
     * Get the total value of items in the wishlist.
     */
    public function getTotalValue(): float
    {
        return $this->products->sum(function ($product) {
            return $product->sale_price ?: $product->price;
        });
    }

    /**
     * Get the total savings if all items were purchased.
     */
    public function getTotalSavings(): float
    {
        return $this->products->sum(function ($product) {
            $originalPrice = $product->pivot->price_when_added ?? $product->price;
            $currentPrice = $product->sale_price ?: $product->price;
            return max(0, $originalPrice - $currentPrice);
        });
    }

    /**
     * Get items that are currently on sale.
     */
    public function getItemsOnSale()
    {
        return $this->products->filter(function ($product) {
            return $product->sale_price && $product->sale_price < $product->price;
        });
    }

    /**
     * Get items with price drops since added.
     */
    public function getItemsWithPriceDrops()
    {
        return $this->products->filter(function ($product) {
            $priceWhenAdded = $product->pivot->price_when_added;
            $currentPrice = $product->sale_price ?: $product->price;
            return $priceWhenAdded && $currentPrice < $priceWhenAdded;
        });
    }

    /**
     * Move all items to cart (if cart system exists).
     */
    public function moveAllToCart(): array
    {
        $results = [];
        
        foreach ($this->products as $product) {
            // This would integrate with cart system when implemented
            $results[] = [
                'product_id' => $product->id,
                'success' => true, // Placeholder
                'message' => 'Added to cart'
            ];
        }

        return $results;
    }

    /**
     * Share the wishlist (generate shareable link).
     */
    public function getShareableUrl(): string
    {
        if (!$this->is_public) {
            return '';
        }

        return route('wishlists.public', $this->uuid);
    }

    /**
     * Get wishlist statistics.
     */
    public function getStats(): array
    {
        return [
            'total_items' => $this->items()->count(),
            'total_value' => $this->getTotalValue(),
            'total_savings' => $this->getTotalSavings(),
            'items_on_sale' => $this->getItemsOnSale()->count(),
            'items_with_price_drops' => $this->getItemsWithPriceDrops()->count(),
        ];
    }
}
