<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Cart extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'session_id',
        'subtotal',
        'tax_amount',
        'discount_amount',
        'total_amount',
        'currency',
        'metadata',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'metadata' => 'array',
        'expires_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cart) {
            if (empty($cart->uuid)) {
                $cart->uuid = (string) Str::uuid();
            }
            if (empty($cart->expires_at) && empty($cart->user_id)) {
                // Guest carts expire after 7 days
                $cart->expires_at = now()->addDays(7);
            }
        });
    }

    /**
     * Get the user that owns the cart.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the cart items for the cart.
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Get the total number of items in the cart.
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Check if the cart is empty.
     */
    public function isEmpty(): bool
    {
        return $this->items->isEmpty();
    }

    /**
     * Check if the cart has expired.
     */
    public function hasExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Add a product to the cart.
     */
    public function addItem(Product $product, int $quantity = 1, array $options = []): CartItem
    {
        $existingItem = $this->items()->where('product_id', $product->id)->first();

        if ($existingItem) {
            $existingItem->quantity += $quantity;
            $existingItem->total_price = $existingItem->quantity * $existingItem->unit_price;
            $existingItem->save();
            $item = $existingItem;
        } else {
            $unitPrice = $product->effective_price;
            $item = $this->items()->create([
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_sku' => $product->sku,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'total_price' => $unitPrice * $quantity,
                'product_data' => [
                    'name' => $product->name,
                    'description' => $product->short_description,
                    'price' => $product->price,
                    'sale_price' => $product->sale_price,
                    'sku' => $product->sku,
                    'image' => $product->image,
                ],
                'options' => $options,
            ]);
        }

        $this->calculateTotals();
        return $item;
    }

    /**
     * Remove an item from the cart.
     */
    public function removeItem(int $cartItemId): bool
    {
        $item = $this->items()->find($cartItemId);
        if ($item) {
            $item->delete();
            $this->calculateTotals();
            return true;
        }
        return false;
    }

    /**
     * Update item quantity.
     */
    public function updateItemQuantity(int $cartItemId, int $quantity): bool
    {
        $item = $this->items()->find($cartItemId);
        if ($item && $quantity > 0) {
            $item->quantity = $quantity;
            $item->total_price = $item->quantity * $item->unit_price;
            $item->save();
            $this->calculateTotals();
            return true;
        }
        return false;
    }

    /**
     * Clear all items from the cart.
     */
    public function clear(): void
    {
        $this->items()->delete();
        $this->calculateTotals();
    }

    /**
     * Calculate cart totals.
     */
    public function calculateTotals(): void
    {
        $this->load('items');

        $this->subtotal = $this->items->sum('total_price');
        $this->tax_amount = $this->calculateTax();
        $this->discount_amount = $this->calculateDiscount();
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;

        $this->save();
    }

    /**
     * Calculate tax amount (placeholder - implement based on business rules).
     */
    protected function calculateTax(): float
    {
        // TODO: Implement tax calculation based on business rules
        return 0.00;
    }

    /**
     * Calculate discount amount (placeholder - implement based on business rules).
     */
    protected function calculateDiscount(): float
    {
        // TODO: Implement discount calculation based on coupons/promotions
        return 0.00;
    }

    /**
     * Convert cart to order.
     */
    public function convertToOrder(array $billingDetails = []): Order
    {
        $order = Order::create([
            'user_id' => $this->user_id,
            'subtotal' => $this->subtotal,
            'tax_amount' => $this->tax_amount,
            'discount_amount' => $this->discount_amount,
            'total_amount' => $this->total_amount,
            'currency' => $this->currency,
            'billing_details' => $billingDetails,
            'status' => 'pending',
            'payment_status' => 'pending',
        ]);

        foreach ($this->items as $cartItem) {
            $order->items()->create([
                'product_id' => $cartItem->product_id,
                'product_name' => $cartItem->product_name,
                'product_sku' => $cartItem->product_sku,
                'quantity' => $cartItem->quantity,
                'unit_price' => $cartItem->unit_price,
                'total_price' => $cartItem->total_price,
                'product_data' => $cartItem->product_data,
            ]);
        }

        return $order;
    }

    /**
     * Scope to get active carts (not expired).
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope to get expired carts.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get carts by user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get carts by session.
     */
    public function scopeForSession($query, $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }
}
