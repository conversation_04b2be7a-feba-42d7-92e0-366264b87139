<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SecurityEvent extends Model
{
    use HasFactory;

    /**
     * Disable updated_at timestamp since security events are immutable.
     */
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_type',
        'severity',
        'user_id',
        'ip_address',
        'user_agent',
        'file_id',
        'order_id',
        'event_data',
        'message',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'event_data' => 'array',
    ];

    /**
     * Security event types constants.
     */
    const EVENT_TYPES = [
        'LOGIN_ATTEMPT' => 'login_attempt',
        'LOGIN_SUCCESS' => 'login_success',
        'LOGIN_FAILED' => 'login_failed',
        'LOGOUT' => 'logout',
        'PASSWORD_RESET' => 'password_reset',
        'ACCOUNT_LOCKED' => 'account_locked',
        'DOWNLOAD_ATTEMPT' => 'download_attempt',
        'DOWNLOAD_SUCCESS' => 'download_success',
        'DOWNLOAD_FAILED' => 'download_failed',
        'INVALID_TOKEN' => 'invalid_token',
        'EXPIRED_TOKEN' => 'expired_token',
        'IP_VIOLATION' => 'ip_violation',
        'RATE_LIMIT_EXCEEDED' => 'rate_limit_exceeded',
        'SUSPICIOUS_ACTIVITY' => 'suspicious_activity',
        'UNAUTHORIZED_ACCESS' => 'unauthorized_access',
        'FILE_ACCESS_DENIED' => 'file_access_denied',
        'PAYMENT_FRAUD' => 'payment_fraud',
        'ACCOUNT_COMPROMISE' => 'account_compromise',
    ];

    /**
     * Severity levels constants.
     */
    const SEVERITY_LEVELS = [
        'LOW' => 'low',
        'MEDIUM' => 'medium',
        'HIGH' => 'high',
        'CRITICAL' => 'critical',
    ];

    /**
     * Get the user that owns the security event.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the file associated with the security event.
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class);
    }

    /**
     * Get the order associated with the security event.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope to get events by type.
     */
    public function scopeEventType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    /**
     * Scope to get events by severity.
     */
    public function scopeSeverity($query, $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope to get events by IP address.
     */
    public function scopeByIp($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope to get events for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get recent events.
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope to get critical events.
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    /**
     * Scope to get high severity events.
     */
    public function scopeHighSeverity($query)
    {
        return $query->whereIn('severity', ['high', 'critical']);
    }

    /**
     * Log a security event.
     */
    public static function logEvent(
        string $eventType,
        string $message,
        string $severity = 'medium',
        ?User $user = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?File $file = null,
        ?Order $order = null,
        array $eventData = []
    ): self {
        return static::create([
            'event_type' => $eventType,
            'severity' => $severity,
            'user_id' => $user?->id,
            'ip_address' => $ipAddress ?? request()->ip(),
            'user_agent' => $userAgent ?? request()->userAgent(),
            'file_id' => $file?->id,
            'order_id' => $order?->id,
            'event_data' => $eventData,
            'message' => $message,
        ]);
    }

    /**
     * Log a login attempt.
     */
    public static function logLoginAttempt(string $email, bool $success, ?User $user = null): self
    {
        return static::logEvent(
            $success ? self::EVENT_TYPES['LOGIN_SUCCESS'] : self::EVENT_TYPES['LOGIN_FAILED'],
            $success ? "User logged in successfully: {$email}" : "Failed login attempt for: {$email}",
            $success ? 'low' : 'medium',
            $user,
            eventData: ['email' => $email]
        );
    }

    /**
     * Log a download attempt.
     */
    public static function logDownloadAttempt(File $file, bool $success, ?User $user = null, ?string $reason = null): self
    {
        return static::logEvent(
            $success ? self::EVENT_TYPES['DOWNLOAD_SUCCESS'] : self::EVENT_TYPES['DOWNLOAD_FAILED'],
            $success 
                ? "File downloaded successfully: {$file->original_name}" 
                : "Download failed for file: {$file->original_name}" . ($reason ? " - {$reason}" : ''),
            $success ? 'low' : 'medium',
            $user,
            file: $file,
            eventData: [
                'file_name' => $file->original_name,
                'file_size' => $file->file_size,
                'storage_type' => $file->storage_type,
                'reason' => $reason,
            ]
        );
    }

    /**
     * Log an IP violation.
     */
    public static function logIpViolation(string $violationType, string $ipAddress, ?User $user = null, array $details = []): self
    {
        return static::logEvent(
            self::EVENT_TYPES['IP_VIOLATION'],
            "IP violation detected: {$violationType} from {$ipAddress}",
            'high',
            $user,
            $ipAddress,
            eventData: array_merge(['violation_type' => $violationType], $details)
        );
    }

    /**
     * Log suspicious activity.
     */
    public static function logSuspiciousActivity(string $activity, string $severity = 'high', ?User $user = null, array $details = []): self
    {
        return static::logEvent(
            self::EVENT_TYPES['SUSPICIOUS_ACTIVITY'],
            "Suspicious activity detected: {$activity}",
            $severity,
            $user,
            eventData: $details
        );
    }

    /**
     * Log unauthorized access attempt.
     */
    public static function logUnauthorizedAccess(string $resource, ?User $user = null): self
    {
        return static::logEvent(
            self::EVENT_TYPES['UNAUTHORIZED_ACCESS'],
            "Unauthorized access attempt to: {$resource}",
            'high',
            $user,
            eventData: ['resource' => $resource]
        );
    }

    /**
     * Get event statistics.
     */
    public static function getEventStats(int $hours = 24): array
    {
        $events = static::recent($hours)->get();

        return [
            'total_events' => $events->count(),
            'by_severity' => $events->groupBy('severity')->map->count(),
            'by_type' => $events->groupBy('event_type')->map->count(),
            'unique_ips' => $events->pluck('ip_address')->unique()->count(),
            'unique_users' => $events->whereNotNull('user_id')->pluck('user_id')->unique()->count(),
            'critical_events' => $events->where('severity', 'critical')->count(),
            'high_severity_events' => $events->whereIn('severity', ['high', 'critical'])->count(),
        ];
    }

    /**
     * Check if this is a critical event.
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Check if this is a high severity event.
     */
    public function isHighSeverity(): bool
    {
        return in_array($this->severity, ['high', 'critical']);
    }

    /**
     * Get formatted event data.
     */
    public function getFormattedEventData(): string
    {
        if (empty($this->event_data)) {
            return 'No additional data';
        }

        return collect($this->event_data)
            ->map(fn($value, $key) => "{$key}: {$value}")
            ->implode(', ');
    }
}
