<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class SearchAnalytics extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'query',
        'normalized_query',
        'result_count',
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'referer',
        'filters_applied',
        'sort_by',
        'sort_order',
        'page',
        'per_page',
        'search_duration',
        'had_results',
        'clicked_result',
        'clicked_position',
        'clicked_product_id',
        'clicked_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'result_count' => 'integer',
        'filters_applied' => 'array',
        'page' => 'integer',
        'per_page' => 'integer',
        'search_duration' => 'decimal:3',
        'had_results' => 'boolean',
        'clicked_result' => 'boolean',
        'clicked_position' => 'integer',
        'clicked_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($analytics) {
            if (empty($analytics->normalized_query)) {
                $analytics->normalized_query = static::normalizeQuery($analytics->query);
            }
        });
    }

    /**
     * Get the user that performed the search.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product that was clicked.
     */
    public function clickedProduct(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'clicked_product_id');
    }

    /**
     * Normalize a search query for better analytics.
     */
    public static function normalizeQuery(string $query): string
    {
        // Convert to lowercase
        $normalized = strtolower(trim($query));
        
        // Remove extra spaces
        $normalized = preg_replace('/\s+/', ' ', $normalized);
        
        // Remove common stop words (basic implementation)
        $stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        $words = explode(' ', $normalized);
        $words = array_filter($words, function($word) use ($stopWords) {
            return !in_array($word, $stopWords) && strlen($word) > 1;
        });
        
        return implode(' ', $words);
    }

    /**
     * Record a search event.
     */
    public static function recordSearch(array $data): self
    {
        return static::create([
            'query' => $data['query'],
            'result_count' => $data['result_count'] ?? 0,
            'user_id' => $data['user_id'] ?? null,
            'session_id' => $data['session_id'] ?? null,
            'ip_address' => $data['ip_address'] ?? null,
            'user_agent' => $data['user_agent'] ?? null,
            'referer' => $data['referer'] ?? null,
            'filters_applied' => $data['filters_applied'] ?? null,
            'sort_by' => $data['sort_by'] ?? null,
            'sort_order' => $data['sort_order'] ?? null,
            'page' => $data['page'] ?? 1,
            'per_page' => $data['per_page'] ?? 12,
            'search_duration' => $data['search_duration'] ?? null,
            'had_results' => ($data['result_count'] ?? 0) > 0,
        ]);
    }

    /**
     * Record a click event.
     */
    public function recordClick(int $productId, int $position): void
    {
        $this->update([
            'clicked_result' => true,
            'clicked_product_id' => $productId,
            'clicked_position' => $position,
            'clicked_at' => now(),
        ]);
    }

    /**
     * Get popular search queries.
     */
    public static function getPopularQueries(int $days = 30, int $limit = 20): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->where('had_results', true)
            ->groupBy('normalized_query')
            ->selectRaw('normalized_query, COUNT(*) as search_count, AVG(result_count) as avg_results')
            ->orderBy('search_count', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get queries with no results.
     */
    public static function getNoResultQueries(int $days = 30, int $limit = 20): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->where('had_results', false)
            ->groupBy('normalized_query')
            ->selectRaw('normalized_query, COUNT(*) as search_count')
            ->orderBy('search_count', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get search trends over time.
     */
    public static function getSearchTrends(int $days = 30): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as total_searches, COUNT(CASE WHEN had_results THEN 1 END) as successful_searches')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get click-through rates.
     */
    public static function getClickThroughRates(int $days = 30): array
    {
        $stats = static::where('created_at', '>=', now()->subDays($days))
            ->where('had_results', true)
            ->selectRaw('
                COUNT(*) as total_searches,
                COUNT(CASE WHEN clicked_result THEN 1 END) as clicked_searches,
                AVG(clicked_position) as avg_click_position
            ')
            ->first();

        $ctr = $stats->total_searches > 0 ? ($stats->clicked_searches / $stats->total_searches) * 100 : 0;

        return [
            'total_searches' => $stats->total_searches,
            'clicked_searches' => $stats->clicked_searches,
            'click_through_rate' => round($ctr, 2),
            'avg_click_position' => round($stats->avg_click_position ?? 0, 1),
        ];
    }

    /**
     * Get most clicked products from search.
     */
    public static function getMostClickedProducts(int $days = 30, int $limit = 10): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->where('clicked_result', true)
            ->with('clickedProduct:id,name,slug,price,sale_price,image')
            ->groupBy('clicked_product_id')
            ->selectRaw('clicked_product_id, COUNT(*) as click_count')
            ->orderBy('click_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'product' => $item->clickedProduct,
                    'click_count' => $item->click_count,
                ];
            })
            ->toArray();
    }

    /**
     * Get search performance metrics.
     */
    public static function getPerformanceMetrics(int $days = 30): array
    {
        $metrics = static::where('created_at', '>=', now()->subDays($days))
            ->selectRaw('
                COUNT(*) as total_searches,
                COUNT(CASE WHEN had_results THEN 1 END) as successful_searches,
                COUNT(CASE WHEN clicked_result THEN 1 END) as clicked_searches,
                AVG(search_duration) as avg_search_duration,
                AVG(result_count) as avg_result_count
            ')
            ->first();

        $successRate = $metrics->total_searches > 0 ? ($metrics->successful_searches / $metrics->total_searches) * 100 : 0;
        $ctr = $metrics->successful_searches > 0 ? ($metrics->clicked_searches / $metrics->successful_searches) * 100 : 0;

        return [
            'total_searches' => $metrics->total_searches,
            'successful_searches' => $metrics->successful_searches,
            'success_rate' => round($successRate, 2),
            'clicked_searches' => $metrics->clicked_searches,
            'click_through_rate' => round($ctr, 2),
            'avg_search_duration' => round($metrics->avg_search_duration ?? 0, 3),
            'avg_result_count' => round($metrics->avg_result_count ?? 0, 1),
        ];
    }

    /**
     * Get filter usage statistics.
     */
    public static function getFilterUsage(int $days = 30): array
    {
        $searches = static::where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('filters_applied')
            ->pluck('filters_applied');

        $filterStats = [];
        foreach ($searches as $filters) {
            if (is_array($filters)) {
                foreach ($filters as $key => $value) {
                    if (!isset($filterStats[$key])) {
                        $filterStats[$key] = 0;
                    }
                    $filterStats[$key]++;
                }
            }
        }

        arsort($filterStats);
        return $filterStats;
    }

    /**
     * Scope for searches with results.
     */
    public function scopeWithResults($query)
    {
        return $query->where('had_results', true);
    }

    /**
     * Scope for searches without results.
     */
    public function scopeWithoutResults($query)
    {
        return $query->where('had_results', false);
    }

    /**
     * Scope for clicked searches.
     */
    public function scopeClicked($query)
    {
        return $query->where('clicked_result', true);
    }

    /**
     * Scope for recent searches.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
}
