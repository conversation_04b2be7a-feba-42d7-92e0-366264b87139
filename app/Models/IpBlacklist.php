<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IpBlacklist extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ip_address',
        'reason',
        'is_active',
        'expires_at',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user who created the blacklist entry.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get active blacklist entries.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope to get expired blacklist entries.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now())
                    ->where('is_active', true);
    }

    /**
     * Scope to get permanent blacklist entries.
     */
    public function scopePermanent($query)
    {
        return $query->whereNull('expires_at')
                    ->where('is_active', true);
    }

    /**
     * Scope to get temporary blacklist entries.
     */
    public function scopeTemporary($query)
    {
        return $query->whereNotNull('expires_at')
                    ->where('is_active', true);
    }

    /**
     * Check if an IP address is blacklisted.
     */
    public static function isBlacklisted(string $ipAddress): bool
    {
        return static::active()
                    ->where('ip_address', $ipAddress)
                    ->exists();
    }

    /**
     * Blacklist an IP address.
     */
    public static function blacklistIp(
        string $ipAddress,
        string $reason,
        ?\DateTime $expiresAt = null,
        ?User $createdBy = null
    ): self {
        // Check if IP is already blacklisted
        $existing = static::where('ip_address', $ipAddress)
                         ->where('is_active', true)
                         ->first();

        if ($existing) {
            // Update existing entry
            $existing->update([
                'reason' => $reason,
                'expires_at' => $expiresAt,
                'created_by' => $createdBy?->id,
            ]);

            return $existing;
        }

        // Create new blacklist entry
        $blacklist = static::create([
            'ip_address' => $ipAddress,
            'reason' => $reason,
            'is_active' => true,
            'expires_at' => $expiresAt,
            'created_by' => $createdBy?->id,
        ]);

        // Log security event
        SecurityEvent::logEvent(
            SecurityEvent::EVENT_TYPES['IP_VIOLATION'],
            "IP address blacklisted: {$ipAddress} - {$reason}",
            'high',
            $createdBy,
            $ipAddress,
            eventData: [
                'action' => 'blacklisted',
                'reason' => $reason,
                'expires_at' => $expiresAt?->toISOString(),
                'permanent' => is_null($expiresAt),
            ]
        );

        return $blacklist;
    }

    /**
     * Remove an IP from blacklist.
     */
    public static function removeFromBlacklist(string $ipAddress, ?User $removedBy = null): bool
    {
        $updated = static::where('ip_address', $ipAddress)
                        ->where('is_active', true)
                        ->update(['is_active' => false]);

        if ($updated > 0) {
            // Log security event
            SecurityEvent::logEvent(
                SecurityEvent::EVENT_TYPES['IP_VIOLATION'],
                "IP address removed from blacklist: {$ipAddress}",
                'medium',
                $removedBy,
                $ipAddress,
                eventData: [
                    'action' => 'removed_from_blacklist',
                    'removed_by' => $removedBy?->id,
                ]
            );

            return true;
        }

        return false;
    }

    /**
     * Clean up expired blacklist entries.
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->update(['is_active' => false]);
    }

    /**
     * Check if this blacklist entry is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if this is a permanent blacklist.
     */
    public function isPermanent(): bool
    {
        return is_null($this->expires_at);
    }

    /**
     * Get time remaining until expiration.
     */
    public function getTimeRemainingAttribute(): ?string
    {
        if ($this->isPermanent()) {
            return 'Permanent';
        }

        if ($this->isExpired()) {
            return 'Expired';
        }

        return $this->expires_at->diffForHumans();
    }

    /**
     * Extend the blacklist expiration.
     */
    public function extend(\DateTime $newExpiresAt): void
    {
        $this->update(['expires_at' => $newExpiresAt]);

        SecurityEvent::logEvent(
            SecurityEvent::EVENT_TYPES['IP_VIOLATION'],
            "IP blacklist extended: {$this->ip_address}",
            'medium',
            null,
            $this->ip_address,
            eventData: [
                'action' => 'extended',
                'new_expires_at' => $newExpiresAt->toISOString(),
            ]
        );
    }

    /**
     * Make the blacklist permanent.
     */
    public function makePermanent(): void
    {
        $this->update(['expires_at' => null]);

        SecurityEvent::logEvent(
            SecurityEvent::EVENT_TYPES['IP_VIOLATION'],
            "IP blacklist made permanent: {$this->ip_address}",
            'high',
            null,
            $this->ip_address,
            eventData: [
                'action' => 'made_permanent',
            ]
        );
    }

    /**
     * Get blacklist statistics.
     */
    public static function getBlacklistStats(): array
    {
        $total = static::where('is_active', true)->count();
        $permanent = static::permanent()->count();
        $temporary = static::temporary()->count();
        $expired = static::expired()->count();

        return [
            'total_active' => $total,
            'permanent' => $permanent,
            'temporary' => $temporary,
            'expired' => $expired,
            'recent_additions' => static::where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }
}
