<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class PaymentTransaction extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'user_id',
        'gateway',
        'gateway_transaction_id',
        'transaction_type',
        'status',
        'amount',
        'currency',
        'gateway_response',
        'failure_reason',
        'processed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'gateway_response' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (empty($transaction->uuid)) {
                $transaction->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the order that owns the payment transaction.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user that owns the payment transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get transactions by status.
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get transactions by gateway.
     */
    public function scopeGateway($query, $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Scope to get completed transactions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get failed transactions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope to get payment transactions (not refunds).
     */
    public function scopePayments($query)
    {
        return $query->where('transaction_type', 'payment');
    }

    /**
     * Scope to get refund transactions.
     */
    public function scopeRefunds($query)
    {
        return $query->whereIn('transaction_type', ['refund', 'partial_refund']);
    }

    /**
     * Check if the transaction is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the transaction is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the transaction is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if this is a payment transaction.
     */
    public function isPayment(): bool
    {
        return $this->transaction_type === 'payment';
    }

    /**
     * Check if this is a refund transaction.
     */
    public function isRefund(): bool
    {
        return in_array($this->transaction_type, ['refund', 'partial_refund']);
    }

    /**
     * Get the formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Mark the transaction as completed.
     */
    public function markAsCompleted(array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'completed',
            'gateway_response' => array_merge($this->gateway_response ?? [], $gatewayResponse),
            'processed_at' => now(),
        ]);

        // Update the order payment status if this is a payment
        if ($this->isPayment() && $this->order) {
            $this->order->markAsPaid($this->gateway, $this->gateway_transaction_id);
        }
    }

    /**
     * Mark the transaction as failed.
     */
    public function markAsFailed(string $reason, array $gatewayResponse = []): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
            'gateway_response' => array_merge($this->gateway_response ?? [], $gatewayResponse),
            'processed_at' => now(),
        ]);
    }

    /**
     * Create a refund transaction for this payment.
     */
    public function createRefund(float $amount = null, string $reason = null): PaymentTransaction
    {
        $refundAmount = $amount ?? $this->amount;
        $transactionType = $refundAmount < $this->amount ? 'partial_refund' : 'refund';

        return static::create([
            'order_id' => $this->order_id,
            'user_id' => $this->user_id,
            'gateway' => $this->gateway,
            'transaction_type' => $transactionType,
            'status' => 'pending',
            'amount' => $refundAmount,
            'currency' => $this->currency,
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Get transaction statistics.
     */
    public function getTransactionStats(): array
    {
        return [
            'gateway' => $this->gateway,
            'transaction_type' => $this->transaction_type,
            'status' => $this->status,
            'amount' => $this->amount,
            'currency' => $this->currency,
            'created_at' => $this->created_at,
            'processed_at' => $this->processed_at,
            'is_completed' => $this->isCompleted(),
            'is_failed' => $this->isFailed(),
            'is_payment' => $this->isPayment(),
            'is_refund' => $this->isRefund(),
        ];
    }

    /**
     * Get gateway response data safely.
     */
    public function getGatewayResponseData(string $key = null)
    {
        if ($key) {
            return $this->gateway_response[$key] ?? null;
        }

        return $this->gateway_response ?? [];
    }
}
