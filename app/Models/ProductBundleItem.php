<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductBundleItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'bundle_id',
        'product_id',
        'quantity',
        'price_override',
        'sort_order',
        'is_required',
        'is_optional',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'quantity' => 'integer',
        'price_override' => 'decimal:2',
        'sort_order' => 'integer',
        'is_required' => 'boolean',
        'is_optional' => 'boolean',
    ];

    /**
     * Get the bundle that owns the item.
     */
    public function bundle(): BelongsTo
    {
        return $this->belongsTo(ProductBundle::class, 'bundle_id');
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the effective price for this item.
     */
    public function getEffectivePrice(): float
    {
        if ($this->price_override !== null) {
            return $this->price_override;
        }

        return $this->product->sale_price ?? $this->product->price;
    }

    /**
     * Get the total price for this item (price * quantity).
     */
    public function getTotalPrice(): float
    {
        return $this->getEffectivePrice() * $this->quantity;
    }

    /**
     * Get the original total price (without bundle discount).
     */
    public function getOriginalTotalPrice(): float
    {
        $originalPrice = $this->product->price;
        return $originalPrice * $this->quantity;
    }

    /**
     * Get the savings for this item.
     */
    public function getSavings(): float
    {
        return $this->getOriginalTotalPrice() - $this->getTotalPrice();
    }

    /**
     * Check if this item is available.
     */
    public function isAvailable(): bool
    {
        return $this->product->isAvailable();
    }

    /**
     * Scope for required items.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for optional items.
     */
    public function scopeOptional($query)
    {
        return $query->where('is_optional', true);
    }

    /**
     * Get formatted effective price.
     */
    public function getFormattedEffectivePriceAttribute(): string
    {
        return '$' . number_format($this->getEffectivePrice(), 2);
    }

    /**
     * Get formatted total price.
     */
    public function getFormattedTotalPriceAttribute(): string
    {
        return '$' . number_format($this->getTotalPrice(), 2);
    }

    /**
     * Get formatted original total price.
     */
    public function getFormattedOriginalTotalPriceAttribute(): string
    {
        return '$' . number_format($this->getOriginalTotalPrice(), 2);
    }

    /**
     * Get formatted savings.
     */
    public function getFormattedSavingsAttribute(): string
    {
        return '$' . number_format($this->getSavings(), 2);
    }
}
