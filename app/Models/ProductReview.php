<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ProductReview extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'user_id',
        'order_id',
        'title',
        'content',
        'rating',
        'is_verified_purchase',
        'is_approved',
        'is_featured',
        'metadata',
        'status',
        'approved_at',
        'approved_by',
        'admin_notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating' => 'integer',
        'is_verified_purchase' => 'boolean',
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'metadata' => 'array',
        'approved_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($review) {
            if (empty($review->uuid)) {
                $review->uuid = (string) Str::uuid();
            }
            
            // Check if user has purchased this product
            if ($review->user_id && $review->product_id) {
                $hasPurchased = OrderItem::whereHas('order', function ($query) use ($review) {
                    $query->where('user_id', $review->user_id)
                          ->where('status', 'completed');
                })->where('product_id', $review->product_id)->exists();
                
                $review->is_verified_purchase = $hasPurchased;
            }
        });

        static::created(function ($review) {
            $review->updateProductRatingStats();
        });

        static::updated(function ($review) {
            if ($review->wasChanged(['rating', 'is_approved'])) {
                $review->updateProductRatingStats();
            }
        });

        static::deleted(function ($review) {
            $review->updateProductRatingStats();
        });
    }

    /**
     * Get the product that owns the review.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who wrote the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order associated with this review.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user who approved the review.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the helpful votes for this review.
     */
    public function helpfulVotes(): HasMany
    {
        return $this->hasMany(ReviewHelpfulVote::class, 'review_id');
    }

    /**
     * Scope for approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope for verified purchase reviews.
     */
    public function scopeVerifiedPurchase($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Scope for featured reviews.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for reviews by rating.
     */
    public function scopeByRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Get helpful votes count.
     */
    public function getHelpfulVotesCountAttribute()
    {
        return $this->helpfulVotes()->where('is_helpful', true)->count();
    }

    /**
     * Get not helpful votes count.
     */
    public function getNotHelpfulVotesCountAttribute()
    {
        return $this->helpfulVotes()->where('is_helpful', false)->count();
    }

    /**
     * Check if user found this review helpful.
     */
    public function isHelpfulForUser($userId)
    {
        return $this->helpfulVotes()
            ->where('user_id', $userId)
            ->where('is_helpful', true)
            ->exists();
    }

    /**
     * Approve the review.
     */
    public function approve($approvedBy = null)
    {
        $this->update([
            'is_approved' => true,
            'status' => 'approved',
            'approved_at' => now(),
            'approved_by' => $approvedBy ?: auth()->id(),
        ]);
    }

    /**
     * Reject the review.
     */
    public function reject($adminNotes = null)
    {
        $this->update([
            'is_approved' => false,
            'status' => 'rejected',
            'admin_notes' => $adminNotes,
        ]);
    }

    /**
     * Update product rating statistics.
     */
    public function updateProductRatingStats()
    {
        $product = $this->product;
        if (!$product) return;

        $ratingService = app(\App\Services\ProductRatingService::class);
        $ratingService->updateProductRatingStats($product);
    }

    /**
     * Get star rating as HTML.
     */
    public function getStarRatingHtml()
    {
        $fullStars = floor($this->rating);
        $emptyStars = 5 - $fullStars;
        
        $html = str_repeat('★', $fullStars) . str_repeat('☆', $emptyStars);
        
        return $html;
    }
}
