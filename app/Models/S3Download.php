<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class S3Download extends Model
{
    use HasFactory;

    /**
     * Disable updated_at timestamp since downloads are immutable events.
     */
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'file_id',
        'user_id',
        'order_id',
        'download_token',
        'ip_address',
        'user_agent',
        'device_fingerprint',
        's3_key',
        's3_bucket',
        'presigned_url_expires_at',
        'download_count',
        'file_size',
        'download_duration',
        'is_active',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'presigned_url_expires_at' => 'datetime',
        'download_count' => 'integer',
        'file_size' => 'integer',
        'download_duration' => 'integer',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the file that was downloaded.
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class);
    }

    /**
     * Get the user who downloaded the file.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order associated with the download.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope to get downloads for a specific file.
     */
    public function scopeForFile($query, $fileId)
    {
        return $query->where('file_id', $fileId);
    }

    /**
     * Scope to get downloads for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get downloads by IP address.
     */
    public function scopeByIp($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope to get downloads from a specific S3 bucket.
     */
    public function scopeFromBucket($query, $bucket)
    {
        return $query->where('s3_bucket', $bucket);
    }

    /**
     * Scope to get recent downloads.
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope to get active downloads.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Record an S3 download event.
     */
    public static function recordDownload(
        File $file,
        ?User $user = null,
        ?Order $order = null,
        ?string $downloadToken = null,
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?int $downloadDuration = null
    ): self {
        $download = static::create([
            'file_id' => $file->id,
            'user_id' => $user?->id,
            'order_id' => $order?->id,
            'download_token' => $downloadToken,
            'ip_address' => $ipAddress ?? request()->ip(),
            'user_agent' => $userAgent ?? request()->userAgent(),
            'device_fingerprint' => static::generateDeviceFingerprint(),
            's3_key' => $file->s3_key,
            's3_bucket' => $file->s3_bucket,
            'presigned_url_expires_at' => now()->addHours(1), // Default 1 hour
            'download_count' => 1,
            'file_size' => $file->file_size,
            'download_duration' => $downloadDuration,
            'is_active' => true,
            'expires_at' => now()->addDays(1), // Keep record for 1 day
        ]);

        // Log security event
        SecurityEvent::logDownloadAttempt($file, true, $user);

        // Update file download count
        $file->incrementDownloadCount();

        return $download;
    }

    /**
     * Get download analytics for a file.
     */
    public static function getFileAnalytics(int $fileId, int $days = 30): array
    {
        $downloads = static::forFile($fileId)
                          ->where('created_at', '>=', now()->subDays($days))
                          ->get();

        return [
            'total_downloads' => $downloads->count(),
            'unique_users' => $downloads->whereNotNull('user_id')->pluck('user_id')->unique()->count(),
            'unique_ips' => $downloads->pluck('ip_address')->unique()->count(),
            'total_bandwidth' => $downloads->sum('file_size'),
            'average_duration' => $downloads->whereNotNull('download_duration')->avg('download_duration'),
            'downloads_by_day' => $downloads->groupBy(function ($download) {
                return $download->created_at->format('Y-m-d');
            })->map->count(),
            'top_countries' => $downloads->groupBy('ip_address')->map->count()->sortDesc()->take(10),
        ];
    }

    /**
     * Get user download analytics.
     */
    public static function getUserAnalytics(int $userId, int $days = 30): array
    {
        $downloads = static::forUser($userId)
                          ->where('created_at', '>=', now()->subDays($days))
                          ->get();

        return [
            'total_downloads' => $downloads->count(),
            'unique_files' => $downloads->pluck('file_id')->unique()->count(),
            'total_bandwidth' => $downloads->sum('file_size'),
            'average_duration' => $downloads->whereNotNull('download_duration')->avg('download_duration'),
            'most_downloaded_files' => $downloads->groupBy('file_id')->map->count()->sortDesc()->take(5),
            'download_pattern' => $downloads->groupBy(function ($download) {
                return $download->created_at->format('H');
            })->map->count(),
        ];
    }

    /**
     * Get S3 bucket analytics.
     */
    public static function getBucketAnalytics(string $bucket, int $days = 30): array
    {
        $downloads = static::fromBucket($bucket)
                          ->where('created_at', '>=', now()->subDays($days))
                          ->get();

        return [
            'total_downloads' => $downloads->count(),
            'unique_files' => $downloads->pluck('file_id')->unique()->count(),
            'total_bandwidth' => $downloads->sum('file_size'),
            'unique_users' => $downloads->whereNotNull('user_id')->pluck('user_id')->unique()->count(),
            'unique_ips' => $downloads->pluck('ip_address')->unique()->count(),
            'downloads_by_day' => $downloads->groupBy(function ($download) {
                return $download->created_at->format('Y-m-d');
            })->map->count(),
        ];
    }

    /**
     * Detect suspicious download patterns.
     */
    public static function detectSuspiciousActivity(int $hours = 1): array
    {
        $suspicious = [];

        // Check for rapid downloads from same IP
        $rapidDownloads = static::recent($hours)
                               ->selectRaw('ip_address, COUNT(*) as download_count')
                               ->groupBy('ip_address')
                               ->having('download_count', '>', 10)
                               ->get();

        foreach ($rapidDownloads as $rapid) {
            $suspicious[] = [
                'type' => 'rapid_downloads',
                'ip_address' => $rapid->ip_address,
                'count' => $rapid->download_count,
                'severity' => 'high',
            ];

            // Record violation
            IpViolation::recordViolation(
                $rapid->ip_address,
                IpViolation::VIOLATION_TYPES['SUSPICIOUS_DOWNLOAD_PATTERN'],
                null,
                null,
                ['rapid_downloads' => $rapid->download_count, 'timeframe' => "{$hours} hours"]
            );
        }

        // Check for downloads without valid tokens
        $invalidTokenDownloads = static::recent($hours)
                                      ->whereNull('download_token')
                                      ->whereNotNull('user_id')
                                      ->count();

        if ($invalidTokenDownloads > 5) {
            $suspicious[] = [
                'type' => 'invalid_token_usage',
                'count' => $invalidTokenDownloads,
                'severity' => 'medium',
            ];
        }

        return $suspicious;
    }

    /**
     * Generate device fingerprint.
     */
    private static function generateDeviceFingerprint(): string
    {
        $components = [
            request()->ip(),
            request()->userAgent(),
            request()->header('Accept-Language'),
            request()->header('Accept-Encoding'),
        ];

        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get formatted download duration.
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->download_duration) {
            return 'Unknown';
        }

        if ($this->download_duration < 60) {
            return $this->download_duration . ' seconds';
        }

        $minutes = floor($this->download_duration / 60);
        $seconds = $this->download_duration % 60;

        return "{$minutes}m {$seconds}s";
    }

    /**
     * Clean up old download records.
     */
    public static function cleanupOldRecords(int $days = 30): int
    {
        return static::where('created_at', '<', now()->subDays($days))->delete();
    }
}
