<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class DigitalAsset extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'product_id',
        'original_name',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_hash',
        'storage_disk',
        'download_count',
        'download_limit',
        'is_active',
        'metadata',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'download_count' => 'integer',
        'download_limit' => 'integer',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    protected $attributes = [
        'download_count' => 0,
        'is_active' => true,
        'storage_disk' => 'digital_assets',
    ];

    /**
     * Get the product that owns the digital asset.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the download logs for this asset.
     */
    public function downloadLogs(): HasMany
    {
        return $this->hasMany(DownloadLog::class);
    }

    /**
     * Scope to get only active assets.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get assets by product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        if ($bytes === 0) return '0 Bytes';
        
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes) / log($k));
        
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Get file extension.
     */
    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->original_name, PATHINFO_EXTENSION);
    }

    /**
     * Check if asset can be downloaded.
     */
    public function canBeDownloaded(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->download_limit && $this->download_count >= $this->download_limit) {
            return false;
        }

        return true;
    }

    /**
     * Increment download count.
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * Get the storage disk instance.
     */
    public function getStorageDisk()
    {
        return \Storage::disk($this->storage_disk);
    }

    /**
     * Check if file exists in storage.
     */
    public function fileExists(): bool
    {
        return $this->getStorageDisk()->exists($this->file_path);
    }

    /**
     * Get file URL (for public files).
     */
    public function getFileUrl(): ?string
    {
        if (!$this->fileExists()) {
            return null;
        }

        return $this->getStorageDisk()->url($this->file_path);
    }

    /**
     * Get temporary download URL.
     */
    public function getTemporaryUrl(int $expiresInMinutes = 60): string
    {
        $expiresAt = now()->addMinutes($expiresInMinutes);
        
        return $this->getStorageDisk()->temporaryUrl(
            $this->file_path,
            $expiresAt
        );
    }

    /**
     * Delete file from storage.
     */
    public function deleteFile(): bool
    {
        if ($this->fileExists()) {
            return $this->getStorageDisk()->delete($this->file_path);
        }

        return true;
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($asset) {
            // Delete the physical file when the model is deleted
            $asset->deleteFile();
        });
    }
}
