<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class ProductView extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'referer',
        'source',
        'view_duration',
        'bounced',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'view_duration' => 'integer',
        'bounced' => 'boolean',
    ];

    /**
     * Get the product that was viewed.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who viewed the product.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Record a product view.
     */
    public static function recordView(int $productId, array $data = []): self
    {
        // Check if this user/session already viewed this product recently (within 1 hour)
        $recentView = static::where('product_id', $productId)
            ->where(function ($query) {
                if (Auth::check()) {
                    $query->where('user_id', Auth::id());
                } else {
                    $query->where('session_id', Session::getId());
                }
            })
            ->where('created_at', '>', now()->subHour())
            ->first();

        if ($recentView) {
            return $recentView; // Don't record duplicate views within an hour
        }

        return static::create([
            'product_id' => $productId,
            'user_id' => Auth::id(),
            'session_id' => Session::getId(),
            'ip_address' => $data['ip_address'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
            'referer' => $data['referer'] ?? request()->header('referer'),
            'source' => $data['source'] ?? 'direct',
        ]);
    }

    /**
     * Update view with duration and bounce information.
     */
    public function updateViewData(int $duration, bool $bounced = false): void
    {
        $this->update([
            'view_duration' => $duration,
            'bounced' => $bounced,
        ]);
    }

    /**
     * Get popular products based on views.
     */
    public static function getPopularProducts(int $days = 30, int $limit = 10): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
            ->groupBy('product_id')
            ->selectRaw('product_id, COUNT(*) as view_count, COUNT(DISTINCT user_id) as unique_viewers')
            ->orderBy('view_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($view) {
                return [
                    'product' => $view->product,
                    'view_count' => $view->view_count,
                    'unique_viewers' => $view->unique_viewers,
                ];
            })
            ->toArray();
    }

    /**
     * Get trending products (products with increasing views).
     */
    public static function getTrendingProducts(int $limit = 10): array
    {
        // Compare last 7 days vs previous 7 days
        $recent = static::where('created_at', '>=', now()->subDays(7))
            ->groupBy('product_id')
            ->selectRaw('product_id, COUNT(*) as recent_views')
            ->pluck('recent_views', 'product_id');

        $previous = static::whereBetween('created_at', [now()->subDays(14), now()->subDays(7)])
            ->groupBy('product_id')
            ->selectRaw('product_id, COUNT(*) as previous_views')
            ->pluck('previous_views', 'product_id');

        $trending = [];
        foreach ($recent as $productId => $recentViews) {
            $previousViews = $previous[$productId] ?? 0;
            $growth = $previousViews > 0 ? (($recentViews - $previousViews) / $previousViews) * 100 : 100;
            
            if ($growth > 0 && $recentViews >= 5) { // Minimum 5 views and positive growth
                $trending[$productId] = [
                    'recent_views' => $recentViews,
                    'previous_views' => $previousViews,
                    'growth_percentage' => round($growth, 1),
                ];
            }
        }

        // Sort by growth percentage
        uasort($trending, function ($a, $b) {
            return $b['growth_percentage'] <=> $a['growth_percentage'];
        });

        $trendingProductIds = array_slice(array_keys($trending), 0, $limit);
        $products = Product::whereIn('id', $trendingProductIds)
            ->with(['category:id,name'])
            ->get()
            ->keyBy('id');

        $result = [];
        foreach ($trendingProductIds as $productId) {
            if (isset($products[$productId])) {
                $result[] = [
                    'product' => $products[$productId],
                    'recent_views' => $trending[$productId]['recent_views'],
                    'previous_views' => $trending[$productId]['previous_views'],
                    'growth_percentage' => $trending[$productId]['growth_percentage'],
                ];
            }
        }

        return $result;
    }

    /**
     * Get view statistics for a product.
     */
    public static function getProductStats(int $productId, int $days = 30): array
    {
        $stats = static::where('product_id', $productId)
            ->where('created_at', '>=', now()->subDays($days))
            ->selectRaw('
                COUNT(*) as total_views,
                COUNT(DISTINCT user_id) as unique_viewers,
                COUNT(DISTINCT session_id) as unique_sessions,
                AVG(view_duration) as avg_duration,
                COUNT(CASE WHEN bounced THEN 1 END) as bounced_views
            ')
            ->first();

        $bounceRate = $stats->total_views > 0 ? ($stats->bounced_views / $stats->total_views) * 100 : 0;

        return [
            'total_views' => $stats->total_views,
            'unique_viewers' => $stats->unique_viewers,
            'unique_sessions' => $stats->unique_sessions,
            'avg_duration' => round($stats->avg_duration ?? 0, 1),
            'bounced_views' => $stats->bounced_views,
            'bounce_rate' => round($bounceRate, 1),
        ];
    }

    /**
     * Get view trends over time for a product.
     */
    public static function getProductViewTrends(int $productId, int $days = 30): array
    {
        return static::where('product_id', $productId)
            ->where('created_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->selectRaw('DATE(created_at) as date, COUNT(*) as views')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get top traffic sources.
     */
    public static function getTopSources(int $days = 30, int $limit = 10): array
    {
        return static::where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('source')
            ->groupBy('source')
            ->selectRaw('source, COUNT(*) as view_count')
            ->orderBy('view_count', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Scope for recent views.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for non-bounced views.
     */
    public function scopeNonBounced($query)
    {
        return $query->where('bounced', false);
    }

    /**
     * Scope for unique viewers.
     */
    public function scopeUniqueViewers($query)
    {
        return $query->distinct('user_id');
    }
}
