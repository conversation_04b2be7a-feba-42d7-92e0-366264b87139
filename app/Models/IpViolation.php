<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class IpViolation extends Model
{
    use HasFactory;

    /**
     * Disable default timestamps since we use custom timestamp fields.
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'ip_address',
        'violation_type',
        'count',
        'user_id',
        'file_id',
        'details',
        'first_violation_at',
        'last_violation_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'count' => 'integer',
        'details' => 'array',
        'first_violation_at' => 'datetime',
        'last_violation_at' => 'datetime',
    ];

    /**
     * Violation types constants.
     */
    const VIOLATION_TYPES = [
        'RATE_LIMIT_EXCEEDED' => 'rate_limit_exceeded',
        'INVALID_TOKEN_USAGE' => 'invalid_token_usage',
        'SUSPICIOUS_DOWNLOAD_PATTERN' => 'suspicious_download_pattern',
        'MULTIPLE_FAILED_LOGINS' => 'multiple_failed_logins',
        'UNAUTHORIZED_ACCESS_ATTEMPT' => 'unauthorized_access_attempt',
        'BOT_ACTIVITY' => 'bot_activity',
        'SCRAPING_ATTEMPT' => 'scraping_attempt',
        'BRUTE_FORCE_ATTACK' => 'brute_force_attack',
        'DEVICE_FINGERPRINT_MISMATCH' => 'device_fingerprint_mismatch',
        'GEOLOCATION_ANOMALY' => 'geolocation_anomaly',
    ];

    /**
     * Violation thresholds for automatic actions.
     */
    const VIOLATION_THRESHOLDS = [
        'rate_limit_exceeded' => 5,
        'invalid_token_usage' => 3,
        'suspicious_download_pattern' => 10,
        'multiple_failed_logins' => 5,
        'unauthorized_access_attempt' => 3,
        'bot_activity' => 10,
        'scraping_attempt' => 5,
        'brute_force_attack' => 3,
        'device_fingerprint_mismatch' => 5,
        'geolocation_anomaly' => 3,
    ];

    /**
     * Get the user associated with the violation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the file associated with the violation.
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class);
    }

    /**
     * Scope to get violations by IP address.
     */
    public function scopeByIp($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope to get violations by type.
     */
    public function scopeByType($query, $type)
    {
        return $query->where('violation_type', $type);
    }

    /**
     * Scope to get violations above threshold.
     */
    public function scopeAboveThreshold($query)
    {
        return $query->where(function ($q) {
            foreach (self::VIOLATION_THRESHOLDS as $type => $threshold) {
                $q->orWhere(function ($subQuery) use ($type, $threshold) {
                    $subQuery->where('violation_type', $type)
                             ->where('count', '>=', $threshold);
                });
            }
        });
    }

    /**
     * Scope to get recent violations.
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('last_violation_at', '>=', now()->subHours($hours));
    }

    /**
     * Record a violation for an IP address.
     */
    public static function recordViolation(
        string $ipAddress,
        string $violationType,
        ?User $user = null,
        ?File $file = null,
        array $details = []
    ): self {
        $violation = static::firstOrNew([
            'ip_address' => $ipAddress,
            'violation_type' => $violationType,
        ]);

        if ($violation->exists) {
            // Update existing violation
            $violation->increment('count');
            $violation->update([
                'last_violation_at' => now(),
                'details' => array_merge($violation->details ?? [], $details),
            ]);
        } else {
            // Create new violation record
            $violation->fill([
                'count' => 1,
                'user_id' => $user?->id,
                'file_id' => $file?->id,
                'details' => $details,
                'first_violation_at' => now(),
                'last_violation_at' => now(),
            ]);
            $violation->save();
        }

        // Log security event
        SecurityEvent::logIpViolation($violationType, $ipAddress, $user, $details);

        // Check if automatic action is needed
        $violation->checkForAutomaticAction();

        return $violation;
    }

    /**
     * Check if this violation should trigger automatic action.
     */
    public function checkForAutomaticAction(): void
    {
        $threshold = self::VIOLATION_THRESHOLDS[$this->violation_type] ?? 10;

        if ($this->count >= $threshold) {
            // Auto-blacklist the IP
            IpBlacklist::blacklistIp(
                $this->ip_address,
                "Automatic blacklist due to {$this->violation_type} violations (count: {$this->count})",
                now()->addDays(1) // 24-hour blacklist
            );

            // Log critical security event
            SecurityEvent::logEvent(
                SecurityEvent::EVENT_TYPES['SUSPICIOUS_ACTIVITY'],
                "IP automatically blacklisted due to repeated violations: {$this->ip_address}",
                'critical',
                $this->user,
                $this->ip_address,
                eventData: [
                    'violation_type' => $this->violation_type,
                    'violation_count' => $this->count,
                    'auto_action' => 'blacklisted',
                ]
            );
        }
    }

    /**
     * Check if this violation is above threshold.
     */
    public function isAboveThreshold(): bool
    {
        $threshold = self::VIOLATION_THRESHOLDS[$this->violation_type] ?? 10;
        return $this->count >= $threshold;
    }

    /**
     * Get violation statistics for an IP.
     */
    public static function getIpStats(string $ipAddress): array
    {
        $violations = static::byIp($ipAddress)->get();

        return [
            'total_violations' => $violations->sum('count'),
            'violation_types' => $violations->pluck('violation_type')->unique()->count(),
            'first_violation' => $violations->min('first_violation_at'),
            'last_violation' => $violations->max('last_violation_at'),
            'above_threshold' => $violations->filter->isAboveThreshold()->count(),
            'by_type' => $violations->groupBy('violation_type')->map(function ($group) {
                return [
                    'count' => $group->sum('count'),
                    'last_violation' => $group->max('last_violation_at'),
                ];
            }),
        ];
    }

    /**
     * Clean up old violations.
     */
    public static function cleanupOldViolations(int $days = 30): int
    {
        return static::where('last_violation_at', '<', now()->subDays($days))->delete();
    }

    /**
     * Get formatted details.
     */
    public function getFormattedDetails(): string
    {
        if (empty($this->details)) {
            return 'No additional details';
        }

        return collect($this->details)
            ->map(fn($value, $key) => "{$key}: {$value}")
            ->implode(', ');
    }
}
