<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WishlistItem extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'wishlist_id',
        'product_id',
        'price_when_added',
        'notes',
        'priority',
        'notify_on_sale',
        'notify_on_restock',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price_when_added' => 'decimal:2',
        'priority' => 'integer',
        'notify_on_sale' => 'boolean',
        'notify_on_restock' => 'boolean',
    ];

    /**
     * Get the wishlist that owns the item.
     */
    public function wishlist(): BelongsTo
    {
        return $this->belongsTo(Wishlist::class);
    }

    /**
     * Get the product.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if the product is currently on sale.
     */
    public function isOnSale(): bool
    {
        return $this->product->sale_price && $this->product->sale_price < $this->product->price;
    }

    /**
     * Check if the price has dropped since added.
     */
    public function hasPriceDrop(): bool
    {
        if (!$this->price_when_added) {
            return false;
        }

        $currentPrice = $this->product->sale_price ?: $this->product->price;
        return $currentPrice < $this->price_when_added;
    }

    /**
     * Get the price difference since added.
     */
    public function getPriceDifference(): float
    {
        if (!$this->price_when_added) {
            return 0;
        }

        $currentPrice = $this->product->sale_price ?: $this->product->price;
        return $this->price_when_added - $currentPrice;
    }

    /**
     * Get the savings percentage.
     */
    public function getSavingsPercentage(): float
    {
        if (!$this->price_when_added || $this->price_when_added == 0) {
            return 0;
        }

        $priceDifference = $this->getPriceDifference();
        return ($priceDifference / $this->price_when_added) * 100;
    }

    /**
     * Check if the product is available.
     */
    public function isAvailable(): bool
    {
        return $this->product->isAvailable();
    }

    /**
     * Scope for items that should be notified on sale.
     */
    public function scopeNotifyOnSale($query)
    {
        return $query->where('notify_on_sale', true);
    }

    /**
     * Scope for items that should be notified on restock.
     */
    public function scopeNotifyOnRestock($query)
    {
        return $query->where('notify_on_restock', true);
    }

    /**
     * Scope for items with price drops.
     */
    public function scopeWithPriceDrops($query)
    {
        return $query->whereHas('product', function ($productQuery) {
            $productQuery->whereNotNull('sale_price');
        })->orWhereRaw('(SELECT COALESCE(products.sale_price, products.price) FROM products WHERE products.id = wishlist_items.product_id) < wishlist_items.price_when_added');
    }

    /**
     * Get formatted price when added.
     */
    public function getFormattedPriceWhenAddedAttribute(): string
    {
        return $this->price_when_added ? '$' . number_format($this->price_when_added, 2) : 'N/A';
    }

    /**
     * Get formatted current price.
     */
    public function getFormattedCurrentPriceAttribute(): string
    {
        $currentPrice = $this->product->sale_price ?: $this->product->price;
        return '$' . number_format($currentPrice, 2);
    }

    /**
     * Get formatted price difference.
     */
    public function getFormattedPriceDifferenceAttribute(): string
    {
        $difference = $this->getPriceDifference();
        $sign = $difference >= 0 ? '-' : '+';
        return $sign . '$' . number_format(abs($difference), 2);
    }
}
