<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class DownloadToken extends Model
{
    use HasFactory;

    /**
     * Disable updated_at timestamp since we only track creation and usage.
     */
    const UPDATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'token',
        'file_id',
        'user_id',
        'order_id',
        'ip_address',
        'user_agent',
        'device_fingerprint',
        'expires_at',
        'download_count',
        'max_downloads',
        'is_active',
        'used_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'used_at' => 'datetime',
        'download_count' => 'integer',
        'max_downloads' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the file that owns the download token.
     */
    public function file(): BelongsTo
    {
        return $this->belongsTo(File::class);
    }

    /**
     * Get the user that owns the download token.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order associated with the download token.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Scope to get only active tokens.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only valid (active and not expired) tokens.
     */
    public function scopeValid($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get tokens by IP address.
     */
    public function scopeByIp($query, $ipAddress)
    {
        return $query->where('ip_address', $ipAddress);
    }

    /**
     * Scope to get tokens for a specific file.
     */
    public function scopeForFile($query, $fileId)
    {
        return $query->where('file_id', $fileId);
    }

    /**
     * Scope to get tokens for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if the token is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if the token is valid (active and not expired).
     */
    public function isValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Check if the token has reached its download limit.
     */
    public function hasReachedLimit(): bool
    {
        return $this->download_count >= $this->max_downloads;
    }

    /**
     * Check if the token can be used for download.
     */
    public function canDownload(): bool
    {
        return $this->isValid() && !$this->hasReachedLimit();
    }

    /**
     * Validate the token against request parameters.
     */
    public function validateRequest(
        ?string $ipAddress = null,
        ?string $userAgent = null,
        ?int $userId = null
    ): bool {
        // Check if token is valid
        if (!$this->canDownload()) {
            return false;
        }

        // Validate IP address if provided and token has IP restriction
        if ($this->ip_address && $ipAddress && $this->ip_address !== $ipAddress) {
            return false;
        }

        // Validate user if provided and token has user restriction
        if ($this->user_id && $userId && $this->user_id !== $userId) {
            return false;
        }

        // Additional security: validate device fingerprint
        if ($this->device_fingerprint && $userAgent) {
            $currentFingerprint = $this->generateDeviceFingerprint($ipAddress, $userAgent);
            if ($this->device_fingerprint !== $currentFingerprint) {
                // Log suspicious activity
                \Log::warning('Download token device fingerprint mismatch', [
                    'token_id' => $this->id,
                    'expected_fingerprint' => $this->device_fingerprint,
                    'actual_fingerprint' => $currentFingerprint,
                    'ip_address' => $ipAddress,
                    'user_agent' => $userAgent,
                ]);
                
                return false;
            }
        }

        return true;
    }

    /**
     * Use the token for download.
     */
    public function use(): bool
    {
        if (!$this->canDownload()) {
            return false;
        }

        $this->increment('download_count');
        $this->used_at = now();
        
        // Deactivate token if it has reached its limit
        if ($this->hasReachedLimit()) {
            $this->is_active = false;
        }
        
        $this->save();

        return true;
    }

    /**
     * Revoke the token.
     */
    public function revoke(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Get time remaining until expiration.
     */
    public function getTimeRemainingAttribute(): ?Carbon
    {
        if ($this->isExpired()) {
            return null;
        }

        return $this->expires_at;
    }

    /**
     * Get downloads remaining.
     */
    public function getDownloadsRemainingAttribute(): int
    {
        return max(0, $this->max_downloads - $this->download_count);
    }

    /**
     * Generate device fingerprint for security validation.
     */
    private function generateDeviceFingerprint(?string $ipAddress, ?string $userAgent): string
    {
        $components = [
            $ipAddress,
            $userAgent,
            request()->header('Accept-Language'),
            request()->header('Accept-Encoding'),
        ];
        
        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Clean up expired tokens.
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())
                    ->where('is_active', true)
                    ->update(['is_active' => false]);
    }

    /**
     * Get usage statistics for the token.
     */
    public function getUsageStats(): array
    {
        return [
            'total_downloads' => $this->download_count,
            'remaining_downloads' => $this->downloads_remaining,
            'is_expired' => $this->isExpired(),
            'is_valid' => $this->isValid(),
            'can_download' => $this->canDownload(),
            'created_at' => $this->created_at,
            'expires_at' => $this->expires_at,
            'last_used_at' => $this->used_at,
        ];
    }
}
