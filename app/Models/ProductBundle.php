<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class ProductBundle extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'short_description',
        'original_price',
        'bundle_price',
        'discount_amount',
        'discount_percentage',
        'discount_type',
        'image',
        'gallery',
        'status',
        'featured',
        'sort_order',
        'starts_at',
        'ends_at',
        'max_purchases',
        'purchases_count',
        'settings',
        'meta_title',
        'meta_description',
        'tags',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'original_price' => 'decimal:2',
        'bundle_price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'featured' => 'boolean',
        'sort_order' => 'integer',
        'starts_at' => 'date',
        'ends_at' => 'date',
        'max_purchases' => 'integer',
        'purchases_count' => 'integer',
        'gallery' => 'array',
        'settings' => 'array',
        'tags' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($bundle) {
            if (empty($bundle->uuid)) {
                $bundle->uuid = (string) Str::uuid();
            }
            
            if (empty($bundle->slug)) {
                $bundle->slug = Str::slug($bundle->name);
            }
        });

        static::updating(function ($bundle) {
            if ($bundle->isDirty('name') && empty($bundle->slug)) {
                $bundle->slug = Str::slug($bundle->name);
            }
        });
    }

    /**
     * Get the user who created the bundle.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the bundle items.
     */
    public function items(): HasMany
    {
        return $this->hasMany(ProductBundleItem::class, 'bundle_id');
    }

    /**
     * Get the products in this bundle.
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_bundle_items', 'bundle_id', 'product_id')
            ->withPivot(['quantity', 'price_override', 'sort_order', 'is_required', 'is_optional'])
            ->withTimestamps()
            ->orderBy('product_bundle_items.sort_order');
    }

    /**
     * Scope for active bundles.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for featured bundles.
     */
    public function scopeFeatured($query)
    {
        return $query->where('featured', true);
    }

    /**
     * Scope for available bundles (within date range).
     */
    public function scopeAvailable($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('starts_at')
              ->orWhere('starts_at', '<=', now());
        })->where(function ($q) {
            $q->whereNull('ends_at')
              ->orWhere('ends_at', '>=', now());
        });
    }

    /**
     * Check if bundle is available for purchase.
     */
    public function isAvailable(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->starts_at && $this->starts_at > now()) {
            return false;
        }

        if ($this->ends_at && $this->ends_at < now()) {
            return false;
        }

        if ($this->max_purchases && $this->purchases_count >= $this->max_purchases) {
            return false;
        }

        return true;
    }

    /**
     * Calculate bundle pricing.
     */
    public function calculatePricing(): void
    {
        $originalPrice = $this->products->sum(function ($product) {
            $price = $product->pivot->price_override ?? ($product->sale_price ?? $product->price);
            return $price * $product->pivot->quantity;
        });

        $this->original_price = $originalPrice;

        if ($this->discount_type === 'percentage') {
            $this->discount_amount = ($originalPrice * $this->discount_percentage) / 100;
            $this->bundle_price = $originalPrice - $this->discount_amount;
        } else {
            $this->bundle_price = $originalPrice - $this->discount_amount;
            $this->discount_percentage = $originalPrice > 0 ? ($this->discount_amount / $originalPrice) * 100 : 0;
        }

        $this->save();
    }

    /**
     * Add a product to the bundle.
     */
    public function addProduct(Product $product, array $options = []): ProductBundleItem
    {
        $item = $this->items()->create([
            'product_id' => $product->id,
            'quantity' => $options['quantity'] ?? 1,
            'price_override' => $options['price_override'] ?? null,
            'sort_order' => $options['sort_order'] ?? 0,
            'is_required' => $options['is_required'] ?? true,
            'is_optional' => $options['is_optional'] ?? false,
        ]);

        $this->calculatePricing();

        return $item;
    }

    /**
     * Remove a product from the bundle.
     */
    public function removeProduct(Product $product): bool
    {
        $removed = $this->items()->where('product_id', $product->id)->delete() > 0;
        
        if ($removed) {
            $this->calculatePricing();
        }

        return $removed;
    }

    /**
     * Get the total savings amount.
     */
    public function getSavingsAmount(): float
    {
        return $this->original_price - $this->bundle_price;
    }

    /**
     * Get the savings percentage.
     */
    public function getSavingsPercentage(): float
    {
        if ($this->original_price <= 0) {
            return 0;
        }

        return (($this->original_price - $this->bundle_price) / $this->original_price) * 100;
    }

    /**
     * Get the bundle's main image URL.
     */
    public function getImageUrl(): ?string
    {
        if ($this->image) {
            return asset('storage/' . $this->image);
        }

        return null;
    }

    /**
     * Get the bundle's gallery images.
     */
    public function getGalleryUrls(): array
    {
        if (!$this->gallery || !is_array($this->gallery)) {
            return [];
        }

        return array_map(function ($image) {
            return asset('storage/' . $image);
        }, $this->gallery);
    }

    /**
     * Get required products in the bundle.
     */
    public function getRequiredProducts()
    {
        return $this->products()->wherePivot('is_required', true)->get();
    }

    /**
     * Get optional products in the bundle.
     */
    public function getOptionalProducts()
    {
        return $this->products()->wherePivot('is_optional', true)->get();
    }

    /**
     * Check if all required products are available.
     */
    public function hasAllRequiredProductsAvailable(): bool
    {
        $requiredProducts = $this->getRequiredProducts();
        
        foreach ($requiredProducts as $product) {
            if (!$product->isAvailable()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get bundle statistics.
     */
    public function getStats(): array
    {
        return [
            'total_products' => $this->products()->count(),
            'required_products' => $this->products()->wherePivot('is_required', true)->count(),
            'optional_products' => $this->products()->wherePivot('is_optional', true)->count(),
            'original_price' => $this->original_price,
            'bundle_price' => $this->bundle_price,
            'savings_amount' => $this->getSavingsAmount(),
            'savings_percentage' => $this->getSavingsPercentage(),
            'purchases_count' => $this->purchases_count,
            'max_purchases' => $this->max_purchases,
            'remaining_purchases' => $this->max_purchases ? $this->max_purchases - $this->purchases_count : null,
        ];
    }

    /**
     * Increment purchases count.
     */
    public function incrementPurchases(): void
    {
        $this->increment('purchases_count');
    }

    /**
     * Get formatted original price.
     */
    public function getFormattedOriginalPriceAttribute(): string
    {
        return '$' . number_format($this->original_price, 2);
    }

    /**
     * Get formatted bundle price.
     */
    public function getFormattedBundlePriceAttribute(): string
    {
        return '$' . number_format($this->bundle_price, 2);
    }

    /**
     * Get formatted savings amount.
     */
    public function getFormattedSavingsAmountAttribute(): string
    {
        return '$' . number_format($this->getSavingsAmount(), 2);
    }
}
