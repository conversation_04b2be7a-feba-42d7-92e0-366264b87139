<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;

class ProductComparison extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'session_id',
        'name',
        'product_ids',
        'settings',
        'is_public',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'product_ids' => 'array',
        'settings' => 'array',
        'is_public' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($comparison) {
            if (empty($comparison->uuid)) {
                $comparison->uuid = (string) Str::uuid();
            }
            
            // Set expiry for guest comparisons (30 days)
            if (!$comparison->user_id && !$comparison->expires_at) {
                $comparison->expires_at = now()->addDays(30);
            }
        });
    }

    /**
     * Get the user that owns the comparison.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the products being compared.
     */
    public function products(): Collection
    {
        if (empty($this->product_ids)) {
            return collect();
        }

        return Product::whereIn('id', $this->product_ids)
            ->orderByRaw('FIELD(id, ' . implode(',', $this->product_ids) . ')')
            ->get();
    }

    /**
     * Add a product to the comparison.
     */
    public function addProduct(int $productId): bool
    {
        $productIds = $this->product_ids ?? [];
        
        if (in_array($productId, $productIds)) {
            return false; // Product already in comparison
        }

        if (count($productIds) >= 4) {
            return false; // Maximum 4 products for comparison
        }

        $productIds[] = $productId;
        $this->update(['product_ids' => $productIds]);
        
        return true;
    }

    /**
     * Remove a product from the comparison.
     */
    public function removeProduct(int $productId): bool
    {
        $productIds = $this->product_ids ?? [];
        $key = array_search($productId, $productIds);
        
        if ($key === false) {
            return false; // Product not in comparison
        }

        unset($productIds[$key]);
        $this->update(['product_ids' => array_values($productIds)]);
        
        return true;
    }

    /**
     * Check if a product is in the comparison.
     */
    public function hasProduct(int $productId): bool
    {
        return in_array($productId, $this->product_ids ?? []);
    }

    /**
     * Get the number of products in the comparison.
     */
    public function getProductCount(): int
    {
        return count($this->product_ids ?? []);
    }

    /**
     * Check if comparison is full (4 products).
     */
    public function isFull(): bool
    {
        return $this->getProductCount() >= 4;
    }

    /**
     * Check if comparison is empty.
     */
    public function isEmpty(): bool
    {
        return $this->getProductCount() === 0;
    }

    /**
     * Get comparison data with products and their attributes.
     */
    public function getComparisonData(): array
    {
        $products = $this->products();
        
        if ($products->isEmpty()) {
            return [];
        }

        // Define comparison attributes
        $attributes = [
            'basic' => [
                'name' => 'Product Name',
                'price' => 'Price',
                'sale_price' => 'Sale Price',
                'average_rating' => 'Rating',
                'total_reviews' => 'Reviews',
            ],
            'details' => [
                'description' => 'Description',
                'short_description' => 'Summary',
                'category' => 'Category',
                'tags' => 'Tags',
            ],
            'digital' => [
                'digital' => 'Digital Product',
                'downloadable' => 'Downloadable',
                'download_limit' => 'Download Limit',
                'download_expiry_days' => 'Download Expiry',
            ],
            'features' => [
                'featured' => 'Featured',
                'status' => 'Status',
            ]
        ];

        $comparisonData = [
            'products' => $products->toArray(),
            'attributes' => $attributes,
            'comparison_matrix' => []
        ];

        // Build comparison matrix
        foreach ($attributes as $section => $sectionAttributes) {
            $comparisonData['comparison_matrix'][$section] = [];
            
            foreach ($sectionAttributes as $key => $label) {
                $comparisonData['comparison_matrix'][$section][$key] = [
                    'label' => $label,
                    'values' => []
                ];
                
                foreach ($products as $product) {
                    $value = $this->getProductAttributeValue($product, $key);
                    $comparisonData['comparison_matrix'][$section][$key]['values'][] = $value;
                }
            }
        }

        return $comparisonData;
    }

    /**
     * Get formatted value for a product attribute.
     */
    protected function getProductAttributeValue(Product $product, string $attribute): array
    {
        switch ($attribute) {
            case 'price':
                return [
                    'value' => $product->price,
                    'formatted' => '$' . number_format($product->price, 2),
                    'type' => 'currency'
                ];
                
            case 'sale_price':
                return [
                    'value' => $product->sale_price,
                    'formatted' => $product->sale_price ? '$' . number_format($product->sale_price, 2) : 'N/A',
                    'type' => 'currency'
                ];
                
            case 'average_rating':
                return [
                    'value' => $product->average_rating,
                    'formatted' => number_format($product->average_rating, 1) . '/5',
                    'type' => 'rating'
                ];
                
            case 'total_reviews':
                return [
                    'value' => $product->total_reviews,
                    'formatted' => number_format($product->total_reviews) . ' reviews',
                    'type' => 'number'
                ];
                
            case 'category':
                return [
                    'value' => $product->category?->name ?? 'Uncategorized',
                    'formatted' => $product->category?->name ?? 'Uncategorized',
                    'type' => 'text'
                ];
                
            case 'tags':
                $tags = is_array($product->tags) ? $product->tags : [];
                return [
                    'value' => $tags,
                    'formatted' => empty($tags) ? 'None' : implode(', ', $tags),
                    'type' => 'array'
                ];
                
            case 'digital':
            case 'downloadable':
            case 'featured':
                return [
                    'value' => $product->$attribute,
                    'formatted' => $product->$attribute ? 'Yes' : 'No',
                    'type' => 'boolean'
                ];
                
            case 'download_limit':
                return [
                    'value' => $product->download_limit,
                    'formatted' => $product->download_limit ? number_format($product->download_limit) : 'Unlimited',
                    'type' => 'number'
                ];
                
            case 'download_expiry_days':
                return [
                    'value' => $product->download_expiry_days,
                    'formatted' => $product->download_expiry_days ? $product->download_expiry_days . ' days' : 'No expiry',
                    'type' => 'number'
                ];
                
            default:
                $value = $product->$attribute ?? '';
                return [
                    'value' => $value,
                    'formatted' => is_string($value) ? $value : (string) $value,
                    'type' => 'text'
                ];
        }
    }

    /**
     * Scope for active comparisons (not expired).
     */
    public function scopeActive($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope for user comparisons.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for session comparisons.
     */
    public function scopeForSession($query, string $sessionId)
    {
        return $query->where('session_id', $sessionId);
    }

    /**
     * Scope for public comparisons.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Get shareable URL for public comparisons.
     */
    public function getShareableUrl(): string
    {
        if (!$this->is_public) {
            return '';
        }

        return route('comparisons.public', $this->uuid);
    }

    /**
     * Clean up expired comparisons.
     */
    public static function cleanupExpired(): int
    {
        return static::where('expires_at', '<', now())->delete();
    }
}
