<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class File extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'original_name',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'file_hash',
        'storage_type',
        's3_bucket',
        's3_key',
        's3_region',
        's3_metadata',
        'download_count',
        'is_active',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'file_size' => 'integer',
        'download_count' => 'integer',
        'is_active' => 'boolean',
        's3_metadata' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($file) {
            if (empty($file->uuid)) {
                $file->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the product that owns the file.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the user who uploaded the file.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the download tokens for this file.
     */
    public function downloadTokens(): HasMany
    {
        return $this->hasMany(DownloadToken::class);
    }

    /**
     * Get active download tokens for this file.
     */
    public function activeDownloadTokens(): HasMany
    {
        return $this->downloadTokens()
                    ->where('is_active', true)
                    ->where('expires_at', '>', now());
    }

    /**
     * Scope to get only active files.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by storage type.
     */
    public function scopeStorageType($query, $type)
    {
        return $query->where('storage_type', $type);
    }

    /**
     * Scope to filter by product.
     */
    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Check if file is stored locally.
     */
    public function isLocal(): bool
    {
        return in_array($this->storage_type, ['local', 'hybrid']);
    }

    /**
     * Check if file is stored in S3.
     */
    public function isS3(): bool
    {
        return in_array($this->storage_type, ['s3', 'hybrid']);
    }

    /**
     * Get the file URL based on storage type.
     */
    public function getUrlAttribute(): string
    {
        if ($this->isS3()) {
            return $this->getS3Url();
        }
        
        return $this->getLocalUrl();
    }

    /**
     * Get the local file URL.
     */
    public function getLocalUrl(): string
    {
        if ($this->file_path) {
            return Storage::disk('local')->url($this->file_path);
        }
        
        return '';
    }

    /**
     * Get the S3 file URL (presigned for security).
     */
    public function getS3Url(int $expiresInMinutes = 60): string
    {
        if (!$this->isS3() || !$this->s3_key) {
            return '';
        }

        try {
            return Storage::disk('s3')->temporaryUrl(
                $this->s3_key,
                now()->addMinutes($expiresInMinutes)
            );
        } catch (\Exception $e) {
            \Log::error('Failed to generate S3 URL for file: ' . $this->id, [
                'error' => $e->getMessage(),
                'file_id' => $this->id,
                's3_key' => $this->s3_key,
            ]);
            
            return '';
        }
    }

    /**
     * Get the file size in human readable format.
     */
    public function getFormattedSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file exists in storage.
     */
    public function exists(): bool
    {
        if ($this->isLocal() && $this->file_path) {
            return Storage::disk('local')->exists($this->file_path);
        }
        
        if ($this->isS3() && $this->s3_key) {
            return Storage::disk('s3')->exists($this->s3_key);
        }
        
        return false;
    }

    /**
     * Increment download count.
     */
    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    /**
     * Generate a secure download token.
     */
    public function generateDownloadToken(
        ?User $user = null,
        ?int $orderId = null,
        int $expiresInHours = 24,
        int $maxDownloads = 1
    ): DownloadToken {
        return DownloadToken::create([
            'token' => Str::random(64),
            'file_id' => $this->id,
            'user_id' => $user?->id,
            'order_id' => $orderId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'device_fingerprint' => $this->generateDeviceFingerprint(),
            'expires_at' => now()->addHours($expiresInHours),
            'max_downloads' => $maxDownloads,
            'is_active' => true,
        ]);
    }

    /**
     * Generate device fingerprint for security.
     */
    private function generateDeviceFingerprint(): string
    {
        $components = [
            request()->ip(),
            request()->userAgent(),
            request()->header('Accept-Language'),
            request()->header('Accept-Encoding'),
        ];
        
        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Delete file from storage when model is deleted.
     */
    protected static function booted()
    {
        static::deleting(function ($file) {
            // Delete from local storage
            if ($file->isLocal() && $file->file_path) {
                Storage::disk('local')->delete($file->file_path);
            }
            
            // Delete from S3 storage
            if ($file->isS3() && $file->s3_key) {
                Storage::disk('s3')->delete($file->s3_key);
            }
        });
    }
}
