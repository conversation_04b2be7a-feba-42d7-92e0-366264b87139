<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductView;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Inertia\Response;

class PopularProductsController extends Controller
{
    /**
     * Display popular products page.
     */
    public function index(Request $request): Response
    {
        $type = $request->get('type', 'popular');
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 20);

        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $products = $this->getProductsByType($type, $days, $limit);

        return Inertia::render('products/popular', [
            'products' => $products,
            'type' => $type,
            'days' => $days,
            'availableTypes' => [
                'popular' => 'Most Popular',
                'trending' => 'Trending',
                'bestsellers' => 'Best Sellers',
                'most_viewed' => 'Most Viewed',
                'recently_viewed' => 'Recently Viewed',
            ]
        ]);
    }

    /**
     * Get popular products.
     */
    public function getPopular(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $cacheKey = "popular_products_{$days}_{$limit}";
        
        $products = Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            return ProductView::getPopularProducts($days, $limit);
        });

        return response()->json([
            'products' => $products,
            'type' => 'popular',
            'days' => $days
        ]);
    }

    /**
     * Get trending products.
     */
    public function getTrending(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $limit = min(max($limit, 1), 50);

        $cacheKey = "trending_products_{$limit}";
        
        $products = Cache::remember($cacheKey, 900, function () use ($limit) {
            return ProductView::getTrendingProducts($limit);
        });

        return response()->json([
            'products' => $products,
            'type' => 'trending'
        ]);
    }

    /**
     * Get best selling products.
     */
    public function getBestSellers(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $cacheKey = "bestsellers_{$days}_{$limit}";
        
        $products = Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            return OrderItem::whereHas('order', function ($query) use ($days) {
                    $query->where('status', 'completed')
                          ->where('created_at', '>=', now()->subDays($days));
                })
                ->with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
                ->groupBy('product_id')
                ->selectRaw('product_id, SUM(quantity) as total_sold, COUNT(*) as order_count')
                ->orderBy('total_sold', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($item) {
                    return [
                        'product' => $item->product,
                        'total_sold' => $item->total_sold,
                        'order_count' => $item->order_count,
                    ];
                })
                ->toArray();
        });

        return response()->json([
            'products' => $products,
            'type' => 'bestsellers',
            'days' => $days
        ]);
    }

    /**
     * Get most viewed products.
     */
    public function getMostViewed(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $cacheKey = "most_viewed_products_{$days}_{$limit}";
        
        $products = Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            return ProductView::where('created_at', '>=', now()->subDays($days))
                ->with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
                ->groupBy('product_id')
                ->selectRaw('product_id, COUNT(*) as view_count, AVG(view_duration) as avg_duration')
                ->orderBy('view_count', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($view) {
                    return [
                        'product' => $view->product,
                        'view_count' => $view->view_count,
                        'avg_duration' => round($view->avg_duration ?? 0, 1),
                    ];
                })
                ->toArray();
        });

        return response()->json([
            'products' => $products,
            'type' => 'most_viewed',
            'days' => $days
        ]);
    }

    /**
     * Get recently viewed products for current user.
     */
    public function getRecentlyViewed(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $limit = min(max($limit, 1), 20);

        $query = ProductView::with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
            ->orderBy('created_at', 'desc')
            ->limit($limit);

        if (auth()->check()) {
            $query->where('user_id', auth()->id());
        } else {
            $query->where('session_id', session()->getId());
        }

        $views = $query->get();
        
        $products = $views->map(function ($view) {
            return [
                'product' => $view->product,
                'viewed_at' => $view->created_at->toISOString(),
            ];
        })->toArray();

        return response()->json([
            'products' => $products,
            'type' => 'recently_viewed'
        ]);
    }

    /**
     * Record a product view.
     */
    public function recordView(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'source' => 'nullable|string|max:100',
        ]);

        $view = ProductView::recordView($request->product_id, [
            'source' => $request->source,
        ]);

        return response()->json([
            'message' => 'View recorded',
            'view_id' => $view->id
        ]);
    }

    /**
     * Update view data (duration, bounce).
     */
    public function updateView(Request $request): JsonResponse
    {
        $request->validate([
            'view_id' => 'required|exists:product_views,id',
            'duration' => 'required|integer|min:0',
            'bounced' => 'boolean',
        ]);

        $view = ProductView::findOrFail($request->view_id);
        $view->updateViewData($request->duration, $request->boolean('bounced'));

        return response()->json([
            'message' => 'View updated'
        ]);
    }

    /**
     * Get product statistics.
     */
    public function getProductStats(Request $request, Product $product): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $stats = ProductView::getProductStats($product->id, $days);
        $trends = ProductView::getProductViewTrends($product->id, $days);

        return response()->json([
            'product' => $product,
            'stats' => $stats,
            'trends' => $trends,
            'days' => $days
        ]);
    }

    /**
     * Get products by type.
     */
    protected function getProductsByType(string $type, int $days, int $limit): array
    {
        switch ($type) {
            case 'trending':
                return ProductView::getTrendingProducts($limit);
            
            case 'bestsellers':
                return OrderItem::whereHas('order', function ($query) use ($days) {
                        $query->where('status', 'completed')
                              ->where('created_at', '>=', now()->subDays($days));
                    })
                    ->with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
                    ->groupBy('product_id')
                    ->selectRaw('product_id, SUM(quantity) as total_sold, COUNT(*) as order_count')
                    ->orderBy('total_sold', 'desc')
                    ->limit($limit)
                    ->get()
                    ->map(function ($item) {
                        return [
                            'product' => $item->product,
                            'total_sold' => $item->total_sold,
                            'order_count' => $item->order_count,
                        ];
                    })
                    ->toArray();
            
            case 'most_viewed':
                return ProductView::where('created_at', '>=', now()->subDays($days))
                    ->with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
                    ->groupBy('product_id')
                    ->selectRaw('product_id, COUNT(*) as view_count, AVG(view_duration) as avg_duration')
                    ->orderBy('view_count', 'desc')
                    ->limit($limit)
                    ->get()
                    ->map(function ($view) {
                        return [
                            'product' => $view->product,
                            'view_count' => $view->view_count,
                            'avg_duration' => round($view->avg_duration ?? 0, 1),
                        ];
                    })
                    ->toArray();
            
            case 'recently_viewed':
                $query = ProductView::with('product:id,name,slug,price,sale_price,image,average_rating,total_reviews')
                    ->orderBy('created_at', 'desc')
                    ->limit($limit);

                if (auth()->check()) {
                    $query->where('user_id', auth()->id());
                } else {
                    $query->where('session_id', session()->getId());
                }

                return $query->get()
                    ->map(function ($view) {
                        return [
                            'product' => $view->product,
                            'viewed_at' => $view->created_at->toISOString(),
                        ];
                    })
                    ->toArray();
            
            default: // popular
                return ProductView::getPopularProducts($days, $limit);
        }
    }

    /**
     * Get dashboard summary for popular products.
     */
    public function getDashboardSummary(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        $days = min(max($days, 1), 30);

        $totalViews = ProductView::recent($days)->count();
        $uniqueViewers = ProductView::recent($days)->distinct('user_id')->count('user_id');
        $avgDuration = ProductView::recent($days)->avg('view_duration') ?? 0;
        $bounceRate = ProductView::recent($days)->where('bounced', true)->count();
        $bounceRatePercent = $totalViews > 0 ? ($bounceRate / $totalViews) * 100 : 0;

        $topProduct = ProductView::recent($days)
            ->with('product:id,name,slug')
            ->groupBy('product_id')
            ->selectRaw('product_id, COUNT(*) as view_count')
            ->orderBy('view_count', 'desc')
            ->first();

        return response()->json([
            'total_views' => $totalViews,
            'unique_viewers' => $uniqueViewers,
            'avg_duration' => round($avgDuration, 1),
            'bounce_rate' => round($bounceRatePercent, 1),
            'top_product' => $topProduct?->product?->name,
            'top_product_views' => $topProduct?->view_count ?? 0,
        ]);
    }
}
