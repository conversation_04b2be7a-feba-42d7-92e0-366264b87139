<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\User;
use App\Services\RelatedProductsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class RelatedProductsController extends Controller
{
    protected RelatedProductsService $relatedProductsService;

    public function __construct(RelatedProductsService $relatedProductsService)
    {
        $this->relatedProductsService = $relatedProductsService;
    }

    /**
     * Get related products for a specific product.
     */
    public function getRelatedProducts(Request $request, Product $product): JsonResponse
    {
        $limit = $request->get('limit', 6);
        $limit = min(max($limit, 1), 20); // Ensure limit is between 1 and 20

        $relatedProducts = $this->relatedProductsService->getRelatedProducts($product, $limit);

        return response()->json([
            'related_products' => $relatedProducts->map(function ($relatedProduct) {
                return [
                    'id' => $relatedProduct->id,
                    'name' => $relatedProduct->name,
                    'slug' => $relatedProduct->slug,
                    'short_description' => $relatedProduct->short_description,
                    'price' => $relatedProduct->price,
                    'sale_price' => $relatedProduct->sale_price,
                    'image' => $relatedProduct->image,
                    'average_rating' => $relatedProduct->average_rating,
                    'total_reviews' => $relatedProduct->total_reviews,
                    'total_ratings' => $relatedProduct->total_ratings,
                    'rating_breakdown' => $relatedProduct->rating_breakdown,
                    'reviews_enabled' => $relatedProduct->reviews_enabled,
                    'featured' => $relatedProduct->featured,
                    'status' => $relatedProduct->status,
                ];
            }),
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
            ]
        ]);
    }

    /**
     * Get personalized recommendations for the authenticated user.
     */
    public function getPersonalizedRecommendations(Request $request): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json([
                'message' => 'Authentication required for personalized recommendations'
            ], 401);
        }

        $user = Auth::user();
        $limit = $request->get('limit', 10);
        $limit = min(max($limit, 1), 20);

        $recommendations = $this->relatedProductsService->getPersonalizedRecommendations($user, $limit);

        return response()->json([
            'recommendations' => $recommendations->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'short_description' => $product->short_description,
                    'price' => $product->price,
                    'sale_price' => $product->sale_price,
                    'image' => $product->image,
                    'average_rating' => $product->average_rating,
                    'total_reviews' => $product->total_reviews,
                    'total_ratings' => $product->total_ratings,
                    'rating_breakdown' => $product->rating_breakdown,
                    'reviews_enabled' => $product->reviews_enabled,
                    'featured' => $product->featured,
                    'status' => $product->status,
                ];
            }),
            'user_id' => $user->id
        ]);
    }

    /**
     * Get trending products.
     */
    public function getTrendingProducts(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $limit = min(max($limit, 1), 20);

        $trendingProducts = $this->relatedProductsService->getTrendingProducts($limit);

        return response()->json([
            'trending_products' => $trendingProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'short_description' => $product->short_description,
                    'price' => $product->price,
                    'sale_price' => $product->sale_price,
                    'image' => $product->image,
                    'average_rating' => $product->average_rating,
                    'total_reviews' => $product->total_reviews,
                    'total_ratings' => $product->total_ratings,
                    'rating_breakdown' => $product->rating_breakdown,
                    'reviews_enabled' => $product->reviews_enabled,
                    'featured' => $product->featured,
                    'status' => $product->status,
                    'recent_orders_count' => $product->recent_orders_count ?? 0,
                ];
            })
        ]);
    }

    /**
     * Get frequently bought together products.
     */
    public function getFrequentlyBoughtTogether(Request $request, Product $product): JsonResponse
    {
        $limit = $request->get('limit', 4);
        $limit = min(max($limit, 1), 10);

        // Use the protected method through reflection or create a public wrapper
        $relatedProducts = $this->relatedProductsService->getRelatedProducts($product, $limit * 2);
        
        // Filter to get products that are actually frequently bought together
        // This is a simplified version - in production you might want a more sophisticated algorithm
        $frequentlyBoughtTogether = $relatedProducts->take($limit);

        return response()->json([
            'frequently_bought_together' => $frequentlyBoughtTogether->map(function ($relatedProduct) {
                return [
                    'id' => $relatedProduct->id,
                    'name' => $relatedProduct->name,
                    'slug' => $relatedProduct->slug,
                    'short_description' => $relatedProduct->short_description,
                    'price' => $relatedProduct->price,
                    'sale_price' => $relatedProduct->sale_price,
                    'image' => $relatedProduct->image,
                    'average_rating' => $relatedProduct->average_rating,
                    'total_reviews' => $relatedProduct->total_reviews,
                    'featured' => $relatedProduct->featured,
                ];
            }),
            'base_product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'image' => $product->image,
            ],
            'total_price' => $frequentlyBoughtTogether->sum(function ($p) {
                return $p->sale_price ?: $p->price;
            }) + ($product->sale_price ?: $product->price)
        ]);
    }

    /**
     * Get products by category (for related products).
     */
    public function getProductsByCategory(Request $request, $categoryId): JsonResponse
    {
        $limit = $request->get('limit', 12);
        $limit = min(max($limit, 1), 50);
        $excludeId = $request->get('exclude_id');

        $query = Product::active()
            ->where('category_id', $categoryId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        $products = $query->orderBy('average_rating', 'desc')
            ->orderBy('total_reviews', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'short_description' => $product->short_description,
                    'price' => $product->price,
                    'sale_price' => $product->sale_price,
                    'image' => $product->image,
                    'average_rating' => $product->average_rating,
                    'total_reviews' => $product->total_reviews,
                    'featured' => $product->featured,
                ];
            }),
            'category_id' => $categoryId
        ]);
    }

    /**
     * Get recommendation statistics (for admin).
     */
    public function getRecommendationStats(): JsonResponse
    {
        $stats = $this->relatedProductsService->getRecommendationStats();

        return response()->json($stats);
    }

    /**
     * Clear recommendation caches.
     */
    public function clearCaches(Request $request): JsonResponse
    {
        if ($request->has('product_id')) {
            $this->relatedProductsService->clearRelatedProductsCache($request->get('product_id'));
            $message = 'Product recommendation cache cleared';
        } else {
            $this->relatedProductsService->clearAllRecommendationCaches();
            $message = 'All recommendation caches cleared';
        }

        return response()->json(['message' => $message]);
    }
}
