<?php

namespace App\Http\Controllers;

use App\Services\ProductSearchService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class SearchController extends Controller
{
    protected ProductSearchService $searchService;

    public function __construct(ProductSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Display search results page.
     */
    public function index(Request $request): Response
    {
        $params = $this->validateSearchParams($request);
        
        $results = $this->searchService->search($params);
        $facets = $this->searchService->getFacets($params);

        return Inertia::render('search/index', [
            'results' => $results,
            'facets' => $facets,
            'query' => $params['q'] ?? '',
            'filters' => $this->getActiveFilters($params),
            'sort' => [
                'by' => $params['sort_by'] ?? 'relevance',
                'order' => $params['sort_order'] ?? 'desc',
            ],
        ]);
    }

    /**
     * API endpoint for search results.
     */
    public function search(Request $request): JsonResponse
    {
        $params = $this->validateSearchParams($request);
        
        $results = $this->searchService->search($params);
        $facets = $this->searchService->getFacets($params);

        return response()->json([
            'results' => $results,
            'facets' => $facets,
            'query' => $params['q'] ?? '',
            'filters' => $this->getActiveFilters($params),
        ]);
    }

    /**
     * Get search suggestions for autocomplete.
     */
    public function suggestions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:2|max:100',
            'limit' => 'integer|min:1|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'suggestions' => []
            ]);
        }

        $query = $request->get('q');
        $limit = $request->get('limit', 10);

        $suggestions = $this->searchService->getSuggestions($query, $limit);

        return response()->json([
            'suggestions' => $suggestions
        ]);
    }

    /**
     * Get search facets for filtering.
     */
    public function facets(Request $request): JsonResponse
    {
        $params = $this->validateSearchParams($request);
        $facets = $this->searchService->getFacets($params);

        return response()->json([
            'facets' => $facets
        ]);
    }

    /**
     * Get popular search terms.
     */
    public function popular(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $limit = min(max($limit, 1), 20);

        $popularSearches = $this->searchService->getPopularSearches($limit);

        return response()->json([
            'popular_searches' => $popularSearches
        ]);
    }

    /**
     * Quick search endpoint for header search.
     */
    public function quick(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'q' => 'required|string|min:2|max:100',
            'limit' => 'integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'products' => [],
                'suggestions' => []
            ]);
        }

        $query = $request->get('q');
        $limit = $request->get('limit', 5);

        // Get quick product results
        $params = ['q' => $query, 'per_page' => $limit];
        $results = $this->searchService->search($params);

        // Get suggestions
        $suggestions = $this->searchService->getSuggestions($query, 5);

        return response()->json([
            'products' => $results->items(),
            'suggestions' => $suggestions,
            'total_results' => $results->total(),
            'search_url' => route('search.index', ['q' => $query])
        ]);
    }

    /**
     * Validate search parameters.
     */
    protected function validateSearchParams(Request $request): array
    {
        $validator = Validator::make($request->all(), [
            'q' => 'nullable|string|max:255',
            'category_id' => 'nullable|array',
            'category_id.*' => 'integer|exists:product_categories,id',
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0',
            'min_rating' => 'nullable|numeric|min:1|max:5',
            'featured' => 'nullable|boolean',
            'digital' => 'nullable|boolean',
            'downloadable' => 'nullable|boolean',
            'on_sale' => 'nullable|boolean',
            'available_only' => 'nullable|boolean',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
            'sort_by' => 'nullable|string|in:relevance,price_low_high,price_high_low,rating,popularity,newest,name,featured',
            'sort_order' => 'nullable|string|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:50',
        ]);

        if ($validator->fails()) {
            return [];
        }

        return $validator->validated();
    }

    /**
     * Get active filters for display.
     */
    protected function getActiveFilters(array $params): array
    {
        $activeFilters = [];

        if (!empty($params['category_id'])) {
            $categoryIds = is_array($params['category_id']) ? $params['category_id'] : [$params['category_id']];
            $categories = \App\Models\ProductCategory::whereIn('id', $categoryIds)->pluck('name', 'id');
            
            foreach ($categories as $id => $name) {
                $activeFilters[] = [
                    'type' => 'category',
                    'key' => 'category_id',
                    'value' => $id,
                    'label' => $name,
                    'display' => "Category: {$name}"
                ];
            }
        }

        if (!empty($params['min_price']) || !empty($params['max_price'])) {
            $min = $params['min_price'] ?? 0;
            $max = $params['max_price'] ?? '∞';
            $activeFilters[] = [
                'type' => 'price_range',
                'key' => 'price_range',
                'value' => ['min' => $params['min_price'], 'max' => $params['max_price']],
                'label' => "Price: \${$min} - \${$max}",
                'display' => "Price: \${$min} - \${$max}"
            ];
        }

        if (!empty($params['min_rating'])) {
            $activeFilters[] = [
                'type' => 'rating',
                'key' => 'min_rating',
                'value' => $params['min_rating'],
                'label' => $params['min_rating'] . ' stars & up',
                'display' => "Rating: {$params['min_rating']} stars & up"
            ];
        }

        if (!empty($params['featured'])) {
            $activeFilters[] = [
                'type' => 'featured',
                'key' => 'featured',
                'value' => true,
                'label' => 'Featured',
                'display' => 'Featured Products'
            ];
        }

        if (isset($params['digital'])) {
            $activeFilters[] = [
                'type' => 'digital',
                'key' => 'digital',
                'value' => $params['digital'],
                'label' => $params['digital'] ? 'Digital' : 'Physical',
                'display' => $params['digital'] ? 'Digital Products' : 'Physical Products'
            ];
        }

        if (!empty($params['on_sale'])) {
            $activeFilters[] = [
                'type' => 'on_sale',
                'key' => 'on_sale',
                'value' => true,
                'label' => 'On Sale',
                'display' => 'On Sale'
            ];
        }

        if (!empty($params['tags'])) {
            foreach ($params['tags'] as $tag) {
                $activeFilters[] = [
                    'type' => 'tag',
                    'key' => 'tags',
                    'value' => $tag,
                    'label' => $tag,
                    'display' => "Tag: {$tag}"
                ];
            }
        }

        return $activeFilters;
    }

    /**
     * Clear all filters and redirect to clean search.
     */
    public function clearFilters(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        
        return response()->json([
            'redirect_url' => route('search.index', $query ? ['q' => $query] : [])
        ]);
    }

    /**
     * Get search analytics for admin.
     */
    public function analytics(Request $request): JsonResponse
    {
        // This would require admin authentication
        $this->authorize('viewAny', \App\Models\Product::class);

        $popularSearches = $this->searchService->getPopularSearches(20);
        
        return response()->json([
            'popular_searches' => $popularSearches,
            'total_searches_today' => count($popularSearches), // Simplified
        ]);
    }
}
