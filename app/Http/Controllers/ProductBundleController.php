<?php

namespace App\Http\Controllers;

use App\Models\ProductBundle;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class ProductBundleController extends Controller
{
    /**
     * Display a listing of bundles.
     */
    public function index(Request $request): Response
    {
        $query = ProductBundle::active()
            ->available()
            ->with(['products:id,name,slug,price,sale_price,image']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Featured filter
        if ($request->boolean('featured')) {
            $query->featured();
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('bundle_price', '>=', $request->get('min_price'));
        }
        if ($request->filled('max_price')) {
            $query->where('bundle_price', '<=', $request->get('max_price'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSortFields = ['name', 'bundle_price', 'discount_percentage', 'created_at', 'sort_order'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $bundles = $query->paginate(12)->withQueryString();

        return Inertia::render('bundles/index', [
            'bundles' => $bundles,
            'filters' => $request->only(['search', 'featured', 'min_price', 'max_price']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Display the specified bundle.
     */
    public function show(Request $request, ProductBundle $bundle): Response
    {
        if (!$bundle->isAvailable()) {
            abort(404, 'Bundle not available');
        }

        $bundle->load([
            'products:id,name,slug,description,short_description,price,sale_price,image,average_rating,total_reviews,reviews_enabled',
            'creator:id,name'
        ]);

        return Inertia::render('bundles/show', [
            'bundle' => $bundle,
            'stats' => $bundle->getStats(),
        ]);
    }

    /**
     * Get bundle data for API.
     */
    public function getBundleData(ProductBundle $bundle): JsonResponse
    {
        if (!$bundle->isAvailable()) {
            return response()->json(['message' => 'Bundle not available'], 404);
        }

        $bundle->load([
            'products:id,name,slug,price,sale_price,image,average_rating,total_reviews',
            'items'
        ]);

        return response()->json([
            'bundle' => $bundle,
            'stats' => $bundle->getStats(),
            'products' => $bundle->products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => $product->price,
                    'sale_price' => $product->sale_price,
                    'image' => $product->image,
                    'average_rating' => $product->average_rating,
                    'total_reviews' => $product->total_reviews,
                    'quantity' => $product->pivot->quantity,
                    'price_override' => $product->pivot->price_override,
                    'is_required' => $product->pivot->is_required,
                    'is_optional' => $product->pivot->is_optional,
                ];
            })
        ]);
    }

    /**
     * Get featured bundles.
     */
    public function getFeatured(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 6);
        $limit = min(max($limit, 1), 20);

        $bundles = ProductBundle::active()
            ->available()
            ->featured()
            ->with(['products:id,name,slug,price,sale_price,image'])
            ->orderBy('sort_order')
            ->limit($limit)
            ->get();

        return response()->json([
            'bundles' => $bundles->map(function ($bundle) {
                return [
                    'id' => $bundle->id,
                    'name' => $bundle->name,
                    'slug' => $bundle->slug,
                    'short_description' => $bundle->short_description,
                    'original_price' => $bundle->original_price,
                    'bundle_price' => $bundle->bundle_price,
                    'discount_percentage' => $bundle->getSavingsPercentage(),
                    'savings_amount' => $bundle->getSavingsAmount(),
                    'image' => $bundle->image,
                    'featured' => $bundle->featured,
                    'products_count' => $bundle->products->count(),
                ];
            })
        ]);
    }

    /**
     * Get bundles containing a specific product.
     */
    public function getBundlesForProduct(Request $request, Product $product): JsonResponse
    {
        $limit = $request->get('limit', 4);
        $limit = min(max($limit, 1), 10);

        $bundles = ProductBundle::active()
            ->available()
            ->whereHas('products', function ($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->with(['products:id,name,slug,price,sale_price,image'])
            ->orderBy('discount_percentage', 'desc')
            ->limit($limit)
            ->get();

        return response()->json([
            'bundles' => $bundles->map(function ($bundle) {
                return [
                    'id' => $bundle->id,
                    'name' => $bundle->name,
                    'slug' => $bundle->slug,
                    'short_description' => $bundle->short_description,
                    'original_price' => $bundle->original_price,
                    'bundle_price' => $bundle->bundle_price,
                    'discount_percentage' => $bundle->getSavingsPercentage(),
                    'savings_amount' => $bundle->getSavingsAmount(),
                    'image' => $bundle->image,
                    'products_count' => $bundle->products->count(),
                ];
            }),
            'product' => [
                'id' => $product->id,
                'name' => $product->name,
                'slug' => $product->slug,
            ]
        ]);
    }

    /**
     * Calculate bundle price for given products.
     */
    public function calculateBundlePrice(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_ids' => 'required|array|min:2',
            'product_ids.*' => 'exists:products,id',
            'discount_type' => 'required|in:percentage,fixed',
            'discount_value' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $products = Product::whereIn('id', $request->product_ids)->get();
        $originalPrice = $products->sum(function ($product) {
            return $product->sale_price ?? $product->price;
        });

        $discountType = $request->discount_type;
        $discountValue = $request->discount_value;

        if ($discountType === 'percentage') {
            $discountAmount = ($originalPrice * $discountValue) / 100;
            $bundlePrice = $originalPrice - $discountAmount;
            $discountPercentage = $discountValue;
        } else {
            $discountAmount = min($discountValue, $originalPrice);
            $bundlePrice = $originalPrice - $discountAmount;
            $discountPercentage = $originalPrice > 0 ? ($discountAmount / $originalPrice) * 100 : 0;
        }

        return response()->json([
            'original_price' => $originalPrice,
            'bundle_price' => max(0, $bundlePrice),
            'discount_amount' => $discountAmount,
            'discount_percentage' => round($discountPercentage, 2),
            'savings_amount' => $discountAmount,
            'products' => $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->sale_price ?? $product->price,
                ];
            })
        ]);
    }

    /**
     * Get bundle recommendations based on cart or products.
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        $productIds = $request->get('product_ids', []);
        
        if (empty($productIds)) {
            return response()->json(['bundles' => []]);
        }

        // Find bundles that contain any of the specified products
        $bundles = ProductBundle::active()
            ->available()
            ->whereHas('products', function ($query) use ($productIds) {
                $query->whereIn('product_id', $productIds);
            })
            ->with(['products:id,name,slug,price,sale_price,image'])
            ->orderBy('discount_percentage', 'desc')
            ->limit(3)
            ->get();

        return response()->json([
            'bundles' => $bundles->map(function ($bundle) {
                return [
                    'id' => $bundle->id,
                    'name' => $bundle->name,
                    'slug' => $bundle->slug,
                    'short_description' => $bundle->short_description,
                    'original_price' => $bundle->original_price,
                    'bundle_price' => $bundle->bundle_price,
                    'discount_percentage' => $bundle->getSavingsPercentage(),
                    'savings_amount' => $bundle->getSavingsAmount(),
                    'image' => $bundle->image,
                    'products_count' => $bundle->products->count(),
                ];
            })
        ]);
    }
}
