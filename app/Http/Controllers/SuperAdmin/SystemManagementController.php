<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class SystemManagementController extends Controller
{
    /**
     * Display the system management index.
     */
    public function index(Request $request): Response
    {
        $systemInfo = $this->getSystemInfo();
        
        return Inertia::render('super-admin/system/index', [
            'systemInfo' => $systemInfo,
        ]);
    }

    /**
     * Display system settings.
     */
    public function settings(Request $request): Response
    {
        // Get current system settings
        $settings = [
            'app_name' => config('app.name'),
            'app_url' => config('app.url'),
            'app_debug' => config('app.debug'),
            'app_env' => config('app.env'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
        ];

        return Inertia::render('super-admin/system/settings', [
            'settings' => $settings,
        ]);
    }

    /**
     * Update system settings.
     */
    public function updateSettings(Request $request)
    {
        $validated = $request->validate([
            'app_name' => 'required|string|max:255',
            'app_url' => 'required|url',
            'timezone' => 'required|string',
            'locale' => 'required|string',
        ]);

        // In a real implementation, you would update the .env file or database settings
        // For now, we'll just return success
        
        return redirect()->route('super-admin.system.settings')
            ->with('success', 'System settings updated successfully.');
    }

    /**
     * Display system health information.
     */
    public function health(Request $request): Response
    {
        $healthData = $this->getSystemHealth();
        
        return Inertia::render('super-admin/system/health', [
            'healthData' => $healthData,
        ]);
    }

    /**
     * Display audit logs.
     */
    public function auditLogs(Request $request): Response
    {
        // Mock audit logs data - in real implementation, you'd have an audit log model
        $auditLogs = collect([
            [
                'id' => 1,
                'user' => 'Super Admin',
                'action' => 'User Created',
                'description' => 'Created new user: <EMAIL>',
                'ip_address' => '***********',
                'created_at' => now()->subHours(2),
            ],
            [
                'id' => 2,
                'user' => 'Admin',
                'action' => 'Settings Updated',
                'description' => 'Updated system settings',
                'ip_address' => '***********',
                'created_at' => now()->subHours(5),
            ],
        ]);

        return Inertia::render('super-admin/system/audit-logs', [
            'auditLogs' => $auditLogs,
        ]);
    }

    /**
     * Get system information via API.
     */
    public function systemInfo()
    {
        return response()->json($this->getSystemInfo());
    }

    /**
     * Get comprehensive system information.
     */
    private function getSystemInfo(): array
    {
        return Cache::remember('system_info', 300, function () {
            return [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'database_version' => $this->getDatabaseVersion(),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'upload_max_filesize' => ini_get('upload_max_filesize'),
                'post_max_size' => ini_get('post_max_size'),
                'disk_usage' => $this->getDiskUsage(),
                'cache_driver' => config('cache.default'),
                'queue_driver' => config('queue.default'),
                'mail_driver' => config('mail.default'),
            ];
        });
    }

    /**
     * Get system health data.
     */
    private function getSystemHealth(): array
    {
        return [
            'database' => $this->checkDatabaseHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
            'queue' => $this->checkQueueHealth(),
            'memory' => $this->getMemoryUsage(),
        ];
    }

    /**
     * Check database health.
     */
    private function checkDatabaseHealth(): array
    {
        try {
            DB::connection()->getPdo();
            $version = $this->getDatabaseVersion();
            return [
                'status' => 'healthy',
                'message' => 'Database connection is operational',
                'version' => $version,
                'response_time' => '< 10ms',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check storage health.
     */
    private function checkStorageHealth(): array
    {
        try {
            $disk = Storage::disk('public');
            $testFile = 'health-check-' . time() . '.txt';
            $disk->put($testFile, 'health check');
            $disk->delete($testFile);
            
            return [
                'status' => 'healthy',
                'message' => 'Storage is operational',
                'disk' => 'public',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Storage check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache health.
     */
    private function checkCacheHealth(): array
    {
        try {
            $key = 'health_check_' . time();
            Cache::put($key, 'test', 60);
            $value = Cache::get($key);
            Cache::forget($key);
            
            return [
                'status' => $value === 'test' ? 'healthy' : 'warning',
                'message' => $value === 'test' ? 'Cache is operational' : 'Cache test failed',
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue health.
     */
    private function checkQueueHealth(): array
    {
        try {
            return [
                'status' => 'healthy',
                'message' => 'Queue system is operational',
                'driver' => config('queue.default'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Queue check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get memory usage information.
     */
    private function getMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->convertToBytes(ini_get('memory_limit'));
        $percentage = $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0;

        return [
            'current' => $this->formatBytes($memoryUsage),
            'limit' => ini_get('memory_limit'),
            'percentage' => $percentage,
            'status' => $percentage > 80 ? 'warning' : 'healthy',
        ];
    }

    /**
     * Get disk usage information.
     */
    private function getDiskUsage(): array
    {
        $bytes = disk_free_space('/');
        $total = disk_total_space('/');
        $used = $total - $bytes;
        $percentage = $total > 0 ? round(($used / $total) * 100, 2) : 0;

        return [
            'free' => $this->formatBytes($bytes),
            'used' => $this->formatBytes($used),
            'total' => $this->formatBytes($total),
            'percentage' => $percentage,
            'status' => $percentage > 90 ? 'warning' : 'healthy',
        ];
    }

    /**
     * Get database version.
     */
    private function getDatabaseVersion(): string
    {
        try {
            return DB::select('SELECT VERSION() as version')[0]->version ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Convert memory string to bytes.
     */
    private function convertToBytes(string $value): int
    {
        $value = trim($value);
        $last = strtolower($value[strlen($value) - 1]);
        $value = (int) $value;

        switch ($last) {
            case 'g':
                $value *= 1024;
            case 'm':
                $value *= 1024;
            case 'k':
                $value *= 1024;
        }

        return $value;
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
