<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class UserManagementController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request): Response
    {
        $query = User::with(['roles'])
            ->when($request->search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
            })
            ->when($request->role, function ($query, $role) {
                $query->whereHas('roles', function ($q) use ($role) {
                    $q->where('name', $role);
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            });

        $users = $query->latest()->paginate(15)->withQueryString();
        $roles = Role::all();

        return Inertia::render('super-admin/users/index', [
            'users' => $users,
            'roles' => $roles,
            'filters' => $request->only(['search', 'role', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new user.
     */
    public function create(): Response
    {
        $roles = Role::all();
        
        return Inertia::render('super-admin/users/create', [
            'roles' => $roles,
        ]);
    }

    /**
     * Store a newly created user in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => bcrypt($validated['password']),
            'status' => $validated['status'],
            'email_verified_at' => now(), // Super admin created users are auto-verified
        ]);

        if (isset($validated['roles'])) {
            $user->roles()->sync($validated['roles']);
        }

        return redirect()->route('super-admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user): Response
    {
        $user->load(['roles.permissions']);
        
        return Inertia::render('super-admin/users/show', [
            'user' => $user,
        ]);
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user): Response
    {
        $user->load('roles');
        $roles = Role::all();
        
        return Inertia::render('super-admin/users/edit', [
            'user' => $user,
            'roles' => $roles,
        ]);
    }

    /**
     * Update the specified user in storage.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => 'nullable|string|min:8|confirmed',
            'roles' => 'array',
            'roles.*' => 'exists:roles,id',
            'status' => 'required|in:active,inactive,suspended',
        ]);

        $updateData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'status' => $validated['status'],
        ];

        if (!empty($validated['password'])) {
            $updateData['password'] = bcrypt($validated['password']);
        }

        $user->update($updateData);

        if (isset($validated['roles'])) {
            $user->roles()->sync($validated['roles']);
        }

        return redirect()->route('super-admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        // Prevent deletion of the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('super-admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        // Prevent deletion of other super admins (optional safety measure)
        if ($user->hasRole('super_admin') && $user->id !== auth()->id()) {
            return redirect()->route('super-admin.users.index')
                ->with('error', 'Cannot delete other super admin accounts.');
        }

        $user->delete();

        return redirect()->route('super-admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Assign role to user.
     */
    public function assignRole(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'role_id' => 'required|exists:roles,id',
        ]);

        $role = Role::find($validated['role_id']);
        
        if (!$user->hasRole($role->name)) {
            $user->assignRole($role);
            $message = "Role '{$role->name}' assigned successfully.";
        } else {
            $message = "User already has the '{$role->name}' role.";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Remove role from user.
     */
    public function removeRole(Request $request, User $user): RedirectResponse
    {
        $validated = $request->validate([
            'role_id' => 'required|exists:roles,id',
        ]);

        $role = Role::find($validated['role_id']);
        
        // Prevent removing super_admin role from the current user
        if ($role->name === 'super_admin' && $user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot remove your own super admin role.');
        }

        if ($user->hasRole($role->name)) {
            $user->removeRole($role);
            $message = "Role '{$role->name}' removed successfully.";
        } else {
            $message = "User does not have the '{$role->name}' role.";
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Handle bulk actions on users.
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,suspend,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $users = User::whereIn('id', $validated['user_ids'])->get();
        $currentUserId = auth()->id();

        foreach ($users as $user) {
            // Skip current user for safety
            if ($user->id === $currentUserId) {
                continue;
            }

            switch ($validated['action']) {
                case 'activate':
                    $user->update(['status' => 'active']);
                    break;
                case 'suspend':
                    $user->update(['status' => 'suspended']);
                    break;
                case 'delete':
                    // Don't delete super admins
                    if (!$user->hasRole('super_admin')) {
                        $user->delete();
                    }
                    break;
            }
        }

        $actionName = ucfirst($validated['action']);
        return redirect()->route('super-admin.users.index')
            ->with('success', "{$actionName} action completed successfully.");
    }

    /**
     * Impersonate a user.
     */
    public function impersonate(Request $request, User $user): RedirectResponse
    {
        // Prevent impersonating super admins
        if ($user->hasRole('super_admin')) {
            return redirect()->back()
                ->with('error', 'Cannot impersonate super admin users.');
        }

        // Store original user ID in session
        session(['impersonating_user_id' => auth()->id()]);
        
        // Login as the target user
        auth()->login($user);

        return redirect()->route('dashboard')
            ->with('success', "Now impersonating {$user->name}");
    }

    /**
     * Suspend a user.
     */
    public function suspend(User $user): RedirectResponse
    {
        if ($user->id === auth()->id()) {
            return redirect()->back()
                ->with('error', 'You cannot suspend your own account.');
        }

        $user->update(['status' => 'suspended']);

        return redirect()->back()
            ->with('success', 'User suspended successfully.');
    }

    /**
     * Activate a user.
     */
    public function activate(User $user): RedirectResponse
    {
        $user->update(['status' => 'active']);

        return redirect()->back()
            ->with('success', 'User activated successfully.');
    }

    /**
     * Display user sessions.
     */
    public function sessions(Request $request): Response
    {
        // Mock sessions data - in real implementation, you'd track user sessions
        $sessions = collect([
            [
                'id' => 1,
                'user_id' => 1,
                'user_name' => 'John Doe',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'last_activity' => now()->subMinutes(5),
                'is_current' => true,
            ],
            [
                'id' => 2,
                'user_id' => 2,
                'user_name' => 'Jane Smith',
                'ip_address' => '***********',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'last_activity' => now()->subHours(2),
                'is_current' => false,
            ],
        ]);

        return Inertia::render('super-admin/sessions/index', [
            'sessions' => $sessions,
        ]);
    }

    /**
     * Destroy a user session.
     */
    public function destroySession(Request $request, $sessionId): RedirectResponse
    {
        // In real implementation, you'd destroy the actual session
        
        return redirect()->route('super-admin.sessions.index')
            ->with('success', 'Session terminated successfully.');
    }

    /**
     * Bulk destroy sessions.
     */
    public function bulkDestroySessions(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'session_ids' => 'required|array',
            'session_ids.*' => 'required',
        ]);

        // In real implementation, you'd destroy the actual sessions
        
        return redirect()->route('super-admin.sessions.index')
            ->with('success', 'Sessions terminated successfully.');
    }
}
