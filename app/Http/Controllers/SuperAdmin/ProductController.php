<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class ProductController extends Controller
{
    /**
     * Display a listing of products.
     */
    public function index(Request $request): Response
    {
        $query = Product::with(['category', 'creator'])
            ->withCount(['files', 'orderItems']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('sku', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $query->where('category_id', $request->get('category'));
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Featured filter
        if ($request->filled('featured')) {
            $query->where('featured', $request->boolean('featured'));
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // Creator filter (Super Admin specific)
        if ($request->filled('creator')) {
            $query->where('created_by', $request->get('creator'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'price', 'created_at', 'updated_at', 'status'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->latest();
        }

        $products = $query->paginate(15)->withQueryString();

        $categories = ProductCategory::select('id', 'name')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        $creators = User::select('id', 'name')
            ->whereHas('createdProducts')
            ->orderBy('name')
            ->get();

        return Inertia::render('super-admin/products/index', [
            'products' => $products,
            'categories' => $categories,
            'creators' => $creators,
            'filters' => $request->only(['search', 'category', 'status', 'featured', 'min_price', 'max_price', 'creator']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Show the form for creating a new product.
     */
    public function create(): Response
    {
        $categories = ProductCategory::select('id', 'name', 'parent_id')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('super-admin/products/create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created product in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'nullable|exists:product_categories,id',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => 'nullable|string|max:100|unique:products,sku',
            'status' => ['required', Rule::in(['active', 'inactive', 'draft'])],
            'featured' => 'boolean',
            'digital' => 'boolean',
            'downloadable' => 'boolean',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
            'image' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
        ]);

        $validated['created_by'] = auth()->id();
        $validated['slug'] = \Str::slug($validated['name']);

        $product = Product::create($validated);

        return redirect()->route('super-admin.products.index')
            ->with('success', 'Product created successfully.');
    }

    /**
     * Display the specified product.
     */
    public function show(Product $product): Response
    {
        $product->load([
            'category',
            'creator',
            'files' => function ($query) {
                $query->orderBy('created_at', 'desc');
            },
            'orderItems.order'
        ]);

        return Inertia::render('super-admin/products/show', [
            'product' => $product,
        ]);
    }

    /**
     * Show the form for editing the specified product.
     */
    public function edit(Product $product): Response
    {
        $categories = ProductCategory::select('id', 'name', 'parent_id')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('super-admin/products/edit', [
            'product' => $product,
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified product in storage.
     */
    public function update(Request $request, Product $product): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'nullable|exists:product_categories,id',
            'description' => 'nullable|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0',
            'sale_price' => 'nullable|numeric|min:0|lt:price',
            'sku' => ['nullable', 'string', 'max:100', Rule::unique('products')->ignore($product->id)],
            'status' => ['required', Rule::in(['active', 'inactive', 'draft'])],
            'featured' => 'boolean',
            'digital' => 'boolean',
            'downloadable' => 'boolean',
            'download_limit' => 'nullable|integer|min:1',
            'download_expiry_days' => 'nullable|integer|min:1',
            'image' => 'nullable|string|max:255',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'tags' => 'nullable|array',
            'tags.*' => 'string|max:50',
        ]);

        if ($validated['name'] !== $product->name) {
            $validated['slug'] = \Str::slug($validated['name']);
        }

        $product->update($validated);

        return redirect()->route('super-admin.products.index')
            ->with('success', 'Product updated successfully.');
    }

    /**
     * Remove the specified product from storage.
     */
    public function destroy(Product $product): RedirectResponse
    {
        // Check if product has orders
        if ($product->orderItems()->count() > 0) {
            return redirect()->route('super-admin.products.index')
                ->with('error', 'Cannot delete product that has associated orders.');
        }

        $product->delete();

        return redirect()->route('super-admin.products.index')
            ->with('success', 'Product deleted successfully.');
    }

    /**
     * Handle bulk actions on products.
     */
    public function bulkAction(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,feature,unfeature,delete',
            'product_ids' => 'required|array',
            'product_ids.*' => 'exists:products,id',
        ]);

        $products = Product::whereIn('id', $validated['product_ids'])->get();

        foreach ($products as $product) {
            switch ($validated['action']) {
                case 'activate':
                    $product->update(['status' => 'active']);
                    break;
                case 'deactivate':
                    $product->update(['status' => 'inactive']);
                    break;
                case 'feature':
                    $product->update(['featured' => true]);
                    break;
                case 'unfeature':
                    $product->update(['featured' => false]);
                    break;
                case 'delete':
                    // Only delete if no orders
                    if ($product->orderItems()->count() === 0) {
                        $product->delete();
                    }
                    break;
            }
        }

        $actionName = ucfirst($validated['action']);
        return redirect()->route('super-admin.products.index')
            ->with('success', "{$actionName} action completed successfully.");
    }
}
