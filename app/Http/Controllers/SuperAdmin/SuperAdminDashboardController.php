<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Services\DashboardService;
use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;
use Inertia\Response;

class SuperAdminDashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the super admin dashboard.
     */
    public function index(Request $request): Response
    {
        $dashboardData = $this->dashboardService->getDashboardData($request->user());
        $systemHealth = $this->getSystemHealth();
        $superAdminStats = $this->getSuperAdminStats();

        return Inertia::render('super-admin/dashboard', [
            'dashboardData' => $dashboardData,
            'systemHealth' => $systemHealth,
            'superAdminStats' => $superAdminStats,
        ]);
    }

    /**
     * Get super admin dashboard data via API.
     */
    public function data(Request $request)
    {
        $dashboardData = $this->dashboardService->getDashboardData($request->user());
        $superAdminStats = $this->getSuperAdminStats();

        return response()->json([
            'dashboardData' => $dashboardData,
            'superAdminStats' => $superAdminStats,
        ]);
    }

    /**
     * Get system health information.
     */
    public function systemHealth()
    {
        $health = $this->getSystemHealth();
        return response()->json($health);
    }

    /**
     * Get comprehensive system health data.
     */
    private function getSystemHealth(): array
    {
        return Cache::remember('system_health', 60, function () {
            return [
                'database' => $this->checkDatabaseHealth(),
                'storage' => $this->checkStorageHealth(),
                'cache' => $this->checkCacheHealth(),
                'queue' => $this->checkQueueHealth(),
                'memory' => $this->getMemoryUsage(),
                'disk' => $this->getDiskUsage(),
            ];
        });
    }

    /**
     * Get super admin specific statistics.
     */
    private function getSuperAdminStats(): array
    {
        return Cache::remember('super_admin_stats', 300, function () {
            return [
                'total_users' => User::count(),
                'active_users' => User::where('status', 'active')->count(),
                'suspended_users' => User::where('status', 'suspended')->count(),
                'total_roles' => Role::count(),
                'total_permissions' => Permission::count(),
                'admin_users' => User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['admin', 'super_admin']);
                })->count(),
                'recent_logins' => User::whereNotNull('last_login_at')
                    ->where('last_login_at', '>=', now()->subDays(7))
                    ->count(),
                'failed_login_attempts' => User::where('failed_login_attempts', '>', 0)->count(),
                'locked_accounts' => User::whereNotNull('locked_until')
                    ->where('locked_until', '>', now())
                    ->count(),
            ];
        });
    }

    /**
     * Check database connectivity and performance.
     */
    private function checkDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $responseTime = round((microtime(true) - $start) * 1000, 2);
            
            return [
                'status' => 'healthy',
                'response_time' => $responseTime . 'ms',
                'connection' => 'active',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'connection' => 'failed',
            ];
        }
    }

    /**
     * Check storage health.
     */
    private function checkStorageHealth(): array
    {
        try {
            $storagePath = storage_path();
            $freeBytes = disk_free_space($storagePath);
            $totalBytes = disk_total_space($storagePath);
            $usedBytes = $totalBytes - $freeBytes;
            $usagePercentage = round(($usedBytes / $totalBytes) * 100, 2);

            return [
                'status' => $usagePercentage < 90 ? 'healthy' : 'warning',
                'usage_percentage' => $usagePercentage,
                'free_space' => $this->formatBytes($freeBytes),
                'total_space' => $this->formatBytes($totalBytes),
                'used_space' => $this->formatBytes($usedBytes),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache health.
     */
    private function checkCacheHealth(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            return [
                'status' => $retrieved === $testValue ? 'healthy' : 'unhealthy',
                'driver' => config('cache.default'),
                'test_result' => $retrieved === $testValue ? 'passed' : 'failed',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'driver' => config('cache.default'),
            ];
        }
    }

    /**
     * Check queue health.
     */
    private function checkQueueHealth(): array
    {
        try {
            return [
                'status' => 'healthy',
                'driver' => config('queue.default'),
                'connection' => 'active',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'driver' => config('queue.default'),
            ];
        }
    }

    /**
     * Get memory usage information.
     */
    private function getMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        
        return [
            'current' => $this->formatBytes($memoryUsage),
            'peak' => $this->formatBytes($memoryPeak),
            'limit' => $this->formatBytes($memoryLimit),
            'usage_percentage' => $memoryLimit > 0 ? round(($memoryUsage / $memoryLimit) * 100, 2) : 0,
        ];
    }

    /**
     * Get disk usage information.
     */
    private function getDiskUsage(): array
    {
        $path = base_path();
        $freeBytes = disk_free_space($path);
        $totalBytes = disk_total_space($path);
        $usedBytes = $totalBytes - $freeBytes;
        
        return [
            'free' => $this->formatBytes($freeBytes),
            'total' => $this->formatBytes($totalBytes),
            'used' => $this->formatBytes($usedBytes),
            'usage_percentage' => round(($usedBytes / $totalBytes) * 100, 2),
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Parse memory limit string to bytes.
     */
    private function parseMemoryLimit($memoryLimit): int
    {
        if ($memoryLimit === '-1') {
            return PHP_INT_MAX;
        }
        
        $unit = strtolower(substr($memoryLimit, -1));
        $value = (int) substr($memoryLimit, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $memoryLimit;
        }
    }
}
