<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\SystemSetting;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class OptionsController extends Controller
{
    /**
     * Display the S3 integration settings.
     */
    public function s3Integration(Request $request): Response
    {
        $s3Settings = $this->getS3Settings();
        
        return Inertia::render('super-admin/options/s3-integration', [
            'settings' => $s3Settings,
        ]);
    }

    /**
     * Update S3 integration settings.
     */
    public function updateS3Integration(Request $request): RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'aws_access_key_id' => 'nullable|string|min:16|max:128|regex:/^AKIA[0-9A-Z]{16}$/',
            'aws_secret_access_key' => 'nullable|string|min:40|max:40',
            'aws_default_region' => 'nullable|string|max:50|regex:/^[a-z0-9-]+$/',
            'aws_bucket' => 'nullable|string|min:3|max:63|regex:/^[a-z0-9.-]+$/',
            'aws_digital_assets_bucket' => 'nullable|string|min:3|max:63|regex:/^[a-z0-9.-]+$/',
            'aws_url' => 'nullable|url|max:255',
            'aws_endpoint' => 'nullable|url|max:255',
            'aws_use_path_style_endpoint' => 'boolean',
            'digital_assets_driver' => ['required', Rule::in(['local', 's3'])],
            's3_enabled' => 'boolean',
        ], [
            'aws_access_key_id.regex' => 'AWS Access Key ID must start with AKIA and be 20 characters long.',
            'aws_secret_access_key.min' => 'AWS Secret Access Key must be exactly 40 characters long.',
            'aws_secret_access_key.max' => 'AWS Secret Access Key must be exactly 40 characters long.',
            'aws_default_region.regex' => 'AWS Region must contain only lowercase letters, numbers, and hyphens.',
            'aws_bucket.regex' => 'Bucket name must contain only lowercase letters, numbers, dots, and hyphens.',
            'aws_bucket.min' => 'Bucket name must be at least 3 characters long.',
            'aws_bucket.max' => 'Bucket name must not exceed 63 characters.',
            'aws_digital_assets_bucket.regex' => 'Digital assets bucket name must contain only lowercase letters, numbers, dots, and hyphens.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $validated = $validator->validated();

        try {
            // Check if AWS SDK is available when S3 is enabled
            if ($validated['s3_enabled'] ?? false) {
                if (!class_exists('\Aws\S3\S3Client')) {
                    return back()
                        ->withErrors(['s3_enabled' => 'AWS SDK for PHP is not installed. Please install it using: composer require aws/aws-sdk-php'])
                        ->withInput();
                }

                // Validate required fields when S3 is enabled
                $requiredFields = ['aws_access_key_id', 'aws_secret_access_key', 'aws_default_region', 'aws_bucket'];
                foreach ($requiredFields as $field) {
                    if (empty($validated[$field])) {
                        return back()
                            ->withErrors([$field => "This field is required when S3 integration is enabled."])
                            ->withInput();
                    }
                }

                // Test the S3 configuration before saving
                $testResult = $this->performS3ConnectionTest($validated);
                if (!$testResult['success']) {
                    return back()
                        ->withErrors(['s3_connection' => 'S3 configuration test failed: ' . $testResult['message']])
                        ->withInput();
                }
            }

            // Update each setting
            foreach ($validated as $key => $value) {
                $this->updateSetting($key, $value, $request->user());
            }

            $successMessage = 'S3 integration settings updated successfully.';
            if ($validated['s3_enabled'] ?? false) {
                $successMessage .= ' Connection test passed.';
            }

            return redirect()
                ->route('super-admin.options.s3-integration')
                ->with('success', $successMessage);

        } catch (\Exception $e) {
            \Log::error('S3 Integration settings update failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => $request->user()?->id,
            ]);

            return back()
                ->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Test S3 connection endpoint.
     */
    public function testS3Connection(Request $request)
    {
        $result = $this->performS3ConnectionTest();
        return response()->json($result);
    }

    /**
     * Test S3 connection with provided settings.
     */
    private function performS3ConnectionTest(array $settings = null): array
    {
        try {
            if (!$settings) {
                $settings = $this->getS3Settings();
            }

            // Validate required settings
            $requiredSettings = ['aws_access_key_id', 'aws_secret_access_key', 'aws_default_region', 'aws_bucket'];
            foreach ($requiredSettings as $setting) {
                if (empty($settings[$setting])) {
                    return [
                        'success' => false,
                        'message' => "Missing required setting: {$setting}",
                        'details' => ['missing_setting' => $setting]
                    ];
                }
            }

            // Create S3 client with provided credentials
            $s3Config = [
                'version' => 'latest',
                'region' => $settings['aws_default_region'],
                'credentials' => [
                    'key' => $settings['aws_access_key_id'],
                    'secret' => $settings['aws_secret_access_key'],
                ],
            ];

            // Add custom endpoint if provided
            if (!empty($settings['aws_endpoint'])) {
                $s3Config['endpoint'] = $settings['aws_endpoint'];
            }

            // Add custom URL if provided
            if (!empty($settings['aws_url'])) {
                $s3Config['url'] = $settings['aws_url'];
            }

            // Add path style endpoint setting
            if ($settings['aws_use_path_style_endpoint']) {
                $s3Config['use_path_style_endpoint'] = true;
            }

            $s3Client = new \Aws\S3\S3Client($s3Config);

            // Test connection using HeadBucket operation (AWS best practice)
            $result = $s3Client->headBucket([
                'Bucket' => $settings['aws_bucket']
            ]);

            return [
                'success' => true,
                'message' => 'S3 connection successful. Bucket is accessible.',
                'details' => [
                    'bucket' => $settings['aws_bucket'],
                    'region' => $settings['aws_default_region'],
                    'http_status' => $result['@metadata']['statusCode'] ?? 200,
                    'request_id' => $result['@metadata']['headers']['x-amz-request-id'] ?? null,
                ]
            ];

        } catch (\Aws\S3\Exception\S3Exception $e) {
            // Handle S3-specific exceptions
            $errorCode = $e->getAwsErrorCode();
            $statusCode = $e->getStatusCode();

            $message = match($errorCode) {
                'NoSuchBucket' => 'The specified bucket does not exist.',
                'AccessDenied' => 'Access denied. Check your credentials and bucket permissions.',
                'InvalidAccessKeyId' => 'The AWS Access Key ID you provided is invalid.',
                'SignatureDoesNotMatch' => 'The AWS Secret Access Key you provided is invalid.',
                'TokenRefreshRequired' => 'The provided token needs to be refreshed.',
                'InvalidBucketName' => 'The bucket name is invalid.',
                'BucketAlreadyOwnedByYou' => 'Bucket exists and you have access to it.',
                default => "S3 Error ({$errorCode}): " . $e->getAwsErrorMessage()
            };

            return [
                'success' => false,
                'message' => $message,
                'details' => [
                    'error_code' => $errorCode,
                    'status_code' => $statusCode,
                    'request_id' => $e->getAwsRequestId(),
                    'error_type' => $e->getAwsErrorType(),
                ]
            ];

        } catch (\Aws\Exception\AwsException $e) {
            // Handle general AWS exceptions
            return [
                'success' => false,
                'message' => 'AWS Error: ' . $e->getAwsErrorMessage(),
                'details' => [
                    'error_code' => $e->getAwsErrorCode(),
                    'error_type' => $e->getAwsErrorType(),
                    'request_id' => $e->getAwsRequestId(),
                ]
            ];

        } catch (\Exception $e) {
            // Handle any other exceptions
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
                'details' => [
                    'error_type' => get_class($e),
                    'error_code' => $e->getCode(),
                ]
            ];
        }
    }

    /**
     * Get current S3 settings.
     */
    private function getS3Settings(): array
    {
        // Use try-catch to handle potential decryption errors
        $getSetting = function($key, $default = '') {
            try {
                return SystemSetting::get($key, $default);
            } catch (\Exception $e) {
                // If decryption fails, return default and log the issue
                \Log::warning("Failed to decrypt setting: {$key}", ['error' => $e->getMessage()]);
                return $default;
            }
        };

        return [
            'aws_access_key_id' => $getSetting('aws_access_key_id', ''),
            'aws_secret_access_key' => $getSetting('aws_secret_access_key', ''),
            'aws_default_region' => $getSetting('aws_default_region', 'us-east-1'),
            'aws_bucket' => $getSetting('aws_bucket', ''),
            'aws_digital_assets_bucket' => $getSetting('aws_digital_assets_bucket', ''),
            'aws_url' => $getSetting('aws_url', ''),
            'aws_endpoint' => $getSetting('aws_endpoint', ''),
            'aws_use_path_style_endpoint' => $getSetting('aws_use_path_style_endpoint', false),
            'digital_assets_driver' => $getSetting('digital_assets_driver', 'local'),
            's3_enabled' => $getSetting('s3_enabled', false),
        ];
    }

    /**
     * Update a system setting.
     */
    private function updateSetting(string $key, $value, $user = null): void
    {
        // Determine if this setting should be encrypted
        $encryptedSettings = [
            'aws_access_key_id',
            'aws_secret_access_key',
        ];

        $setting = SystemSetting::firstOrNew(['key' => $key]);
        $setting->value = $value;
        $setting->type = $this->determineSettingType($value);
        $setting->group = $this->getSettingGroup($key);
        $setting->is_encrypted = in_array($key, $encryptedSettings);
        $setting->updated_by = $user?->id;
        $setting->save();
    }

    /**
     * Determine the type of a setting value.
     */
    private function determineSettingType($value): string
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'array';
        } else {
            return 'string';
        }
    }

    /**
     * Get the group for a setting key.
     */
    private function getSettingGroup(string $key): string
    {
        $groups = [
            'aws_' => 's3',
            'digital_assets_' => 's3',
            's3_' => 's3',
        ];

        foreach ($groups as $prefix => $group) {
            if (str_starts_with($key, $prefix)) {
                return $group;
            }
        }

        return 'general';
    }

    /**
     * Display system options.
     */
    public function systemOptions(Request $request): Response
    {
        return Inertia::render('super-admin/options/system-options');
    }

    /**
     * Display interface template options.
     */
    public function interfaceTemplate(Request $request): Response
    {
        return Inertia::render('super-admin/options/interface-template');
    }

    /**
     * Display dashboard template options.
     */
    public function dashboardTemplate(Request $request): Response
    {
        return Inertia::render('super-admin/options/dashboard-template');
    }

    /**
     * Display email/SMTP options.
     */
    public function emailSmtp(Request $request): Response
    {
        return Inertia::render('super-admin/options/email-smtp');
    }

    /**
     * Display download options.
     */
    public function download(Request $request): Response
    {
        return Inertia::render('super-admin/options/download');
    }

    /**
     * Display cron tasks options.
     */
    public function cronTasks(Request $request): Response
    {
        return Inertia::render('super-admin/options/cron-tasks');
    }
}
