<?php

namespace App\Http\Controllers;

use App\Services\DashboardService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class DashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the main dashboard.
     */
    public function index(Request $request)
    {
        $user = $request->user()->load('roles');

        // Redirect to appropriate dashboard based on role
        if ($user->hasRole('super_admin')) {
            return redirect()->route('super-admin.dashboard');
        }

        if ($user->hasRole('admin')) {
            return redirect()->route('admin.dashboard');
        }

        // Regular user dashboard (including users with no roles or 'user' role)
        $dashboardData = $this->dashboardService->getDashboardData($user);

        return Inertia::render('dashboard', [
            'dashboardData' => $dashboardData,
        ]);
    }

    /**
     * Get dashboard data via API.
     */
    public function data(Request $request)
    {
        $dashboardData = $this->dashboardService->getDashboardData($request->user());

        return response()->json($dashboardData);
    }

    /**
     * Get recent activities.
     */
    public function activities(Request $request)
    {
        $activities = $this->dashboardService->getRecentActivities($request->user());

        return response()->json($activities);
    }

    /**
     * Get transaction summaries.
     */
    public function transactions(Request $request)
    {
        $transactions = $this->dashboardService->getTransactionSummaries($request->user());

        return response()->json($transactions);
    }
}
