<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\DownloadToken;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CustomerController extends Controller
{
    /**
     * Display the customer dashboard.
     */
    public function dashboard(Request $request): Response
    {
        $user = $request->user();

        // Get recent orders
        $recentOrders = Order::with(['items.product'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get download statistics
        $downloadStats = [
            'total_downloads' => DownloadToken::where('user_id', $user->id)->count(),
            'active_downloads' => DownloadToken::where('user_id', $user->id)
                ->where('is_active', true)
                ->where('expires_at', '>', now())
                ->count(),
            'expired_downloads' => DownloadToken::where('user_id', $user->id)
                ->where('expires_at', '<=', now())
                ->count(),
        ];

        // Get order statistics
        $orderStats = [
            'total_orders' => Order::where('user_id', $user->id)->count(),
            'completed_orders' => Order::where('user_id', $user->id)
                ->where('status', 'completed')
                ->count(),
            'pending_orders' => Order::where('user_id', $user->id)
                ->where('status', 'pending')
                ->count(),
            'total_spent' => Order::where('user_id', $user->id)
                ->where('status', 'completed')
                ->sum('total_amount'),
        ];

        return Inertia::render('customer/dashboard', [
            'user' => $user,
            'recentOrders' => $recentOrders,
            'downloadStats' => $downloadStats,
            'orderStats' => $orderStats,
        ]);
    }

    /**
     * Display customer orders.
     */
    public function orders(Request $request): Response
    {
        $user = $request->user();

        $query = Order::with(['items.product'])
            ->where('user_id', $user->id);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by date range
        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->get('from_date'));
        }
        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->get('to_date'));
        }

        // Search by order number
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where('order_number', 'like', "%{$search}%");
        }

        $orders = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('customer/orders', [
            'orders' => $orders,
            'filters' => $request->only(['status', 'from_date', 'to_date', 'search']),
        ]);
    }

    /**
     * Display a specific order.
     */
    public function order(Request $request, string $orderNumber): Response
    {
        $user = $request->user();

        $order = Order::with([
            'items.product.files',
            'downloadTokens' => function ($query) {
                $query->where('is_active', true);
            }
        ])
        ->where('user_id', $user->id)
        ->where('order_number', $orderNumber)
        ->firstOrFail();

        return Inertia::render('customer/order', [
            'order' => $order,
        ]);
    }

    /**
     * Display customer downloads.
     */
    public function downloads(Request $request): Response
    {
        $user = $request->user();

        $query = DownloadToken::with([
            'digitalAsset.product',
            'order'
        ])
        ->where('user_id', $user->id);

        // Filter by status
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true)
                      ->where('expires_at', '>', now());
            } elseif ($status === 'expired') {
                $query->where('expires_at', '<=', now());
            } elseif ($status === 'used') {
                $query->where('download_count', '>', 0);
            }
        }

        // Search by product name
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('digitalAsset.product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        $downloads = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('customer/downloads', [
            'downloads' => $downloads,
            'filters' => $request->only(['status', 'search']),
        ]);
    }

    /**
     * Display customer profile.
     */
    public function profile(Request $request): Response
    {
        $user = $request->user();

        return Inertia::render('customer/profile', [
            'user' => $user,
        ]);
    }

    /**
     * Update customer profile.
     */
    public function updateProfile(Request $request): \Illuminate\Http\RedirectResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
        ]);

        $user->update($validated);

        return redirect()->back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Display customer support page.
     */
    public function support(Request $request): Response
    {
        return Inertia::render('customer/support', [
            'user' => $request->user(),
        ]);
    }

    /**
     * Submit a support ticket.
     */
    public function submitSupportTicket(Request $request): \Illuminate\Http\RedirectResponse
    {
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:2000',
            'priority' => 'required|in:low,medium,high',
            'category' => 'required|in:technical,billing,general',
        ]);

        // Here you would typically create a support ticket
        // For now, we'll just return a success message
        
        return redirect()->back()->with('success', 'Support ticket submitted successfully. We will get back to you soon.');
    }

    /**
     * Get customer statistics for API.
     */
    public function statistics(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();

        $stats = [
            'orders' => [
                'total' => Order::where('user_id', $user->id)->count(),
                'completed' => Order::where('user_id', $user->id)->where('status', 'completed')->count(),
                'pending' => Order::where('user_id', $user->id)->where('status', 'pending')->count(),
                'total_spent' => Order::where('user_id', $user->id)->where('status', 'completed')->sum('total_amount'),
            ],
            'downloads' => [
                'total' => DownloadToken::where('user_id', $user->id)->count(),
                'active' => DownloadToken::where('user_id', $user->id)
                    ->where('is_active', true)
                    ->where('expires_at', '>', now())
                    ->count(),
                'used' => DownloadToken::where('user_id', $user->id)
                    ->where('download_count', '>', 0)
                    ->count(),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get recent activity for customer.
     */
    public function recentActivity(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = $request->user();

        $recentOrders = Order::with(['items.product'])
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentDownloads = DownloadToken::with(['digitalAsset.product'])
            ->where('user_id', $user->id)
            ->where('download_count', '>', 0)
            ->orderBy('used_at', 'desc')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'orders' => $recentOrders,
                'downloads' => $recentDownloads,
            ],
        ]);
    }
}
