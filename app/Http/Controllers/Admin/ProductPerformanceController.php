<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ProductPerformanceService;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class ProductPerformanceController extends Controller
{
    protected ProductPerformanceService $performanceService;

    public function __construct(ProductPerformanceService $performanceService)
    {
        $this->performanceService = $performanceService;
    }

    /**
     * Display product performance dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $metric = $request->get('metric', 'revenue');
        $limit = $request->get('limit', 20);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $topProducts = $this->performanceService->getTopPerformingProducts($days, $limit, $metric);
        $summary = $this->performanceService->getPerformanceSummary($days);

        return Inertia::render('super-admin/analytics/products', [
            'topProducts' => $topProducts,
            'summary' => $summary,
            'days' => $days,
            'metric' => $metric,
            'availableMetrics' => [
                'revenue' => 'Revenue',
                'sales' => 'Units Sold',
                'views' => 'Views',
                'downloads' => 'Downloads',
                'conversion' => 'Conversion Rate',
            ]
        ]);
    }

    /**
     * Get detailed performance for a specific product.
     */
    public function show(Request $request, Product $product): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $performance = $this->performanceService->getProductPerformance($product->id, $days);

        return response()->json($performance);
    }

    /**
     * Get top performing products.
     */
    public function topProducts(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $metric = $request->get('metric', 'revenue');
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $products = $this->performanceService->getTopPerformingProducts($days, $limit, $metric);

        return response()->json([
            'products' => $products,
            'metric' => $metric,
            'days' => $days
        ]);
    }

    /**
     * Compare multiple products.
     */
    public function compare(Request $request): JsonResponse
    {
        $request->validate([
            'product_ids' => 'required|array|min:2|max:5',
            'product_ids.*' => 'exists:products,id',
            'days' => 'integer|min:1|max:365',
        ]);

        $days = $request->get('days', 30);
        $productIds = $request->get('product_ids');

        $comparison = $this->performanceService->compareProducts($productIds, $days);

        return response()->json([
            'comparison' => $comparison,
            'days' => $days
        ]);
    }

    /**
     * Get performance summary.
     */
    public function summary(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        $days = min(max($days, 1), 30);

        $summary = $this->performanceService->getPerformanceSummary($days);

        return response()->json($summary);
    }

    /**
     * Get product performance trends.
     */
    public function trends(Request $request, Product $product): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $performance = $this->performanceService->getProductPerformance($product->id, $days);

        return response()->json([
            'trends' => $performance['trends'],
            'product' => $product,
            'days' => $days
        ]);
    }

    /**
     * Get conversion funnel for a product.
     */
    public function conversionFunnel(Request $request, Product $product): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $performance = $this->performanceService->getProductPerformance($product->id, $days);
        
        $funnel = [
            [
                'stage' => 'Views',
                'count' => $performance['views']['total_views'],
                'percentage' => 100,
            ],
            [
                'stage' => 'Unique Viewers',
                'count' => $performance['views']['unique_viewers'],
                'percentage' => $performance['views']['total_views'] > 0 ? 
                    ($performance['views']['unique_viewers'] / $performance['views']['total_views']) * 100 : 0,
            ],
            [
                'stage' => 'Wishlist Additions',
                'count' => $performance['wishlist']['total_additions'],
                'percentage' => $performance['views']['total_views'] > 0 ? 
                    ($performance['wishlist']['total_additions'] / $performance['views']['total_views']) * 100 : 0,
            ],
            [
                'stage' => 'Purchases',
                'count' => $performance['sales']['total_sold'],
                'percentage' => $performance['views']['total_views'] > 0 ? 
                    ($performance['sales']['total_sold'] / $performance['views']['total_views']) * 100 : 0,
            ],
        ];

        if ($product->digital && $product->downloadable) {
            $funnel[] = [
                'stage' => 'Downloads',
                'count' => $performance['downloads']['total_downloads'],
                'percentage' => $performance['sales']['total_sold'] > 0 ? 
                    ($performance['downloads']['total_downloads'] / $performance['sales']['total_sold']) * 100 : 0,
            ];
        }

        return response()->json([
            'funnel' => $funnel,
            'product' => $product,
            'days' => $days
        ]);
    }

    /**
     * Get product performance by category.
     */
    public function byCategory(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $startDate = now()->subDays($days);

        $categoryPerformance = Product::with(['category:id,name'])
            ->withCount(['views' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->withSum(['orderItems as total_sold' => function ($query) use ($startDate) {
                $query->whereHas('order', function ($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
                });
            }], 'quantity')
            ->withSum(['orderItems as total_revenue' => function ($query) use ($startDate) {
                $query->whereHas('order', function ($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
                });
            }], \DB::raw('unit_price * quantity'))
            ->get()
            ->groupBy('category.name')
            ->map(function ($products, $categoryName) {
                return [
                    'category' => $categoryName ?? 'Uncategorized',
                    'product_count' => $products->count(),
                    'total_views' => $products->sum('views_count'),
                    'total_sold' => $products->sum('total_sold'),
                    'total_revenue' => round($products->sum('total_revenue'), 2),
                    'avg_conversion' => $products->sum('views_count') > 0 ? 
                        ($products->sum('total_sold') / $products->sum('views_count')) * 100 : 0,
                ];
            })
            ->values()
            ->sortByDesc('total_revenue')
            ->take(10)
            ->toArray();

        return response()->json([
            'categories' => $categoryPerformance,
            'days' => $days
        ]);
    }

    /**
     * Get underperforming products.
     */
    public function underperforming(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $startDate = now()->subDays($days);

        // Products with high views but low sales
        $underperforming = Product::with(['category:id,name'])
            ->withCount(['views' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->withSum(['orderItems as total_sold' => function ($query) use ($startDate) {
                $query->whereHas('order', function ($q) use ($startDate) {
                    $q->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
                });
            }], 'quantity')
            ->having('views_count', '>', 10) // At least 10 views
            ->get()
            ->map(function ($product) {
                $conversionRate = $product->views_count > 0 ? 
                    ($product->total_sold / $product->views_count) * 100 : 0;
                
                return [
                    'product' => $product,
                    'views' => $product->views_count,
                    'sales' => $product->total_sold ?? 0,
                    'conversion_rate' => round($conversionRate, 2),
                ];
            })
            ->sortBy('conversion_rate')
            ->take($limit)
            ->values()
            ->toArray();

        return response()->json([
            'products' => $underperforming,
            'days' => $days
        ]);
    }

    /**
     * Export product performance data.
     */
    public function export(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $metric = $request->get('metric', 'revenue');
        $limit = $request->get('limit', 100);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 500);

        $products = $this->performanceService->getTopPerformingProducts($days, $limit, $metric);
        
        $exportData = array_map(function ($item) use ($days) {
            $performance = $this->performanceService->getProductPerformance($item['product']['id'], $days);
            
            return [
                'product_id' => $item['product']['id'],
                'product_name' => $item['product']['name'],
                'product_slug' => $item['product']['slug'],
                'price' => $item['product']['price'],
                'sale_price' => $item['product']['sale_price'],
                'total_views' => $performance['views']['total_views'],
                'unique_viewers' => $performance['views']['unique_viewers'],
                'total_sold' => $performance['sales']['total_sold'],
                'total_revenue' => $performance['sales']['total_revenue'],
                'conversion_rate' => $performance['conversion']['view_to_purchase_rate'],
                'avg_rating' => $performance['reviews']['avg_rating'],
                'total_reviews' => $performance['reviews']['total_reviews'],
                'wishlist_additions' => $performance['wishlist']['total_additions'],
                'total_downloads' => $performance['downloads']['total_downloads'],
            ];
        }, $products);

        return response()->json([
            'data' => $exportData,
            'total_records' => count($exportData),
            'metric' => $metric,
            'days' => $days,
            'generated_at' => now()->toISOString(),
        ]);
    }
}
