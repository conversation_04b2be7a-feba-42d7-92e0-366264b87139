<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Inertia\Response;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index(Request $request): Response
    {
        $query = ProductCategory::with(['parent'])
            ->withCount(['products' => function ($query) {
                $query->whereNull('deleted_at');
            }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Parent category filter
        if ($request->filled('parent')) {
            if ($request->get('parent') === 'root') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->get('parent'));
            }
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('is_active', $request->boolean('status'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSorts = ['name', 'sort_order', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $categories = $query->paginate(15)->withQueryString();

        // Get parent categories for filter dropdown
        $parentCategories = ProductCategory::select('id', 'name')
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/categories/index', [
            'categories' => $categories,
            'parentCategories' => $parentCategories,
            'filters' => $request->only(['search', 'parent', 'status']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Show the form for creating a new category.
     */
    public function create(): Response
    {
        $parentCategories = ProductCategory::select('id', 'name')
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/categories/create', [
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Store a newly created category in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parent_id' => 'nullable|exists:product_categories,id',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $category = ProductCategory::create($validator->validated());

        return redirect()
            ->route('admin.categories.show', $category)
            ->with('success', 'Category created successfully.');
    }

    /**
     * Display the specified category.
     */
    public function show(ProductCategory $category): Response
    {
        $category->load([
            'parent',
            'children' => function ($query) {
                $query->orderBy('sort_order')->orderBy('name');
            },
            'products' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(10);
            }
        ]);

        return Inertia::render('admin/categories/show', [
            'category' => $category,
        ]);
    }

    /**
     * Show the form for editing the specified category.
     */
    public function edit(ProductCategory $category): Response
    {
        $parentCategories = ProductCategory::select('id', 'name')
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->where('id', '!=', $category->id) // Exclude self
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/categories/edit', [
            'category' => $category,
            'parentCategories' => $parentCategories,
        ]);
    }

    /**
     * Update the specified category in storage.
     */
    public function update(Request $request, ProductCategory $category)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'parent_id' => [
                'nullable',
                'exists:product_categories,id',
                function ($attribute, $value, $fail) use ($category) {
                    if ($value == $category->id) {
                        $fail('A category cannot be its own parent.');
                    }
                },
            ],
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:255',
            'sort_order' => 'integer|min:0',
            'is_active' => 'boolean',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $category->update($validator->validated());

        return redirect()
            ->route('admin.categories.show', $category)
            ->with('success', 'Category updated successfully.');
    }

    /**
     * Remove the specified category from storage.
     */
    public function destroy(ProductCategory $category)
    {
        // Check if category has products
        if ($category->products()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category that has products. Please move or delete the products first.']);
        }

        // Check if category has children
        if ($category->children()->exists()) {
            return back()->withErrors(['error' => 'Cannot delete category that has subcategories. Please delete the subcategories first.']);
        }

        $category->delete();

        return redirect()
            ->route('admin.categories.index')
            ->with('success', 'Category deleted successfully.');
    }

    /**
     * Get category tree for hierarchical display.
     */
    public function tree()
    {
        $categories = ProductCategory::with(['children' => function ($query) {
            $query->orderBy('sort_order')->orderBy('name');
        }])
        ->whereNull('parent_id')
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->orderBy('name')
        ->get();

        return response()->json($categories);
    }

    /**
     * Update category sort order.
     */
    public function updateSortOrder(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'categories' => 'required|array',
            'categories.*.id' => 'required|exists:product_categories,id',
            'categories.*.sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        foreach ($request->get('categories') as $categoryData) {
            ProductCategory::where('id', $categoryData['id'])
                ->update(['sort_order' => $categoryData['sort_order']]);
        }

        return response()->json(['message' => 'Sort order updated successfully.']);
    }

    /**
     * Bulk actions for categories.
     */
    public function bulkAction(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:delete,activate,deactivate',
            'category_ids' => 'required|array|min:1',
            'category_ids.*' => 'exists:product_categories,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $categoryIds = $request->get('category_ids');
        $action = $request->get('action');

        switch ($action) {
            case 'delete':
                // Check if any categories have products or children
                $categoriesWithProducts = ProductCategory::whereIn('id', $categoryIds)
                    ->whereHas('products')
                    ->exists();
                
                $categoriesWithChildren = ProductCategory::whereIn('id', $categoryIds)
                    ->whereHas('children')
                    ->exists();

                if ($categoriesWithProducts || $categoriesWithChildren) {
                    return back()->withErrors(['error' => 'Cannot delete categories that have products or subcategories.']);
                }

                ProductCategory::whereIn('id', $categoryIds)->delete();
                $message = 'Selected categories deleted successfully.';
                break;
            case 'activate':
                ProductCategory::whereIn('id', $categoryIds)->update(['is_active' => true]);
                $message = 'Selected categories activated successfully.';
                break;
            case 'deactivate':
                ProductCategory::whereIn('id', $categoryIds)->update(['is_active' => false]);
                $message = 'Selected categories deactivated successfully.';
                break;
        }

        return back()->with('success', $message);
    }
}
