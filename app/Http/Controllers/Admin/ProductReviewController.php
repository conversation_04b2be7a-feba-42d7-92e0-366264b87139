<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProductReview;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class ProductReviewController extends Controller
{
    /**
     * Display a listing of reviews.
     */
    public function index(Request $request): Response
    {
        $query = ProductReview::with(['product:id,name,slug', 'user:id,name,email'])
            ->withCount(['helpfulVotes as helpful_votes_count' => function ($q) {
                $q->where('is_helpful', true);
            }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%")
                  ->orWhereHas('product', function ($productQuery) use ($search) {
                      $productQuery->where('name', 'like', "%{$search}%");
                  })
                  ->orWhereHas('user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('email', 'like', "%{$search}%");
                  });
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Rating filter
        if ($request->filled('rating')) {
            $query->where('rating', $request->get('rating'));
        }

        // Verified purchase filter
        if ($request->filled('verified_only')) {
            $query->where('is_verified_purchase', true);
        }

        // Product filter
        if ($request->filled('product_id')) {
            $query->where('product_id', $request->get('product_id'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSortFields = ['created_at', 'rating', 'status', 'is_verified_purchase'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $reviews = $query->paginate(15)->withQueryString();

        // Get products for filter dropdown
        $products = Product::select('id', 'name')
            ->whereHas('reviews')
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/reviews/index', [
            'reviews' => $reviews,
            'products' => $products,
            'filters' => $request->only(['search', 'status', 'rating', 'verified_only', 'product_id']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Display the specified review.
     */
    public function show(ProductReview $review): Response
    {
        $review->load([
            'product:id,name,slug,image',
            'user:id,name,email',
            'order:id,order_number',
            'approver:id,name',
            'helpfulVotes.user:id,name'
        ]);

        return Inertia::render('admin/reviews/show', [
            'review' => $review,
        ]);
    }

    /**
     * Approve a review.
     */
    public function approve(ProductReview $review): JsonResponse
    {
        $review->approve(auth()->id());

        return response()->json([
            'message' => 'Review approved successfully',
            'review' => $review->fresh(['product:id,name', 'user:id,name'])
        ]);
    }

    /**
     * Reject a review.
     */
    public function reject(Request $request, ProductReview $review): JsonResponse
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $review->reject($request->admin_notes);

        return response()->json([
            'message' => 'Review rejected successfully',
            'review' => $review->fresh(['product:id,name', 'user:id,name'])
        ]);
    }

    /**
     * Toggle featured status of a review.
     */
    public function toggleFeatured(ProductReview $review): JsonResponse
    {
        $review->update([
            'is_featured' => !$review->is_featured
        ]);

        return response()->json([
            'message' => $review->is_featured ? 'Review marked as featured' : 'Review unmarked as featured',
            'review' => $review->fresh(['product:id,name', 'user:id,name'])
        ]);
    }

    /**
     * Bulk approve reviews.
     */
    public function bulkApprove(Request $request): JsonResponse
    {
        $request->validate([
            'review_ids' => 'required|array',
            'review_ids.*' => 'exists:product_reviews,id',
        ]);

        $reviews = ProductReview::whereIn('id', $request->review_ids)->get();
        
        foreach ($reviews as $review) {
            $review->approve(auth()->id());
        }

        return response()->json([
            'message' => count($reviews) . ' reviews approved successfully'
        ]);
    }

    /**
     * Bulk reject reviews.
     */
    public function bulkReject(Request $request): JsonResponse
    {
        $request->validate([
            'review_ids' => 'required|array',
            'review_ids.*' => 'exists:product_reviews,id',
            'admin_notes' => 'nullable|string|max:1000',
        ]);

        $reviews = ProductReview::whereIn('id', $request->review_ids)->get();
        
        foreach ($reviews as $review) {
            $review->reject($request->admin_notes);
        }

        return response()->json([
            'message' => count($reviews) . ' reviews rejected successfully'
        ]);
    }

    /**
     * Delete a review.
     */
    public function destroy(ProductReview $review): JsonResponse
    {
        $review->delete();

        return response()->json([
            'message' => 'Review deleted successfully'
        ]);
    }

    /**
     * Get review statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_reviews' => ProductReview::count(),
            'pending_reviews' => ProductReview::where('status', 'pending')->count(),
            'approved_reviews' => ProductReview::where('is_approved', true)->count(),
            'rejected_reviews' => ProductReview::where('status', 'rejected')->count(),
            'featured_reviews' => ProductReview::where('is_featured', true)->count(),
            'verified_purchase_reviews' => ProductReview::where('is_verified_purchase', true)->count(),
            'average_rating' => ProductReview::where('is_approved', true)->avg('rating'),
            'rating_distribution' => ProductReview::where('is_approved', true)
                ->selectRaw('rating, COUNT(*) as count')
                ->groupBy('rating')
                ->orderBy('rating')
                ->pluck('count', 'rating')
                ->toArray(),
        ];

        return response()->json($stats);
    }
}
