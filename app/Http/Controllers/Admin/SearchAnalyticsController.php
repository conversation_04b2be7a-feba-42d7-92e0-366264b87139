<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SearchAnalytics;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class SearchAnalyticsController extends Controller
{
    /**
     * Display search analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $metrics = SearchAnalytics::getPerformanceMetrics($days);
        $popularQueries = SearchAnalytics::getPopularQueries($days, 20);
        $noResultQueries = SearchAnalytics::getNoResultQueries($days, 20);
        $trends = SearchAnalytics::getSearchTrends($days);
        $clickThroughRates = SearchAnalytics::getClickThroughRates($days);
        $mostClickedProducts = SearchAnalytics::getMostClickedProducts($days, 10);
        $filterUsage = SearchAnalytics::getFilterUsage($days);

        return Inertia::render('super-admin/analytics/search', [
            'metrics' => $metrics,
            'popularQueries' => $popularQueries,
            'noResultQueries' => $noResultQueries,
            'trends' => $trends,
            'clickThroughRates' => $clickThroughRates,
            'mostClickedProducts' => $mostClickedProducts,
            'filterUsage' => $filterUsage,
            'days' => $days,
        ]);
    }

    /**
     * Get search performance metrics.
     */
    public function metrics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $metrics = SearchAnalytics::getPerformanceMetrics($days);

        return response()->json($metrics);
    }

    /**
     * Get popular search queries.
     */
    public function popularQueries(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 20);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 100);

        $queries = SearchAnalytics::getPopularQueries($days, $limit);

        return response()->json([
            'queries' => $queries
        ]);
    }

    /**
     * Get queries with no results.
     */
    public function noResultQueries(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 20);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 100);

        $queries = SearchAnalytics::getNoResultQueries($days, $limit);

        return response()->json([
            'queries' => $queries
        ]);
    }

    /**
     * Get search trends over time.
     */
    public function trends(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $trends = SearchAnalytics::getSearchTrends($days);

        return response()->json([
            'trends' => $trends
        ]);
    }

    /**
     * Get click-through rates.
     */
    public function clickThroughRates(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $rates = SearchAnalytics::getClickThroughRates($days);

        return response()->json($rates);
    }

    /**
     * Get most clicked products from search.
     */
    public function mostClickedProducts(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $products = SearchAnalytics::getMostClickedProducts($days, $limit);

        return response()->json([
            'products' => $products
        ]);
    }

    /**
     * Get filter usage statistics.
     */
    public function filterUsage(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $usage = SearchAnalytics::getFilterUsage($days);

        return response()->json([
            'filter_usage' => $usage
        ]);
    }

    /**
     * Get detailed search data for export.
     */
    public function export(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $searches = SearchAnalytics::recent($days)
            ->with(['user:id,name,email', 'clickedProduct:id,name,slug'])
            ->orderBy('created_at', 'desc')
            ->limit(1000)
            ->get();

        $exportData = $searches->map(function ($search) {
            return [
                'id' => $search->id,
                'query' => $search->query,
                'normalized_query' => $search->normalized_query,
                'result_count' => $search->result_count,
                'user_name' => $search->user?->name,
                'user_email' => $search->user?->email,
                'had_results' => $search->had_results,
                'clicked_result' => $search->clicked_result,
                'clicked_product' => $search->clickedProduct?->name,
                'clicked_position' => $search->clicked_position,
                'search_duration' => $search->search_duration,
                'filters_applied' => $search->filters_applied,
                'sort_by' => $search->sort_by,
                'created_at' => $search->created_at->toISOString(),
            ];
        });

        return response()->json([
            'data' => $exportData,
            'total_records' => $searches->count(),
        ]);
    }

    /**
     * Get search analytics summary for dashboard.
     */
    public function summary(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        $days = min(max($days, 1), 30);

        $totalSearches = SearchAnalytics::recent($days)->count();
        $successfulSearches = SearchAnalytics::recent($days)->withResults()->count();
        $clickedSearches = SearchAnalytics::recent($days)->clicked()->count();
        
        $successRate = $totalSearches > 0 ? ($successfulSearches / $totalSearches) * 100 : 0;
        $ctr = $successfulSearches > 0 ? ($clickedSearches / $successfulSearches) * 100 : 0;

        $topQuery = SearchAnalytics::recent($days)
            ->groupBy('normalized_query')
            ->selectRaw('normalized_query, COUNT(*) as count')
            ->orderBy('count', 'desc')
            ->first();

        return response()->json([
            'total_searches' => $totalSearches,
            'successful_searches' => $successfulSearches,
            'success_rate' => round($successRate, 1),
            'clicked_searches' => $clickedSearches,
            'click_through_rate' => round($ctr, 1),
            'top_query' => $topQuery?->normalized_query,
            'top_query_count' => $topQuery?->count ?? 0,
        ]);
    }

    /**
     * Record a search result click.
     */
    public function recordClick(Request $request): JsonResponse
    {
        $request->validate([
            'search_id' => 'required|exists:search_analytics,id',
            'product_id' => 'required|exists:products,id',
            'position' => 'required|integer|min:1',
        ]);

        $search = SearchAnalytics::findOrFail($request->search_id);
        $search->recordClick($request->product_id, $request->position);

        return response()->json([
            'message' => 'Click recorded successfully'
        ]);
    }

    /**
     * Get search suggestions for improving no-result queries.
     */
    public function suggestions(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $noResultQueries = SearchAnalytics::getNoResultQueries($days, $limit);
        
        $suggestions = [];
        foreach ($noResultQueries as $queryData) {
            $query = $queryData['normalized_query'];
            $searchCount = $queryData['search_count'];
            
            // Find similar products that might match
            $similarProducts = \App\Models\Product::active()
                ->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhereJsonContains('tags', $query);
                })
                ->limit(3)
                ->get(['id', 'name', 'slug']);

            $suggestions[] = [
                'query' => $query,
                'search_count' => $searchCount,
                'similar_products' => $similarProducts,
                'suggestions' => [
                    'Add product with similar name',
                    'Create category for this type',
                    'Add relevant tags to existing products',
                    'Consider this for product development'
                ]
            ];
        }

        return response()->json([
            'suggestions' => $suggestions
        ]);
    }
}
