<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\DownloadAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class DownloadAnalyticsController extends Controller
{
    protected DownloadAnalyticsService $downloadAnalytics;

    public function __construct(DownloadAnalyticsService $downloadAnalytics)
    {
        $this->downloadAnalytics = $downloadAnalytics;
    }

    /**
     * Display the download analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->downloadAnalytics->getDownloadOverview($days);
        $dailyTrends = $this->downloadAnalytics->getDailyDownloadTrends($days);
        $mostDownloaded = $this->downloadAnalytics->getMostDownloadedFiles($days, 10);
        $fileTypes = $this->downloadAnalytics->getDownloadsByFileType($days);
        $topUsers = $this->downloadAnalytics->getTopDownloadingUsers($days, 10);
        $failureAnalysis = $this->downloadAnalytics->getDownloadFailureAnalysis($days);
        $bandwidthUsage = $this->downloadAnalytics->getBandwidthUsage($days);

        return Inertia::render('super-admin/analytics/downloads', [
            'overview' => $overview,
            'dailyTrends' => $dailyTrends,
            'mostDownloaded' => $mostDownloaded,
            'fileTypes' => $fileTypes,
            'topUsers' => $topUsers,
            'failureAnalysis' => $failureAnalysis,
            'bandwidthUsage' => $bandwidthUsage,
            'days' => $days,
        ]);
    }

    /**
     * Get download overview data.
     */
    public function overview(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->downloadAnalytics->getDownloadOverview($days);

        return response()->json($overview);
    }

    /**
     * Get daily download trends.
     */
    public function dailyTrends(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $trends = $this->downloadAnalytics->getDailyDownloadTrends($days);

        return response()->json([
            'trends' => $trends,
            'days' => $days
        ]);
    }

    /**
     * Get hourly download patterns.
     */
    public function hourlyPattern(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $pattern = $this->downloadAnalytics->getHourlyDownloadPattern($days);

        return response()->json([
            'pattern' => $pattern,
            'days' => $days
        ]);
    }

    /**
     * Get most downloaded files.
     */
    public function mostDownloaded(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $files = $this->downloadAnalytics->getMostDownloadedFiles($days, $limit);

        return response()->json([
            'files' => $files,
            'days' => $days
        ]);
    }

    /**
     * Get downloads by file type.
     */
    public function byFileType(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $fileTypes = $this->downloadAnalytics->getDownloadsByFileType($days);

        return response()->json([
            'file_types' => $fileTypes,
            'days' => $days
        ]);
    }

    /**
     * Get top downloading users.
     */
    public function topUsers(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $users = $this->downloadAnalytics->getTopDownloadingUsers($days, $limit);

        return response()->json([
            'users' => $users,
            'days' => $days
        ]);
    }

    /**
     * Get download failure analysis.
     */
    public function failureAnalysis(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $analysis = $this->downloadAnalytics->getDownloadFailureAnalysis($days);

        return response()->json([
            'analysis' => $analysis,
            'days' => $days
        ]);
    }

    /**
     * Get bandwidth usage statistics.
     */
    public function bandwidthUsage(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $usage = $this->downloadAnalytics->getBandwidthUsage($days);

        return response()->json([
            'usage' => $usage,
            'days' => $days
        ]);
    }

    /**
     * Get download expiry analysis.
     */
    public function expiryAnalysis(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $analysis = $this->downloadAnalytics->getDownloadExpiryAnalysis($days);

        return response()->json([
            'analysis' => $analysis,
            'days' => $days
        ]);
    }

    /**
     * Get download summary for dashboard widgets.
     */
    public function summary(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        $days = min(max($days, 1), 30);

        $summary = $this->downloadAnalytics->getDownloadSummary($days);

        return response()->json($summary);
    }

    /**
     * Export download analytics data.
     */
    public function export(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $format = $request->get('format', 'json');
        
        $days = min(max($days, 1), 365);

        $data = [
            'overview' => $this->downloadAnalytics->getDownloadOverview($days),
            'daily_trends' => $this->downloadAnalytics->getDailyDownloadTrends($days),
            'hourly_pattern' => $this->downloadAnalytics->getHourlyDownloadPattern($days),
            'most_downloaded' => $this->downloadAnalytics->getMostDownloadedFiles($days, 50),
            'file_types' => $this->downloadAnalytics->getDownloadsByFileType($days),
            'top_users' => $this->downloadAnalytics->getTopDownloadingUsers($days, 50),
            'failure_analysis' => $this->downloadAnalytics->getDownloadFailureAnalysis($days),
            'bandwidth_usage' => $this->downloadAnalytics->getBandwidthUsage($days),
            'expiry_analysis' => $this->downloadAnalytics->getDownloadExpiryAnalysis($days),
            'generated_at' => now()->toISOString(),
            'period_days' => $days,
        ];

        if ($format === 'csv') {
            // For CSV export, we'd typically generate a file and return a download link
            // For now, return the data structure
            return response()->json([
                'message' => 'CSV export would be generated here',
                'data' => $data
            ]);
        }

        return response()->json($data);
    }

    /**
     * Get real-time download metrics.
     */
    public function realtime(Request $request): JsonResponse
    {
        $period = $request->get('period', 'hour');
        
        switch ($period) {
            case 'hour':
                $startDate = now()->subHour();
                break;
            case 'day':
                $startDate = now()->subDay();
                break;
            case 'week':
                $startDate = now()->subWeek();
                break;
            default:
                $startDate = now()->subHour();
        }

        $stats = \App\Models\DownloadLog::where('created_at', '>=', $startDate)
            ->selectRaw('
                COUNT(*) as total_downloads,
                COUNT(CASE WHEN download_successful THEN 1 END) as successful_downloads,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(DISTINCT digital_asset_id) as unique_files
            ')
            ->first();

        $successRate = $stats->total_downloads > 0 ? 
            ($stats->successful_downloads / $stats->total_downloads) * 100 : 0;

        return response()->json([
            'total_downloads' => $stats->total_downloads ?? 0,
            'successful_downloads' => $stats->successful_downloads ?? 0,
            'success_rate' => round($successRate, 1),
            'unique_users' => $stats->unique_users ?? 0,
            'unique_files' => $stats->unique_files ?? 0,
            'period' => $period,
            'start_time' => $startDate->toISOString(),
        ]);
    }

    /**
     * Get download performance metrics.
     */
    public function performanceMetrics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->downloadAnalytics->getDownloadOverview($days);
        $bandwidthUsage = $this->downloadAnalytics->getBandwidthUsage($days);
        
        return response()->json([
            'success_rate' => $overview['current']['success_rate'],
            'avg_downloads_per_user' => $overview['current']['avg_downloads_per_user'],
            'total_bandwidth_gb' => $bandwidthUsage['total_gb'],
            'avg_daily_bandwidth_mb' => $bandwidthUsage['avg_daily_mb'],
            'unique_file_ratio' => $overview['current']['total_downloads'] > 0 ? 
                round(($overview['current']['unique_files'] / $overview['current']['total_downloads']) * 100, 2) : 0,
            'user_engagement' => $overview['current']['unique_users'] > 0 ? 
                round($overview['current']['total_downloads'] / $overview['current']['unique_users'], 2) : 0,
        ]);
    }
}
