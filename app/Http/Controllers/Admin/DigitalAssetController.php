<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\DigitalAsset;
use App\Models\Product;
use App\Services\DigitalAssetService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Exception;

class DigitalAssetController extends Controller
{
    protected $assetService;

    public function __construct(DigitalAssetService $assetService)
    {
        $this->assetService = $assetService;
    }

    /**
     * Display a listing of digital assets for a product.
     */
    public function index(Product $product)
    {
        $assets = $product->digitalAssets()
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return Inertia::render('admin/products/files/index', [
            'product' => $product,
            'assets' => $assets,
            'storageStats' => $this->assetService->getStorageStats(),
        ]);
    }

    /**
     * Show the form for uploading new assets.
     */
    public function create(Product $product)
    {
        return Inertia::render('admin/products/files/upload', [
            'product' => $product,
            'maxFileSize' => config('app.digital_asset_max_file_size', 104857600),
            'allowedExtensions' => explode(',', config('app.digital_asset_allowed_extensions', 'pdf,zip')),
        ]);
    }

    /**
     * Store uploaded files.
     */
    public function store(Request $request, Product $product)
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required|array|min:1',
            'files.*' => 'required|file|max:' . (config('app.digital_asset_max_file_size', 104857600) / 1024),
            'description' => 'nullable|string|max:1000',
            'version' => 'nullable|string|max:50',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $files = $request->file('files');
            $metadata = [
                'description' => $request->get('description'),
                'version' => $request->get('version'),
                'upload_session' => uniqid(),
            ];

            if (count($files) === 1) {
                // Single file upload
                $asset = $this->assetService->uploadFile($files[0], $product, $metadata);
                
                if ($request->has('is_active')) {
                    $asset->update(['is_active' => $request->boolean('is_active')]);
                }

                return redirect()
                    ->route('admin.products.files.index', $product)
                    ->with('success', 'File uploaded successfully.');
            } else {
                // Bulk upload
                $result = $this->assetService->bulkUpload($files, $product, $metadata);
                
                $message = "Upload completed: {$result['summary']['successful']} successful, {$result['summary']['failed']} failed.";
                
                return redirect()
                    ->route('admin.products.files.index', $product)
                    ->with('success', $message)
                    ->with('uploadResults', $result);
            }

        } catch (Exception $e) {
            return back()
                ->withErrors(['error' => 'Upload failed: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified asset.
     */
    public function show(Product $product, DigitalAsset $asset)
    {
        // Ensure asset belongs to product
        if ($asset->product_id !== $product->id) {
            abort(404);
        }

        $fileInfo = $this->assetService->getFileInfo($asset);

        return Inertia::render('admin/products/files/show', [
            'product' => $product,
            'asset' => $asset->load(['downloadLogs' => function ($query) {
                $query->latest()->limit(10);
            }]),
            'fileInfo' => $fileInfo,
        ]);
    }

    /**
     * Update the specified asset.
     */
    public function update(Request $request, Product $product, DigitalAsset $asset)
    {
        // Ensure asset belongs to product
        if ($asset->product_id !== $product->id) {
            abort(404);
        }

        $validator = Validator::make($request->all(), [
            'original_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'version' => 'nullable|string|max:50',
            'is_active' => 'boolean',
            'download_limit' => 'nullable|integer|min:0',
            'metadata' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();
            
            // Update metadata if provided
            if (isset($data['metadata'])) {
                $currentMetadata = $asset->metadata ?? [];
                $data['metadata'] = array_merge($currentMetadata, $data['metadata'], [
                    'updated_at' => now()->toISOString(),
                    'updated_by' => auth()->id(),
                ]);
            }

            $asset->update($data);

            return redirect()
                ->route('admin.products.files.show', [$product, $asset])
                ->with('success', 'Asset updated successfully.');

        } catch (Exception $e) {
            return back()
                ->withErrors(['error' => 'Update failed: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Remove the specified asset.
     */
    public function destroy(Product $product, DigitalAsset $asset)
    {
        // Ensure asset belongs to product
        if ($asset->product_id !== $product->id) {
            abort(404);
        }

        try {
            $this->assetService->deleteAsset($asset);

            return redirect()
                ->route('admin.products.files.index', $product)
                ->with('success', 'Asset deleted successfully.');

        } catch (Exception $e) {
            return back()->withErrors(['error' => 'Delete failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Generate a download URL for the asset.
     */
    public function download(Product $product, DigitalAsset $asset)
    {
        // Ensure asset belongs to product
        if ($asset->product_id !== $product->id) {
            abort(404);
        }

        if (!$asset->is_active) {
            abort(403, 'This file is not available for download.');
        }

        try {
            $downloadUrl = $this->assetService->generateDownloadUrl($asset);
            
            // Log the download attempt
            $asset->downloadLogs()->create([
                'user_id' => auth()->id(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'downloaded_at' => now(),
            ]);

            // Increment download count
            $asset->increment('download_count');

            return response()->json([
                'download_url' => $downloadUrl,
                'expires_at' => now()->addHours(24)->toISOString(),
            ]);

        } catch (Exception $e) {
            return response()->json([
                'error' => 'Failed to generate download URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test S3 connection.
     */
    public function testConnection()
    {
        $result = $this->assetService->testConnection();
        
        return response()->json($result);
    }

    /**
     * Bulk actions on assets.
     */
    public function bulkAction(Request $request, Product $product)
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:delete,activate,deactivate',
            'asset_ids' => 'required|array|min:1',
            'asset_ids.*' => 'exists:digital_assets,id',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $assetIds = $request->get('asset_ids');
        $action = $request->get('action');

        // Ensure all assets belong to the product
        $assets = DigitalAsset::whereIn('id', $assetIds)
            ->where('product_id', $product->id)
            ->get();

        if ($assets->count() !== count($assetIds)) {
            return back()->withErrors(['error' => 'Some assets do not belong to this product.']);
        }

        try {
            switch ($action) {
                case 'delete':
                    foreach ($assets as $asset) {
                        $this->assetService->deleteAsset($asset);
                    }
                    $message = 'Selected assets deleted successfully.';
                    break;
                    
                case 'activate':
                    DigitalAsset::whereIn('id', $assetIds)->update(['is_active' => true]);
                    $message = 'Selected assets activated successfully.';
                    break;
                    
                case 'deactivate':
                    DigitalAsset::whereIn('id', $assetIds)->update(['is_active' => false]);
                    $message = 'Selected assets deactivated successfully.';
                    break;
            }

            return back()->with('success', $message);

        } catch (Exception $e) {
            return back()->withErrors(['error' => 'Bulk action failed: ' . $e->getMessage()]);
        }
    }
}
