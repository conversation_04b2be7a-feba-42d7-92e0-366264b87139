<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Inertia\Inertia;

class AdminDashboardController extends Controller
{
    protected $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the admin dashboard.
     */
    public function index(Request $request)
    {
        $dashboardData = $this->dashboardService->getDashboardData($request->user());
        $systemHealth = $this->getSystemHealth();

        return Inertia::render('admin/dashboard', [
            'dashboardData' => $dashboardData,
            'systemHealth' => $systemHealth,
        ]);
    }

    /**
     * Get admin dashboard data via API.
     */
    public function data(Request $request)
    {
        $dashboardData = $this->dashboardService->getDashboardData($request->user());

        return response()->json($dashboardData);
    }

    /**
     * Get system health status.
     */
    public function systemHealth()
    {
        return response()->json($this->getSystemHealth());
    }

    /**
     * Get system health data.
     */
    protected function getSystemHealth(): array
    {
        return [
            'database' => $this->checkDatabaseHealth(),
            'email' => $this->checkEmailHealth(),
            'storage' => $this->checkStorageHealth(),
            'cache' => $this->checkCacheHealth(),
        ];
    }

    /**
     * Check database health.
     */
    protected function checkDatabaseHealth(): array
    {
        try {
            \DB::connection()->getPdo();
            return [
                'status' => 'operational',
                'message' => 'Database connection is healthy',
                'response_time' => '< 10ms',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Database connection failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check email service health.
     */
    protected function checkEmailHealth(): array
    {
        // Mock implementation - replace with actual email service check
        return [
            'status' => 'operational',
            'message' => 'Email service is operational',
            'provider' => config('mail.default'),
        ];
    }

    /**
     * Check storage health.
     */
    protected function checkStorageHealth(): array
    {
        try {
            $disk = \Storage::disk('public');
            $testFile = 'health-check-' . time() . '.txt';
            $disk->put($testFile, 'health check');
            $disk->delete($testFile);
            
            return [
                'status' => 'operational',
                'message' => 'Storage is operational',
                'disk' => 'public',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Storage check failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache health.
     */
    protected function checkCacheHealth(): array
    {
        try {
            $key = 'health-check-' . time();
            \Cache::put($key, 'test', 60);
            $value = \Cache::get($key);
            \Cache::forget($key);
            
            if ($value === 'test') {
                return [
                    'status' => 'operational',
                    'message' => 'Cache is operational',
                    'driver' => config('cache.default'),
                ];
            }
            
            return [
                'status' => 'warning',
                'message' => 'Cache test failed',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'Cache check failed',
                'error' => $e->getMessage(),
            ];
        }
    }
}
