<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\SalesAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Inertia\Inertia;
use Inertia\Response;

class SalesAnalyticsController extends Controller
{
    protected SalesAnalyticsService $salesAnalytics;

    public function __construct(SalesAnalyticsService $salesAnalytics)
    {
        $this->salesAnalytics = $salesAnalytics;
    }

    /**
     * Display the sales analytics dashboard.
     */
    public function index(Request $request): Response
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->salesAnalytics->getSalesOverview($days);
        $dailyTrends = $this->salesAnalytics->getDailySalesTrends($days);
        $topProducts = $this->salesAnalytics->getTopSellingProducts($days, 10);
        $revenueByCategory = $this->salesAnalytics->getRevenueByCategory($days);
        $customerAnalytics = $this->salesAnalytics->getCustomerAnalytics($days);
        $orderStatusDistribution = $this->salesAnalytics->getOrderStatusDistribution($days);
        $paymentMethods = $this->salesAnalytics->getPaymentMethodAnalytics($days);

        return Inertia::render('super-admin/analytics/sales', [
            'overview' => $overview,
            'dailyTrends' => $dailyTrends,
            'topProducts' => $topProducts,
            'revenueByCategory' => $revenueByCategory,
            'customerAnalytics' => $customerAnalytics,
            'orderStatusDistribution' => $orderStatusDistribution,
            'paymentMethods' => $paymentMethods,
            'days' => $days,
        ]);
    }

    /**
     * Get sales overview data.
     */
    public function overview(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->salesAnalytics->getSalesOverview($days);

        return response()->json($overview);
    }

    /**
     * Get daily sales trends.
     */
    public function dailyTrends(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $trends = $this->salesAnalytics->getDailySalesTrends($days);

        return response()->json([
            'trends' => $trends,
            'days' => $days
        ]);
    }

    /**
     * Get hourly sales patterns.
     */
    public function hourlyPattern(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $pattern = $this->salesAnalytics->getHourlySalesPattern($days);

        return response()->json([
            'pattern' => $pattern,
            'days' => $days
        ]);
    }

    /**
     * Get top selling products.
     */
    public function topProducts(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $limit = $request->get('limit', 10);
        
        $days = min(max($days, 1), 365);
        $limit = min(max($limit, 1), 50);

        $products = $this->salesAnalytics->getTopSellingProducts($days, $limit);

        return response()->json([
            'products' => $products,
            'days' => $days
        ]);
    }

    /**
     * Get revenue by category.
     */
    public function revenueByCategory(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $categories = $this->salesAnalytics->getRevenueByCategory($days);

        return response()->json([
            'categories' => $categories,
            'days' => $days
        ]);
    }

    /**
     * Get customer analytics.
     */
    public function customerAnalytics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $analytics = $this->salesAnalytics->getCustomerAnalytics($days);

        return response()->json($analytics);
    }

    /**
     * Get order status distribution.
     */
    public function orderStatusDistribution(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $distribution = $this->salesAnalytics->getOrderStatusDistribution($days);

        return response()->json([
            'distribution' => $distribution,
            'days' => $days
        ]);
    }

    /**
     * Get payment method analytics.
     */
    public function paymentMethods(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $methods = $this->salesAnalytics->getPaymentMethodAnalytics($days);

        return response()->json([
            'methods' => $methods,
            'days' => $days
        ]);
    }

    /**
     * Get monthly comparison data.
     */
    public function monthlyComparison(Request $request): JsonResponse
    {
        $months = $request->get('months', 12);
        $months = min(max($months, 1), 24);

        $comparison = $this->salesAnalytics->getMonthlyComparison($months);

        return response()->json([
            'comparison' => $comparison,
            'months' => $months
        ]);
    }

    /**
     * Get sales forecast.
     */
    public function forecast(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 7), 90);

        $forecast = $this->salesAnalytics->getSalesForecast($days);

        return response()->json([
            'forecast' => $forecast,
            'based_on_days' => $days
        ]);
    }

    /**
     * Export sales data.
     */
    public function export(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $format = $request->get('format', 'json');
        
        $days = min(max($days, 1), 365);

        $data = [
            'overview' => $this->salesAnalytics->getSalesOverview($days),
            'daily_trends' => $this->salesAnalytics->getDailySalesTrends($days),
            'top_products' => $this->salesAnalytics->getTopSellingProducts($days, 20),
            'revenue_by_category' => $this->salesAnalytics->getRevenueByCategory($days),
            'customer_analytics' => $this->salesAnalytics->getCustomerAnalytics($days),
            'order_status_distribution' => $this->salesAnalytics->getOrderStatusDistribution($days),
            'payment_methods' => $this->salesAnalytics->getPaymentMethodAnalytics($days),
            'generated_at' => now()->toISOString(),
            'period_days' => $days,
        ];

        if ($format === 'csv') {
            // For CSV export, we'd typically generate a file and return a download link
            // For now, return the data structure
            return response()->json([
                'message' => 'CSV export would be generated here',
                'data' => $data
            ]);
        }

        return response()->json($data);
    }

    /**
     * Get real-time sales summary for dashboard widgets.
     */
    public function realtimeSummary(Request $request): JsonResponse
    {
        $period = $request->get('period', 'today');
        
        switch ($period) {
            case 'today':
                $days = 1;
                break;
            case 'week':
                $days = 7;
                break;
            case 'month':
                $days = 30;
                break;
            default:
                $days = 1;
        }

        $overview = $this->salesAnalytics->getSalesOverview($days);
        
        return response()->json([
            'revenue' => $overview['current']['total_revenue'],
            'orders' => $overview['current']['total_orders'],
            'customers' => $overview['current']['unique_customers'],
            'avg_order_value' => $overview['current']['average_order_value'],
            'growth' => [
                'revenue' => $overview['growth']['total_revenue'] ?? 0,
                'orders' => $overview['growth']['total_orders'] ?? 0,
                'customers' => $overview['growth']['unique_customers'] ?? 0,
            ],
            'period' => $period,
        ]);
    }

    /**
     * Get sales performance metrics.
     */
    public function performanceMetrics(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $days = min(max($days, 1), 365);

        $overview = $this->salesAnalytics->getSalesOverview($days);
        $customerAnalytics = $this->salesAnalytics->getCustomerAnalytics($days);
        
        return response()->json([
            'conversion_rate' => $overview['current']['conversion_rate'],
            'average_order_value' => $overview['current']['average_order_value'],
            'customer_lifetime_value' => $customerAnalytics['customer_ltv'],
            'new_customer_rate' => $customerAnalytics['new_customer_rate'],
            'revenue_per_customer' => $overview['current']['unique_customers'] > 0 ? 
                round($overview['current']['total_revenue'] / $overview['current']['unique_customers'], 2) : 0,
            'orders_per_customer' => $customerAnalytics['avg_orders_per_customer'],
        ]);
    }
}
