<?php

namespace App\Http\Controllers;

use App\Models\Wishlist;
use App\Models\WishlistItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class WishlistController extends Controller
{
    /**
     * Display user's wishlists.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();
        
        $wishlists = $user->wishlists()
            ->withCount('items')
            ->with(['items.product:id,name,slug,price,sale_price,image'])
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return Inertia::render('wishlists/index', [
            'wishlists' => $wishlists,
        ]);
    }

    /**
     * Display a specific wishlist.
     */
    public function show(Request $request, Wishlist $wishlist): Response
    {
        // Check if user owns the wishlist or if it's public
        if ($wishlist->user_id !== Auth::id() && !$wishlist->is_public) {
            abort(403, 'This wishlist is private.');
        }

        $wishlist->load([
            'items.product:id,name,slug,description,short_description,price,sale_price,image,average_rating,total_reviews,reviews_enabled',
            'user:id,name'
        ]);

        return Inertia::render('wishlists/show', [
            'wishlist' => $wishlist,
            'stats' => $wishlist->getStats(),
        ]);
    }

    /**
     * Create a new wishlist.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $wishlist = Auth::user()->wishlists()->create([
            'name' => $request->name,
            'description' => $request->description,
            'is_public' => $request->boolean('is_public', false),
        ]);

        return response()->json([
            'message' => 'Wishlist created successfully',
            'wishlist' => $wishlist
        ], 201);
    }

    /**
     * Update a wishlist.
     */
    public function update(Request $request, Wishlist $wishlist): JsonResponse
    {
        if ($wishlist->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'is_public' => 'boolean',
            'is_default' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $wishlist->update($request->only(['name', 'description', 'is_public', 'is_default']));

        return response()->json([
            'message' => 'Wishlist updated successfully',
            'wishlist' => $wishlist
        ]);
    }

    /**
     * Delete a wishlist.
     */
    public function destroy(Wishlist $wishlist): JsonResponse
    {
        if ($wishlist->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($wishlist->is_default) {
            return response()->json([
                'message' => 'Cannot delete default wishlist'
            ], 422);
        }

        $wishlist->delete();

        return response()->json(['message' => 'Wishlist deleted successfully']);
    }

    /**
     * Add a product to wishlist.
     */
    public function addProduct(Request $request, Wishlist $wishlist): JsonResponse
    {
        if ($wishlist->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'notes' => 'nullable|string|max:500',
            'priority' => 'nullable|integer|min:0|max:10',
            'notify_on_sale' => 'boolean',
            'notify_on_restock' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $product = Product::findOrFail($request->product_id);
        
        $item = $wishlist->addProduct($product, [
            'notes' => $request->notes,
            'priority' => $request->priority ?? 0,
            'notify_on_sale' => $request->boolean('notify_on_sale', false),
            'notify_on_restock' => $request->boolean('notify_on_restock', false),
        ]);

        return response()->json([
            'message' => 'Product added to wishlist',
            'item' => $item->load('product:id,name,slug,price,sale_price,image')
        ]);
    }

    /**
     * Remove a product from wishlist.
     */
    public function removeProduct(Request $request, Wishlist $wishlist): JsonResponse
    {
        if ($wishlist->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $product = Product::findOrFail($request->product_id);
        $removed = $wishlist->removeProduct($product);

        if (!$removed) {
            return response()->json([
                'message' => 'Product not found in wishlist'
            ], 404);
        }

        return response()->json(['message' => 'Product removed from wishlist']);
    }

    /**
     * Quick add to default wishlist.
     */
    public function quickAdd(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $defaultWishlist = Wishlist::getDefaultForUser($user->id);
        $product = Product::findOrFail($request->product_id);

        $item = $defaultWishlist->addProduct($product);

        return response()->json([
            'message' => 'Product added to wishlist',
            'wishlist_id' => $defaultWishlist->id,
            'item' => $item->load('product:id,name,slug,price,sale_price,image')
        ]);
    }

    /**
     * Quick remove from default wishlist.
     */
    public function quickRemove(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $defaultWishlist = Wishlist::getDefaultForUser($user->id);
        $product = Product::findOrFail($request->product_id);

        $removed = $defaultWishlist->removeProduct($product);

        return response()->json([
            'message' => $removed ? 'Product removed from wishlist' : 'Product not in wishlist',
            'removed' => $removed
        ]);
    }

    /**
     * Check if product is in user's wishlist.
     */
    public function checkProduct(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $product = Product::findOrFail($request->product_id);
        
        $wishlists = $user->wishlists()
            ->whereHas('items', function ($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->get(['id', 'name']);

        return response()->json([
            'in_wishlist' => $wishlists->isNotEmpty(),
            'wishlists' => $wishlists
        ]);
    }

    /**
     * Move all items to cart.
     */
    public function moveToCart(Wishlist $wishlist): JsonResponse
    {
        if ($wishlist->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $results = $wishlist->moveAllToCart();

        return response()->json([
            'message' => 'Items moved to cart',
            'results' => $results
        ]);
    }

    /**
     * View public wishlist.
     */
    public function viewPublic(string $uuid): Response
    {
        $wishlist = Wishlist::where('uuid', $uuid)
            ->where('is_public', true)
            ->with([
                'items.product:id,name,slug,description,short_description,price,sale_price,image,average_rating,total_reviews',
                'user:id,name'
            ])
            ->firstOrFail();

        return Inertia::render('wishlists/public', [
            'wishlist' => $wishlist,
            'stats' => $wishlist->getStats(),
        ]);
    }
}
