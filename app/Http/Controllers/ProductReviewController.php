<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductReview;
use App\Models\ReviewHelpfulVote;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProductReviewController extends Controller
{
    /**
     * Display reviews for a product.
     */
    public function index(Request $request, Product $product): JsonResponse
    {
        $perPage = $request->get('per_page', 10);
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $rating = $request->get('rating');
        $verifiedOnly = $request->get('verified_only', false);

        $query = $product->reviews()
            ->with(['user:id,name', 'helpfulVotes'])
            ->approved();

        // Filter by rating
        if ($rating) {
            $query->where('rating', $rating);
        }

        // Filter by verified purchases only
        if ($verifiedOnly) {
            $query->where('is_verified_purchase', true);
        }

        // Sort reviews
        $allowedSortFields = ['created_at', 'rating', 'helpful_votes_count'];
        if (in_array($sortBy, $allowedSortFields)) {
            if ($sortBy === 'helpful_votes_count') {
                $query->withCount(['helpfulVotes as helpful_votes_count' => function ($q) {
                    $q->where('is_helpful', true);
                }])->orderBy('helpful_votes_count', $sortOrder);
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }
        }

        $reviews = $query->paginate($perPage);

        // Add helpful vote status for authenticated user
        if (Auth::check()) {
            $reviews->getCollection()->transform(function ($review) {
                $review->user_helpful_vote = $review->helpfulVotes()
                    ->where('user_id', Auth::id())
                    ->first();
                return $review;
            });
        }

        return response()->json([
            'reviews' => $reviews,
            'product_rating_stats' => [
                'average_rating' => $product->average_rating,
                'total_reviews' => $product->total_reviews,
                'rating_breakdown' => $product->rating_breakdown,
                'rating_breakdown_percentages' => $product->getRatingBreakdownPercentages(),
            ]
        ]);
    }

    /**
     * Store a new review.
     */
    public function store(Request $request, Product $product): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['message' => 'Authentication required'], 401);
        }

        $user = Auth::user();

        // Check if user can review this product
        if (!$product->canBeReviewedBy($user->id)) {
            return response()->json([
                'message' => 'You cannot review this product. Either reviews are disabled or you have already reviewed it.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string|max:2000',
            'rating' => 'required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $review = ProductReview::create([
            'product_id' => $product->id,
            'user_id' => $user->id,
            'title' => $request->title,
            'content' => $request->content,
            'rating' => $request->rating,
            'status' => 'pending', // Reviews need approval by default
        ]);

        return response()->json([
            'message' => 'Review submitted successfully. It will be published after approval.',
            'review' => $review->load('user:id,name')
        ], 201);
    }

    /**
     * Update a review.
     */
    public function update(Request $request, Product $product, ProductReview $review): JsonResponse
    {
        if (!Auth::check() || Auth::id() !== $review->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Only allow editing if review is not approved yet or within 24 hours of creation
        if ($review->is_approved && $review->created_at->diffInHours(now()) > 24) {
            return response()->json([
                'message' => 'Cannot edit approved reviews after 24 hours'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string|max:2000',
            'rating' => 'sometimes|required|integer|min:1|max:5',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $review->update($request->only(['title', 'content', 'rating']));

        // Reset approval status if review was modified
        if ($review->is_approved) {
            $review->update([
                'is_approved' => false,
                'status' => 'pending',
                'approved_at' => null,
                'approved_by' => null,
            ]);
        }

        return response()->json([
            'message' => 'Review updated successfully',
            'review' => $review->load('user:id,name')
        ]);
    }

    /**
     * Delete a review.
     */
    public function destroy(Product $product, ProductReview $review): JsonResponse
    {
        if (!Auth::check() || Auth::id() !== $review->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $review->delete();

        return response()->json(['message' => 'Review deleted successfully']);
    }

    /**
     * Vote on review helpfulness.
     */
    public function voteHelpful(Request $request, Product $product, ProductReview $review): JsonResponse
    {
        if (!Auth::check()) {
            return response()->json(['message' => 'Authentication required'], 401);
        }

        $validator = Validator::make($request->all(), [
            'is_helpful' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        // Check if user already voted on this review
        $existingVote = ReviewHelpfulVote::where('review_id', $review->id)
            ->where('user_id', $user->id)
            ->first();

        if ($existingVote) {
            // Update existing vote
            $existingVote->update([
                'is_helpful' => $request->is_helpful,
                'ip_address' => $request->ip(),
            ]);
        } else {
            // Create new vote
            ReviewHelpfulVote::create([
                'review_id' => $review->id,
                'user_id' => $user->id,
                'is_helpful' => $request->is_helpful,
                'ip_address' => $request->ip(),
            ]);
        }

        // Get updated vote counts
        $helpfulCount = $review->helpfulVotes()->where('is_helpful', true)->count();
        $notHelpfulCount = $review->helpfulVotes()->where('is_helpful', false)->count();

        return response()->json([
            'message' => 'Vote recorded successfully',
            'helpful_count' => $helpfulCount,
            'not_helpful_count' => $notHelpfulCount,
        ]);
    }

    /**
     * Get review statistics for a product.
     */
    public function stats(Product $product): JsonResponse
    {
        return response()->json([
            'average_rating' => $product->average_rating,
            'total_reviews' => $product->total_reviews,
            'total_ratings' => $product->total_ratings,
            'rating_breakdown' => $product->rating_breakdown,
            'rating_breakdown_percentages' => $product->getRatingBreakdownPercentages(),
            'reviews_enabled' => $product->reviews_enabled,
        ]);
    }
}
