<?php

namespace App\Http\Controllers;

use App\Services\DownloadTokenService;
use App\Services\DigitalAssetService;
use App\Models\DownloadLog;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class SecureDownloadController extends Controller
{
    protected $tokenService;
    protected $assetService;

    public function __construct(DownloadTokenService $tokenService, DigitalAssetService $assetService)
    {
        $this->tokenService = $tokenService;
        $this->assetService = $assetService;
    }

    /**
     * Handle secure file download with token validation.
     */
    public function download(Request $request, string $token)
    {
        try {
            // Validate and use the token
            $result = $this->tokenService->validateAndUseToken($token);

            if (!$result['success']) {
                return $this->handleDownloadError($result['error'], $result['code']);
            }

            $asset = $result['asset'];
            $downloadToken = $result['token'];

            // Check if asset is active
            if (!$asset->is_active) {
                Log::warning('Attempt to download inactive asset', [
                    'asset_id' => $asset->id,
                    'token_id' => $downloadToken->id,
                    'ip_address' => $request->ip(),
                ]);

                return $this->handleDownloadError('This file is no longer available for download.', 'FILE_INACTIVE');
            }

            // Check if file exists in storage
            if (!$asset->fileExists()) {
                Log::error('File not found in storage', [
                    'asset_id' => $asset->id,
                    'file_path' => $asset->file_path,
                    'storage_disk' => $asset->storage_disk,
                ]);

                return $this->handleDownloadError('File not found in storage.', 'FILE_NOT_FOUND');
            }

            // Log the download
            $this->logDownload($asset, $downloadToken, $request);

            // Increment asset download count
            $asset->increment('download_count');

            // Stream the file download
            return $this->streamFileDownload($asset);

        } catch (\Exception $e) {
            Log::error('Secure download error', [
                'token' => $token,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip_address' => $request->ip(),
            ]);

            return $this->handleDownloadError('An error occurred while processing your download.', 'SYSTEM_ERROR');
        }
    }

    /**
     * Stream file download with proper headers.
     */
    protected function streamFileDownload($asset): StreamedResponse
    {
        $disk = Storage::disk($asset->storage_disk);
        $filePath = $asset->file_path;

        return new StreamedResponse(function () use ($disk, $filePath) {
            $stream = $disk->readStream($filePath);
            
            if ($stream === false) {
                throw new \Exception('Unable to read file stream');
            }

            fpassthru($stream);
            fclose($stream);
        }, 200, [
            'Content-Type' => $asset->mime_type,
            'Content-Disposition' => 'attachment; filename="' . $asset->original_name . '"',
            'Content-Length' => $asset->file_size,
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
            'X-Robots-Tag' => 'noindex, nofollow',
        ]);
    }

    /**
     * Log download activity.
     */
    protected function logDownload($asset, $token, Request $request): void
    {
        try {
            DownloadLog::create([
                'digital_asset_id' => $asset->id,
                'user_id' => auth()->id(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'downloaded_at' => now(),
            ]);

            Log::info('File downloaded successfully', [
                'asset_id' => $asset->id,
                'asset_name' => $asset->original_name,
                'token_id' => $token->id,
                'user_id' => auth()->id(),
                'ip_address' => $request->ip(),
                'file_size' => $asset->file_size,
            ]);
        } catch (\Exception $e) {
            // Don't fail the download if logging fails
            Log::error('Failed to log download', [
                'asset_id' => $asset->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle download errors with appropriate responses.
     */
    protected function handleDownloadError(string $message, string $code): Response
    {
        $statusCode = match ($code) {
            'INVALID_TOKEN', 'VALIDATION_FAILED' => 403,
            'TOKEN_EXHAUSTED' => 410,
            'FILE_NOT_FOUND', 'FILE_INACTIVE' => 404,
            'RATE_LIMITED' => 429,
            default => 500,
        };

        return response()->view('errors.download', [
            'message' => $message,
            'code' => $code,
        ], $statusCode);
    }

    /**
     * Generate a new download token for an asset (admin only).
     */
    public function generateToken(Request $request, int $assetId)
    {
        $this->authorize('admin');

        $request->validate([
            'expires_in_hours' => 'nullable|integer|min:1|max:168', // Max 1 week
            'max_downloads' => 'nullable|integer|min:1|max:100',
            'user_id' => 'nullable|exists:users,id',
        ]);

        try {
            $asset = \App\Models\DigitalAsset::findOrFail($assetId);
            $user = $request->user_id ? \App\Models\User::find($request->user_id) : null;

            $options = array_filter([
                'expires_in_hours' => $request->expires_in_hours,
                'max_downloads' => $request->max_downloads,
            ]);

            $downloadUrl = $this->tokenService->generateSecureDownloadUrl($asset, $user, $options);

            return response()->json([
                'success' => true,
                'download_url' => $downloadUrl,
                'expires_at' => now()->addHours($request->expires_in_hours ?? 24)->toISOString(),
                'max_downloads' => $request->max_downloads ?? 1,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to generate download token: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get download statistics for an asset (admin only).
     */
    public function getDownloadStats(int $assetId)
    {
        $this->authorize('admin');

        try {
            $asset = \App\Models\DigitalAsset::findOrFail($assetId);
            $stats = $this->tokenService->getAssetDownloadStats($asset);

            return response()->json([
                'success' => true,
                'stats' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to get download statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Revoke download tokens for an asset (admin only).
     */
    public function revokeTokens(Request $request, int $assetId)
    {
        $this->authorize('admin');

        try {
            $asset = \App\Models\DigitalAsset::findOrFail($assetId);
            $revokedCount = $this->tokenService->revokeAssetTokens($asset);

            return response()->json([
                'success' => true,
                'message' => "Revoked {$revokedCount} download tokens.",
                'revoked_count' => $revokedCount,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to revoke tokens: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clean up expired tokens (admin only).
     */
    public function cleanupExpiredTokens()
    {
        $this->authorize('admin');

        try {
            $cleanedCount = $this->tokenService->cleanupExpiredTokens();

            return response()->json([
                'success' => true,
                'message' => "Cleaned up {$cleanedCount} expired tokens.",
                'cleaned_count' => $cleanedCount,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to cleanup tokens: ' . $e->getMessage(),
            ], 500);
        }
    }
}
