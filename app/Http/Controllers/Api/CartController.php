<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CartController extends Controller
{
    protected CartService $cartService;

    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    /**
     * Get cart contents.
     */
    public function index(): JsonResponse
    {
        try {
            $cartSummary = $this->cartService->getCartSummary();

            return response()->json([
                'success' => true,
                'data' => $cartSummary,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve cart',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Add item to cart.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'product_id' => 'required|integer|exists:products,id',
                'quantity' => 'integer|min:1|max:100',
                'options' => 'array',
            ]);

            $quantity = $validated['quantity'] ?? 1;
            $options = $validated['options'] ?? [];

            $cartItem = $this->cartService->addToCart(
                $validated['product_id'],
                $quantity,
                $options
            );

            $cartSummary = $this->cartService->getCartSummary();

            return response()->json([
                'success' => true,
                'message' => 'Item added to cart successfully',
                'data' => [
                    'cart_item' => $cartItem,
                    'cart' => $cartSummary,
                ],
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add item to cart',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update cart item quantity.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'quantity' => 'required|integer|min:1|max:100',
            ]);

            $success = $this->cartService->updateQuantity((int)$id, $validated['quantity']);

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart item not found or update failed',
                ], 404);
            }

            $cartSummary = $this->cartService->getCartSummary();

            return response()->json([
                'success' => true,
                'message' => 'Cart item updated successfully',
                'data' => $cartSummary,
            ]);
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart item',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove item from cart.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $success = $this->cartService->removeItem((int)$id);

            if (!$success) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cart item not found',
                ], 404);
            }

            $cartSummary = $this->cartService->getCartSummary();

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart successfully',
                'data' => $cartSummary,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove item from cart',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clear entire cart.
     */
    public function clear(): JsonResponse
    {
        try {
            $this->cartService->clearCart();

            return response()->json([
                'success' => true,
                'message' => 'Cart cleared successfully',
                'data' => $this->cartService->getCartSummary(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cart',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get cart item count.
     */
    public function count(): JsonResponse
    {
        try {
            $count = $this->cartService->getCartItemCount();

            return response()->json([
                'success' => true,
                'data' => [
                    'count' => $count,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get cart count',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate cart before checkout.
     */
    public function validate(): JsonResponse
    {
        try {
            $errors = $this->cartService->validateCart();

            return response()->json([
                'success' => empty($errors),
                'data' => [
                    'is_valid' => empty($errors),
                    'errors' => $errors,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate cart',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
