<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ShopController extends Controller
{
    /**
     * Display the main shop page with products.
     */
    public function index(Request $request): Response
    {
        $query = Product::with(['category', 'files'])
            ->where('status', 'active')
            ->where('is_active', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Category filter
        if ($request->filled('category')) {
            $categoryId = $request->get('category');
            $query->where('category_id', $categoryId);
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // Featured filter
        if ($request->filled('featured') && $request->get('featured') === 'true') {
            $query->where('featured', true);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'price', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(12)->withQueryString();

        // Get categories for filter
        $categories = ProductCategory::select('id', 'name', 'slug')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get price range for filter
        $priceRange = Product::where('status', 'active')
            ->where('is_active', true)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        return Inertia::render('shop/index', [
            'products' => $products,
            'categories' => $categories,
            'priceRange' => $priceRange,
            'filters' => $request->only(['search', 'category', 'min_price', 'max_price', 'featured']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Display a specific product.
     */
    public function show(Request $request, string $slug): Response
    {
        $product = Product::with([
            'category',
            'files' => function ($query) {
                $query->where('is_active', true);
            },
            'reviews' => function ($query) {
                $query->where('is_active', true)
                      ->with('user:id,name')
                      ->orderBy('created_at', 'desc');
            }
        ])
        ->where('slug', $slug)
        ->where('status', 'active')
        ->where('is_active', true)
        ->firstOrFail();

        // Get related products
        $relatedProducts = Product::with(['category', 'files'])
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('status', 'active')
            ->where('is_active', true)
            ->limit(4)
            ->get();

        // Calculate average rating
        $averageRating = $product->reviews->avg('rating');
        $reviewCount = $product->reviews->count();

        return Inertia::render('shop/product', [
            'product' => $product,
            'relatedProducts' => $relatedProducts,
            'averageRating' => $averageRating ? round($averageRating, 1) : null,
            'reviewCount' => $reviewCount,
        ]);
    }

    /**
     * Display products by category.
     */
    public function category(Request $request, string $slug): Response
    {
        $category = ProductCategory::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        $query = Product::with(['category', 'files'])
            ->where('category_id', $category->id)
            ->where('status', 'active')
            ->where('is_active', true);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        // Price range filter
        if ($request->filled('min_price')) {
            $query->where('price', '>=', $request->get('min_price'));
        }
        if ($request->filled('max_price')) {
            $query->where('price', '<=', $request->get('max_price'));
        }

        // Featured filter
        if ($request->filled('featured') && $request->get('featured') === 'true') {
            $query->where('featured', true);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        $allowedSorts = ['name', 'price', 'created_at', 'updated_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(12)->withQueryString();

        // Get subcategories
        $subcategories = ProductCategory::select('id', 'name', 'slug')
            ->where('parent_id', $category->id)
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        // Get price range for filter
        $priceRange = Product::where('category_id', $category->id)
            ->where('status', 'active')
            ->where('is_active', true)
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        return Inertia::render('shop/category', [
            'category' => $category,
            'products' => $products,
            'subcategories' => $subcategories,
            'priceRange' => $priceRange,
            'filters' => $request->only(['search', 'min_price', 'max_price', 'featured']),
            'sort' => [
                'by' => $sortBy,
                'order' => $sortOrder,
            ],
        ]);
    }

    /**
     * Get featured products for homepage.
     */
    public function featured(): \Illuminate\Http\JsonResponse
    {
        $products = Product::with(['category', 'files'])
            ->where('featured', true)
            ->where('status', 'active')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(8)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products,
        ]);
    }

    /**
     * Get new arrivals.
     */
    public function newArrivals(): \Illuminate\Http\JsonResponse
    {
        $products = Product::with(['category', 'files'])
            ->where('status', 'active')
            ->where('is_active', true)
            ->orderBy('created_at', 'desc')
            ->limit(8)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $products,
        ]);
    }

    /**
     * Get categories for navigation.
     */
    public function categories(): \Illuminate\Http\JsonResponse
    {
        $categories = ProductCategory::select('id', 'name', 'slug', 'parent_id')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }
}
