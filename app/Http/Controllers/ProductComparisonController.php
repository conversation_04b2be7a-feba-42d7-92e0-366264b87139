<?php

namespace App\Http\Controllers;

use App\Models\ProductComparison;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;
use Inertia\Response;

class ProductComparisonController extends Controller
{
    /**
     * Display user's comparisons.
     */
    public function index(Request $request): Response
    {
        $comparisons = collect();

        if (Auth::check()) {
            $comparisons = ProductComparison::forUser(Auth::id())
                ->active()
                ->orderBy('created_at', 'desc')
                ->get();
        } else {
            $sessionId = Session::getId();
            $comparisons = ProductComparison::forSession($sessionId)
                ->active()
                ->orderBy('created_at', 'desc')
                ->get();
        }

        // Load product data for each comparison
        $comparisons->each(function ($comparison) {
            $comparison->products_data = $comparison->products();
        });

        return Inertia::render('comparisons/index', [
            'comparisons' => $comparisons,
        ]);
    }

    /**
     * Display a specific comparison.
     */
    public function show(Request $request, ProductComparison $comparison): Response
    {
        // Check access permissions
        if (!$this->canAccessComparison($comparison)) {
            abort(403, 'You do not have access to this comparison.');
        }

        $comparisonData = $comparison->getComparisonData();

        return Inertia::render('comparisons/show', [
            'comparison' => $comparison,
            'comparisonData' => $comparisonData,
        ]);
    }

    /**
     * Create a new comparison.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'product_ids' => 'required|array|min:1|max:4',
            'product_ids.*' => 'exists:products,id',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = [
            'name' => $request->name ?: 'Product Comparison',
            'product_ids' => $request->product_ids,
            'is_public' => $request->boolean('is_public', false),
        ];

        if (Auth::check()) {
            $data['user_id'] = Auth::id();
        } else {
            $data['session_id'] = Session::getId();
        }

        $comparison = ProductComparison::create($data);

        return response()->json([
            'message' => 'Comparison created successfully',
            'comparison' => $comparison,
            'redirect_url' => route('comparisons.show', $comparison)
        ], 201);
    }

    /**
     * Update a comparison.
     */
    public function update(Request $request, ProductComparison $comparison): JsonResponse
    {
        if (!$this->canModifyComparison($comparison)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'is_public' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $comparison->update($request->only(['name', 'is_public']));

        return response()->json([
            'message' => 'Comparison updated successfully',
            'comparison' => $comparison
        ]);
    }

    /**
     * Delete a comparison.
     */
    public function destroy(ProductComparison $comparison): JsonResponse
    {
        if (!$this->canModifyComparison($comparison)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $comparison->delete();

        return response()->json(['message' => 'Comparison deleted successfully']);
    }

    /**
     * Add a product to comparison.
     */
    public function addProduct(Request $request, ProductComparison $comparison): JsonResponse
    {
        if (!$this->canModifyComparison($comparison)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $productId = $request->product_id;

        if ($comparison->hasProduct($productId)) {
            return response()->json([
                'message' => 'Product already in comparison'
            ], 422);
        }

        if ($comparison->isFull()) {
            return response()->json([
                'message' => 'Comparison is full (maximum 4 products)'
            ], 422);
        }

        $added = $comparison->addProduct($productId);

        if (!$added) {
            return response()->json([
                'message' => 'Failed to add product to comparison'
            ], 422);
        }

        return response()->json([
            'message' => 'Product added to comparison',
            'comparison' => $comparison->fresh()
        ]);
    }

    /**
     * Remove a product from comparison.
     */
    public function removeProduct(Request $request, ProductComparison $comparison): JsonResponse
    {
        if (!$this->canModifyComparison($comparison)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $productId = $request->product_id;
        $removed = $comparison->removeProduct($productId);

        if (!$removed) {
            return response()->json([
                'message' => 'Product not found in comparison'
            ], 404);
        }

        return response()->json([
            'message' => 'Product removed from comparison',
            'comparison' => $comparison->fresh()
        ]);
    }

    /**
     * Quick compare products.
     */
    public function quickCompare(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_ids' => 'required|array|min:2|max:4',
            'product_ids.*' => 'exists:products,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Create temporary comparison
        $comparison = new ProductComparison([
            'name' => 'Quick Comparison',
            'product_ids' => $request->product_ids,
        ]);

        $comparisonData = $comparison->getComparisonData();

        return response()->json([
            'comparisonData' => $comparisonData
        ]);
    }

    /**
     * Get current comparison for user/session.
     */
    public function getCurrent(Request $request): JsonResponse
    {
        $comparison = null;

        if (Auth::check()) {
            $comparison = ProductComparison::forUser(Auth::id())
                ->active()
                ->latest()
                ->first();
        } else {
            $sessionId = Session::getId();
            $comparison = ProductComparison::forSession($sessionId)
                ->active()
                ->latest()
                ->first();
        }

        if (!$comparison) {
            return response()->json([
                'comparison' => null,
                'products' => []
            ]);
        }

        return response()->json([
            'comparison' => $comparison,
            'products' => $comparison->products()
        ]);
    }

    /**
     * View public comparison.
     */
    public function viewPublic(string $uuid): Response
    {
        $comparison = ProductComparison::where('uuid', $uuid)
            ->where('is_public', true)
            ->active()
            ->firstOrFail();

        $comparisonData = $comparison->getComparisonData();

        return Inertia::render('comparisons/public', [
            'comparison' => $comparison,
            'comparisonData' => $comparisonData,
        ]);
    }

    /**
     * Check if user can access comparison.
     */
    protected function canAccessComparison(ProductComparison $comparison): bool
    {
        if ($comparison->is_public) {
            return true;
        }

        if (Auth::check() && $comparison->user_id === Auth::id()) {
            return true;
        }

        if (!Auth::check() && $comparison->session_id === Session::getId()) {
            return true;
        }

        return false;
    }

    /**
     * Check if user can modify comparison.
     */
    protected function canModifyComparison(ProductComparison $comparison): bool
    {
        if (Auth::check() && $comparison->user_id === Auth::id()) {
            return true;
        }

        if (!Auth::check() && $comparison->session_id === Session::getId()) {
            return true;
        }

        return false;
    }
}
