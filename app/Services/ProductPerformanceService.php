<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductView;
use App\Models\OrderItem;
use App\Models\DownloadLog;
use App\Models\ProductReview;
use App\Models\WishlistItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class ProductPerformanceService
{
    /**
     * Get comprehensive product performance metrics.
     */
    public function getProductPerformance(int $productId, int $days = 30): array
    {
        $cacheKey = "product_performance_{$productId}_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($productId, $days) {
            $startDate = now()->subDays($days);
            $product = Product::findOrFail($productId);
            
            return [
                'product' => $product,
                'views' => $this->getViewMetrics($productId, $days),
                'sales' => $this->getSalesMetrics($productId, $days),
                'downloads' => $this->getDownloadMetrics($productId, $days),
                'reviews' => $this->getReviewMetrics($productId, $days),
                'wishlist' => $this->getWishlistMetrics($productId, $days),
                'conversion' => $this->getConversionMetrics($productId, $days),
                'trends' => $this->getPerformanceTrends($productId, $days),
                'period_days' => $days,
                'start_date' => $startDate->toDateString(),
                'end_date' => now()->toDateString(),
            ];
        });
    }

    /**
     * Get view metrics for a product.
     */
    protected function getViewMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        $viewStats = ProductView::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('
                COUNT(*) as total_views,
                COUNT(DISTINCT user_id) as unique_viewers,
                COUNT(DISTINCT session_id) as unique_sessions,
                AVG(view_duration) as avg_duration,
                COUNT(CASE WHEN bounced THEN 1 END) as bounced_views
            ')
            ->first();

        $bounceRate = $viewStats->total_views > 0 ? 
            ($viewStats->bounced_views / $viewStats->total_views) * 100 : 0;

        // Get traffic sources
        $sources = ProductView::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('source')
            ->groupBy('source')
            ->selectRaw('source, COUNT(*) as count')
            ->orderBy('count', 'desc')
            ->get()
            ->toArray();

        return [
            'total_views' => $viewStats->total_views ?? 0,
            'unique_viewers' => $viewStats->unique_viewers ?? 0,
            'unique_sessions' => $viewStats->unique_sessions ?? 0,
            'avg_duration' => round($viewStats->avg_duration ?? 0, 1),
            'bounce_rate' => round($bounceRate, 2),
            'traffic_sources' => $sources,
        ];
    }

    /**
     * Get sales metrics for a product.
     */
    protected function getSalesMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        $salesStats = OrderItem::where('product_id', $productId)
            ->whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->selectRaw('
                SUM(quantity) as total_sold,
                SUM(unit_price * quantity) as total_revenue,
                COUNT(DISTINCT order_id) as order_count,
                AVG(unit_price) as avg_price
            ')
            ->first();

        return [
            'total_sold' => $salesStats->total_sold ?? 0,
            'total_revenue' => round($salesStats->total_revenue ?? 0, 2),
            'order_count' => $salesStats->order_count ?? 0,
            'avg_price' => round($salesStats->avg_price ?? 0, 2),
        ];
    }

    /**
     * Get download metrics for a product.
     */
    protected function getDownloadMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        $downloadStats = DownloadLog::whereHas('digitalAsset', function ($query) use ($productId) {
                $query->where('product_id', $productId);
            })
            ->where('created_at', '>=', $startDate)
            ->selectRaw('
                COUNT(*) as total_downloads,
                COUNT(DISTINCT user_id) as unique_downloaders,
                COUNT(CASE WHEN download_successful THEN 1 END) as successful_downloads
            ')
            ->first();

        $successRate = $downloadStats->total_downloads > 0 ? 
            ($downloadStats->successful_downloads / $downloadStats->total_downloads) * 100 : 0;

        return [
            'total_downloads' => $downloadStats->total_downloads ?? 0,
            'unique_downloaders' => $downloadStats->unique_downloaders ?? 0,
            'successful_downloads' => $downloadStats->successful_downloads ?? 0,
            'success_rate' => round($successRate, 2),
        ];
    }

    /**
     * Get review metrics for a product.
     */
    protected function getReviewMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        $reviewStats = ProductReview::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->where('status', 'approved')
            ->selectRaw('
                COUNT(*) as total_reviews,
                AVG(rating) as avg_rating,
                COUNT(CASE WHEN rating >= 4 THEN 1 END) as positive_reviews,
                COUNT(CASE WHEN rating <= 2 THEN 1 END) as negative_reviews
            ')
            ->first();

        $positiveRate = $reviewStats->total_reviews > 0 ? 
            ($reviewStats->positive_reviews / $reviewStats->total_reviews) * 100 : 0;

        return [
            'total_reviews' => $reviewStats->total_reviews ?? 0,
            'avg_rating' => round($reviewStats->avg_rating ?? 0, 2),
            'positive_reviews' => $reviewStats->positive_reviews ?? 0,
            'negative_reviews' => $reviewStats->negative_reviews ?? 0,
            'positive_rate' => round($positiveRate, 2),
        ];
    }

    /**
     * Get wishlist metrics for a product.
     */
    protected function getWishlistMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        $wishlistStats = WishlistItem::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->selectRaw('
                COUNT(*) as total_additions,
                COUNT(DISTINCT wishlist_id) as unique_wishlists
            ')
            ->first();

        return [
            'total_additions' => $wishlistStats->total_additions ?? 0,
            'unique_wishlists' => $wishlistStats->unique_wishlists ?? 0,
        ];
    }

    /**
     * Get conversion metrics for a product.
     */
    protected function getConversionMetrics(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        // View to purchase conversion
        $views = ProductView::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->count();
        
        $purchases = OrderItem::where('product_id', $productId)
            ->whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->sum('quantity');

        $viewToPurchaseRate = $views > 0 ? ($purchases / $views) * 100 : 0;

        // Wishlist to purchase conversion
        $wishlistAdditions = WishlistItem::where('product_id', $productId)
            ->where('created_at', '>=', $startDate)
            ->count();

        $wishlistToPurchaseRate = $wishlistAdditions > 0 ? 
            ($purchases / $wishlistAdditions) * 100 : 0;

        return [
            'view_to_purchase_rate' => round($viewToPurchaseRate, 2),
            'wishlist_to_purchase_rate' => round($wishlistToPurchaseRate, 2),
            'total_conversions' => $purchases,
        ];
    }

    /**
     * Get performance trends over time.
     */
    protected function getPerformanceTrends(int $productId, int $days): array
    {
        $startDate = now()->subDays($days);
        
        // Daily trends
        $dailyTrends = [];
        for ($i = $days - 1; $i >= 0; $i--) {
            $date = now()->subDays($i)->toDateString();
            $dayStart = now()->subDays($i)->startOfDay();
            $dayEnd = now()->subDays($i)->endOfDay();
            
            $views = ProductView::where('product_id', $productId)
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();
            
            $sales = OrderItem::where('product_id', $productId)
                ->whereHas('order', function ($query) use ($dayStart, $dayEnd) {
                    $query->whereBetween('created_at', [$dayStart, $dayEnd])
                          ->where('status', 'completed');
                })
                ->sum('quantity');
            
            $downloads = DownloadLog::whereHas('digitalAsset', function ($query) use ($productId) {
                    $query->where('product_id', $productId);
                })
                ->whereBetween('created_at', [$dayStart, $dayEnd])
                ->count();
            
            $dailyTrends[] = [
                'date' => $date,
                'views' => $views,
                'sales' => $sales,
                'downloads' => $downloads,
            ];
        }
        
        return [
            'daily' => $dailyTrends,
        ];
    }

    /**
     * Get top performing products.
     */
    public function getTopPerformingProducts(int $days = 30, int $limit = 10, string $metric = 'revenue'): array
    {
        $cacheKey = "top_performing_products_{$days}_{$limit}_{$metric}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days, $limit, $metric) {
            $startDate = now()->subDays($days);
            
            switch ($metric) {
                case 'views':
                    return $this->getTopViewedProducts($startDate, $limit);
                case 'sales':
                    return $this->getTopSellingProducts($startDate, $limit);
                case 'downloads':
                    return $this->getTopDownloadedProducts($startDate, $limit);
                case 'conversion':
                    return $this->getTopConvertingProducts($startDate, $limit);
                default: // revenue
                    return $this->getTopRevenueProducts($startDate, $limit);
            }
        });
    }

    /**
     * Get top viewed products.
     */
    protected function getTopViewedProducts(Carbon $startDate, int $limit): array
    {
        return ProductView::where('created_at', '>=', $startDate)
            ->with('product:id,name,slug,price,sale_price,image')
            ->groupBy('product_id')
            ->selectRaw('product_id, COUNT(*) as view_count, COUNT(DISTINCT user_id) as unique_viewers')
            ->orderBy('view_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($view) {
                return [
                    'product' => $view->product,
                    'metric_value' => $view->view_count,
                    'metric_label' => 'Views',
                    'secondary_metric' => $view->unique_viewers,
                    'secondary_label' => 'Unique Viewers',
                ];
            })
            ->toArray();
    }

    /**
     * Get top selling products.
     */
    protected function getTopSellingProducts(Carbon $startDate, int $limit): array
    {
        return OrderItem::whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->with('product:id,name,slug,price,sale_price,image')
            ->groupBy('product_id')
            ->selectRaw('product_id, SUM(quantity) as total_sold, COUNT(DISTINCT order_id) as order_count')
            ->orderBy('total_sold', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'product' => $item->product,
                    'metric_value' => $item->total_sold,
                    'metric_label' => 'Units Sold',
                    'secondary_metric' => $item->order_count,
                    'secondary_label' => 'Orders',
                ];
            })
            ->toArray();
    }

    /**
     * Get top revenue products.
     */
    protected function getTopRevenueProducts(Carbon $startDate, int $limit): array
    {
        return OrderItem::whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->with('product:id,name,slug,price,sale_price,image')
            ->groupBy('product_id')
            ->selectRaw('product_id, SUM(unit_price * quantity) as total_revenue, SUM(quantity) as total_sold')
            ->orderBy('total_revenue', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'product' => $item->product,
                    'metric_value' => round($item->total_revenue, 2),
                    'metric_label' => 'Revenue',
                    'secondary_metric' => $item->total_sold,
                    'secondary_label' => 'Units Sold',
                ];
            })
            ->toArray();
    }

    /**
     * Get product performance comparison.
     */
    public function compareProducts(array $productIds, int $days = 30): array
    {
        $comparison = [];
        
        foreach ($productIds as $productId) {
            $performance = $this->getProductPerformance($productId, $days);
            $comparison[] = [
                'product' => $performance['product'],
                'views' => $performance['views']['total_views'],
                'sales' => $performance['sales']['total_sold'],
                'revenue' => $performance['sales']['total_revenue'],
                'conversion_rate' => $performance['conversion']['view_to_purchase_rate'],
                'avg_rating' => $performance['reviews']['avg_rating'],
            ];
        }
        
        return $comparison;
    }

    /**
     * Get product performance summary for dashboard.
     */
    public function getPerformanceSummary(int $days = 7): array
    {
        $cacheKey = "performance_summary_{$days}";
        
        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);
            
            $totalViews = ProductView::where('created_at', '>=', $startDate)->count();
            $totalSales = OrderItem::whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })->sum('quantity');
            
            $totalRevenue = OrderItem::whereHas('order', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })->sum(DB::raw('unit_price * quantity'));
            
            $avgConversion = $totalViews > 0 ? ($totalSales / $totalViews) * 100 : 0;
            
            $topProduct = OrderItem::whereHas('order', function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                          ->where('status', 'completed');
                })
                ->with('product:id,name,slug')
                ->groupBy('product_id')
                ->selectRaw('product_id, SUM(quantity) as total_sold')
                ->orderBy('total_sold', 'desc')
                ->first();
            
            return [
                'total_views' => $totalViews,
                'total_sales' => $totalSales,
                'total_revenue' => round($totalRevenue, 2),
                'avg_conversion_rate' => round($avgConversion, 2),
                'top_product' => $topProduct?->product?->name,
                'top_product_sales' => $topProduct?->total_sold ?? 0,
            ];
        });
    }
}
