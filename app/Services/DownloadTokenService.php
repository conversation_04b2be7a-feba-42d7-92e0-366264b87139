<?php

namespace App\Services;

use App\Models\DigitalAsset;
use App\Models\DownloadToken;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class DownloadTokenService
{
    protected $defaultExpiryHours;
    protected $defaultMaxDownloads;
    protected $rateLimitPerHour;

    public function __construct()
    {
        $this->defaultExpiryHours = config('app.download_token_expiry_hours', 24);
        $this->defaultMaxDownloads = 1;
        $this->rateLimitPerHour = config('app.download_rate_limit_per_hour', 10);
    }

    /**
     * Generate a secure download token for a digital asset.
     */
    public function generateToken(
        DigitalAsset $asset,
        ?User $user = null,
        array $options = []
    ): DownloadToken {
        // Check rate limiting
        if (!$this->checkRateLimit($user, request()->ip())) {
            throw new \Exception('Download rate limit exceeded. Please try again later.');
        }

        $expiresAt = isset($options['expires_in_hours']) 
            ? now()->addHours($options['expires_in_hours'])
            : now()->addHours($this->defaultExpiryHours);

        $maxDownloads = $options['max_downloads'] ?? $this->defaultMaxDownloads;

        // Generate device fingerprint for additional security
        $deviceFingerprint = $this->generateDeviceFingerprint();

        $token = DownloadToken::create([
            'token' => $this->generateSecureToken(),
            'file_id' => $asset->id, // Using file_id to match existing schema
            'user_id' => $user?->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'device_fingerprint' => $deviceFingerprint,
            'expires_at' => $expiresAt,
            'download_count' => 0,
            'max_downloads' => $maxDownloads,
            'is_active' => true,
        ]);

        // Log token generation
        Log::info('Download token generated', [
            'token_id' => $token->id,
            'asset_id' => $asset->id,
            'user_id' => $user?->id,
            'ip_address' => request()->ip(),
            'expires_at' => $expiresAt,
        ]);

        return $token;
    }

    /**
     * Validate and use a download token.
     */
    public function validateAndUseToken(string $tokenString): array
    {
        $token = DownloadToken::where('token', $tokenString)->first();

        if (!$token) {
            return [
                'success' => false,
                'error' => 'Invalid download token.',
                'code' => 'INVALID_TOKEN'
            ];
        }

        // Validate the token
        if (!$token->validateRequest(
            request()->ip(),
            request()->userAgent(),
            auth()->id()
        )) {
            Log::warning('Download token validation failed', [
                'token_id' => $token->id,
                'ip_address' => request()->ip(),
                'user_id' => auth()->id(),
            ]);

            return [
                'success' => false,
                'error' => 'Token validation failed. This may be due to security restrictions.',
                'code' => 'VALIDATION_FAILED'
            ];
        }

        // Use the token
        if (!$token->use()) {
            return [
                'success' => false,
                'error' => 'Token cannot be used. It may be expired or exhausted.',
                'code' => 'TOKEN_EXHAUSTED'
            ];
        }

        // Load the associated asset
        $asset = DigitalAsset::find($token->file_id);
        if (!$asset) {
            return [
                'success' => false,
                'error' => 'Associated file not found.',
                'code' => 'FILE_NOT_FOUND'
            ];
        }

        return [
            'success' => true,
            'token' => $token,
            'asset' => $asset,
            'downloads_remaining' => $token->downloads_remaining,
        ];
    }

    /**
     * Generate a secure download URL with token.
     */
    public function generateSecureDownloadUrl(DigitalAsset $asset, ?User $user = null, array $options = []): string
    {
        $token = $this->generateToken($asset, $user, $options);
        
        return route('secure.download', ['token' => $token->token]);
    }

    /**
     * Check rate limiting for downloads.
     */
    protected function checkRateLimit(?User $user, string $ipAddress): bool
    {
        $key = $user ? "download_rate_user_{$user->id}" : "download_rate_ip_{$ipAddress}";
        
        $attempts = Cache::get($key, 0);
        
        if ($attempts >= $this->rateLimitPerHour) {
            return false;
        }

        Cache::put($key, $attempts + 1, now()->addHour());
        
        return true;
    }

    /**
     * Generate a cryptographically secure token.
     */
    protected function generateSecureToken(): string
    {
        return hash('sha256', Str::random(64) . microtime(true) . random_bytes(32));
    }

    /**
     * Generate device fingerprint for security.
     */
    protected function generateDeviceFingerprint(): string
    {
        $components = [
            request()->ip(),
            request()->userAgent(),
            request()->header('Accept-Language'),
            request()->header('Accept-Encoding'),
            request()->header('Accept'),
        ];
        
        return hash('sha256', implode('|', array_filter($components)));
    }

    /**
     * Revoke all tokens for a user.
     */
    public function revokeUserTokens(User $user): int
    {
        return DownloadToken::where('user_id', $user->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);
    }

    /**
     * Revoke all tokens for an asset.
     */
    public function revokeAssetTokens(DigitalAsset $asset): int
    {
        return DownloadToken::where('file_id', $asset->id)
            ->where('is_active', true)
            ->update(['is_active' => false]);
    }

    /**
     * Clean up expired tokens.
     */
    public function cleanupExpiredTokens(): int
    {
        return DownloadToken::cleanupExpired();
    }

    /**
     * Get download statistics for an asset.
     */
    public function getAssetDownloadStats(DigitalAsset $asset): array
    {
        $tokens = DownloadToken::where('file_id', $asset->id)->get();
        
        return [
            'total_tokens_generated' => $tokens->count(),
            'active_tokens' => $tokens->where('is_active', true)->count(),
            'expired_tokens' => $tokens->filter(fn($t) => $t->isExpired())->count(),
            'total_downloads' => $tokens->sum('download_count'),
            'unique_users' => $tokens->whereNotNull('user_id')->pluck('user_id')->unique()->count(),
            'unique_ips' => $tokens->pluck('ip_address')->unique()->count(),
            'recent_downloads' => $tokens->where('used_at', '>=', now()->subDays(7))->sum('download_count'),
        ];
    }

    /**
     * Get user download history.
     */
    public function getUserDownloadHistory(User $user, int $limit = 50): array
    {
        $tokens = DownloadToken::where('user_id', $user->id)
            ->with('file')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();

        return $tokens->map(function ($token) {
            return [
                'id' => $token->id,
                'asset_name' => $token->file->original_name ?? 'Unknown',
                'downloads_used' => $token->download_count,
                'max_downloads' => $token->max_downloads,
                'created_at' => $token->created_at,
                'expires_at' => $token->expires_at,
                'last_used_at' => $token->used_at,
                'is_active' => $token->is_active,
                'status' => $this->getTokenStatus($token),
            ];
        })->toArray();
    }

    /**
     * Get token status for display.
     */
    protected function getTokenStatus(DownloadToken $token): string
    {
        if (!$token->is_active) {
            return 'revoked';
        }

        if ($token->isExpired()) {
            return 'expired';
        }

        if ($token->hasReachedLimit()) {
            return 'exhausted';
        }

        return 'active';
    }

    /**
     * Generate bulk download tokens for multiple assets.
     */
    public function generateBulkTokens(array $assetIds, ?User $user = null, array $options = []): array
    {
        $tokens = [];
        $errors = [];

        foreach ($assetIds as $assetId) {
            try {
                $asset = DigitalAsset::findOrFail($assetId);
                $tokens[] = $this->generateToken($asset, $user, $options);
            } catch (\Exception $e) {
                $errors[] = [
                    'asset_id' => $assetId,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return [
            'tokens' => $tokens,
            'errors' => $errors,
            'success_count' => count($tokens),
            'error_count' => count($errors),
        ];
    }
}
