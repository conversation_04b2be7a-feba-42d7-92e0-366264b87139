<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\SearchAnalytics;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Pagination\LengthAwarePaginator;

class ProductSearchService
{
    /**
     * Search products with advanced filtering and sorting.
     */
    public function search(array $params, array $requestData = []): LengthAwarePaginator
    {
        $startTime = microtime(true);

        $query = Product::active()->with(['category:id,name']);

        // Apply search query
        if (!empty($params['q'])) {
            $this->applyTextSearch($query, $params['q']);
        }

        // Apply filters
        $this->applyFilters($query, $params);

        // Apply sorting
        $this->applySorting($query, $params);

        // Paginate results
        $perPage = min(max($params['per_page'] ?? 12, 1), 50);
        $results = $query->paginate($perPage);

        $searchDuration = microtime(true) - $startTime;

        // Track search analytics if query provided
        if (!empty($params['q'])) {
            $this->trackSearchAnalytics($params, $results, $searchDuration, $requestData);
        }

        return $results;
    }

    /**
     * Apply text search to query.
     */
    protected function applyTextSearch(Builder $query, string $searchTerm): void
    {
        $searchTerm = trim($searchTerm);
        
        if (empty($searchTerm)) {
            return;
        }

        // Split search term into words for better matching
        $words = explode(' ', $searchTerm);
        $words = array_filter($words, fn($word) => strlen($word) >= 2);

        $query->where(function ($q) use ($searchTerm, $words) {
            // Exact phrase match (highest priority)
            $q->where('name', 'like', "%{$searchTerm}%")
              ->orWhere('description', 'like', "%{$searchTerm}%")
              ->orWhere('short_description', 'like', "%{$searchTerm}%");

            // SKU exact match
            $q->orWhere('sku', '=', $searchTerm);

            // Individual word matches
            foreach ($words as $word) {
                $q->orWhere('name', 'like', "%{$word}%")
                  ->orWhere('description', 'like', "%{$word}%")
                  ->orWhere('tags', 'like', "%{$word}%");
            }

            // Category name search
            $q->orWhereHas('category', function ($categoryQuery) use ($searchTerm, $words) {
                $categoryQuery->where('name', 'like', "%{$searchTerm}%");
                foreach ($words as $word) {
                    $categoryQuery->orWhere('name', 'like', "%{$word}%");
                }
            });

            // Tags search (JSON contains)
            foreach ($words as $word) {
                $q->orWhereJsonContains('tags', $word);
            }
        });

        // Add relevance scoring
        $this->addRelevanceScoring($query, $searchTerm, $words);
    }

    /**
     * Add relevance scoring to search results.
     */
    protected function addRelevanceScoring(Builder $query, string $searchTerm, array $words): void
    {
        $selectRaw = "*, (
            CASE 
                WHEN name = '{$searchTerm}' THEN 100
                WHEN name LIKE '{$searchTerm}%' THEN 90
                WHEN name LIKE '%{$searchTerm}%' THEN 80
                WHEN sku = '{$searchTerm}' THEN 95
                WHEN short_description LIKE '%{$searchTerm}%' THEN 70
                WHEN description LIKE '%{$searchTerm}%' THEN 60
                ELSE 0
            END";

        // Add word-based scoring
        foreach ($words as $word) {
            $selectRaw .= " + CASE 
                WHEN name LIKE '%{$word}%' THEN 10
                WHEN description LIKE '%{$word}%' THEN 5
                ELSE 0
            END";
        }

        $selectRaw .= ") as relevance_score";

        $query->selectRaw($selectRaw);
    }

    /**
     * Apply filters to the query.
     */
    protected function applyFilters(Builder $query, array $params): void
    {
        // Category filter
        if (!empty($params['category_id'])) {
            if (is_array($params['category_id'])) {
                $query->whereIn('category_id', $params['category_id']);
            } else {
                $query->where('category_id', $params['category_id']);
            }
        }

        // Price range filter
        if (!empty($params['min_price'])) {
            $query->where(function ($q) use ($params) {
                $q->where('sale_price', '>=', $params['min_price'])
                  ->orWhere(function ($subQ) use ($params) {
                      $subQ->whereNull('sale_price')
                           ->where('price', '>=', $params['min_price']);
                  });
            });
        }

        if (!empty($params['max_price'])) {
            $query->where(function ($q) use ($params) {
                $q->where('sale_price', '<=', $params['max_price'])
                  ->orWhere(function ($subQ) use ($params) {
                      $subQ->whereNull('sale_price')
                           ->where('price', '<=', $params['max_price']);
                  });
            });
        }

        // Rating filter
        if (!empty($params['min_rating'])) {
            $query->where('average_rating', '>=', $params['min_rating']);
        }

        // Featured filter
        if (!empty($params['featured'])) {
            $query->where('featured', true);
        }

        // Digital/Physical filter
        if (isset($params['digital'])) {
            $query->where('digital', $params['digital']);
        }

        // Downloadable filter
        if (isset($params['downloadable'])) {
            $query->where('downloadable', $params['downloadable']);
        }

        // On sale filter
        if (!empty($params['on_sale'])) {
            $query->whereNotNull('sale_price')
                  ->whereColumn('sale_price', '<', 'price');
        }

        // Tags filter
        if (!empty($params['tags'])) {
            $tags = is_array($params['tags']) ? $params['tags'] : [$params['tags']];
            foreach ($tags as $tag) {
                $query->whereJsonContains('tags', $tag);
            }
        }

        // Availability filter
        if (!empty($params['available_only'])) {
            $query->where('status', 'active');
        }
    }

    /**
     * Apply sorting to the query.
     */
    protected function applySorting(Builder $query, array $params): void
    {
        $sortBy = $params['sort_by'] ?? 'relevance';
        $sortOrder = $params['sort_order'] ?? 'desc';

        switch ($sortBy) {
            case 'relevance':
                if (!empty($params['q'])) {
                    $query->orderBy('relevance_score', 'desc');
                }
                $query->orderBy('featured', 'desc')
                      ->orderBy('average_rating', 'desc');
                break;

            case 'price_low_high':
                $query->orderByRaw('COALESCE(sale_price, price) ASC');
                break;

            case 'price_high_low':
                $query->orderByRaw('COALESCE(sale_price, price) DESC');
                break;

            case 'rating':
                $query->orderBy('average_rating', 'desc')
                      ->orderBy('total_reviews', 'desc');
                break;

            case 'popularity':
                $query->orderBy('total_reviews', 'desc')
                      ->orderBy('average_rating', 'desc');
                break;

            case 'newest':
                $query->orderBy('created_at', 'desc');
                break;

            case 'name':
                $query->orderBy('name', $sortOrder);
                break;

            case 'featured':
                $query->orderBy('featured', 'desc')
                      ->orderBy('average_rating', 'desc');
                break;

            default:
                $query->orderBy('featured', 'desc')
                      ->orderBy('created_at', 'desc');
                break;
        }
    }

    /**
     * Get search suggestions based on partial query.
     */
    public function getSuggestions(string $query, int $limit = 10): array
    {
        $query = trim($query);
        
        if (strlen($query) < 2) {
            return [];
        }

        $cacheKey = "search_suggestions_" . md5($query) . "_{$limit}";
        
        return Cache::remember($cacheKey, 300, function () use ($query, $limit) {
            $suggestions = [];

            // Product name suggestions
            $productSuggestions = Product::active()
                ->where('name', 'like', "%{$query}%")
                ->orderBy('total_reviews', 'desc')
                ->limit($limit)
                ->pluck('name')
                ->toArray();

            foreach ($productSuggestions as $suggestion) {
                $suggestions[] = [
                    'type' => 'product',
                    'text' => $suggestion,
                    'category' => 'Products'
                ];
            }

            // Category suggestions
            $categorySuggestions = ProductCategory::where('name', 'like', "%{$query}%")
                ->orderBy('name')
                ->limit(5)
                ->pluck('name')
                ->toArray();

            foreach ($categorySuggestions as $suggestion) {
                $suggestions[] = [
                    'type' => 'category',
                    'text' => $suggestion,
                    'category' => 'Categories'
                ];
            }

            // Tag suggestions
            $tagSuggestions = Product::active()
                ->whereJsonContains('tags', $query)
                ->orWhere('tags', 'like', "%{$query}%")
                ->pluck('tags')
                ->flatten()
                ->filter(function ($tag) use ($query) {
                    return stripos($tag, $query) !== false;
                })
                ->unique()
                ->take(5)
                ->values()
                ->toArray();

            foreach ($tagSuggestions as $suggestion) {
                $suggestions[] = [
                    'type' => 'tag',
                    'text' => $suggestion,
                    'category' => 'Tags'
                ];
            }

            return array_slice($suggestions, 0, $limit);
        });
    }

    /**
     * Get search facets for filtering.
     */
    public function getFacets(array $params = []): array
    {
        $cacheKey = "search_facets_" . md5(serialize($params));
        
        return Cache::remember($cacheKey, 1800, function () use ($params) {
            $query = Product::active();

            // Apply current search to get relevant facets
            if (!empty($params['q'])) {
                $this->applyTextSearch($query, $params['q']);
            }

            $facets = [];

            // Categories facet
            $facets['categories'] = $this->getCategoryFacets($query);

            // Price ranges facet
            $facets['price_ranges'] = $this->getPriceFacets($query);

            // Rating facet
            $facets['ratings'] = $this->getRatingFacets($query);

            // Tags facet
            $facets['tags'] = $this->getTagFacets($query);

            // Attributes facet
            $facets['attributes'] = $this->getAttributeFacets($query);

            return $facets;
        });
    }

    /**
     * Get category facets.
     */
    protected function getCategoryFacets(Builder $query): array
    {
        return $query->clone()
            ->join('product_categories', 'products.category_id', '=', 'product_categories.id')
            ->groupBy('product_categories.id', 'product_categories.name')
            ->selectRaw('product_categories.id, product_categories.name, COUNT(*) as count')
            ->orderBy('count', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    /**
     * Get price range facets.
     */
    protected function getPriceFacets(Builder $query): array
    {
        $ranges = [
            ['min' => 0, 'max' => 25, 'label' => 'Under $25'],
            ['min' => 25, 'max' => 50, 'label' => '$25 - $50'],
            ['min' => 50, 'max' => 100, 'label' => '$50 - $100'],
            ['min' => 100, 'max' => 200, 'label' => '$100 - $200'],
            ['min' => 200, 'max' => null, 'label' => 'Over $200'],
        ];

        $facets = [];
        foreach ($ranges as $range) {
            $rangeQuery = $query->clone();
            
            $rangeQuery->where(function ($q) use ($range) {
                $q->where(function ($subQ) use ($range) {
                    if ($range['max']) {
                        $subQ->whereBetween('sale_price', [$range['min'], $range['max']])
                             ->orWhere(function ($priceQ) use ($range) {
                                 $priceQ->whereNull('sale_price')
                                        ->whereBetween('price', [$range['min'], $range['max']]);
                             });
                    } else {
                        $subQ->where('sale_price', '>=', $range['min'])
                             ->orWhere(function ($priceQ) use ($range) {
                                 $priceQ->whereNull('sale_price')
                                        ->where('price', '>=', $range['min']);
                             });
                    }
                });
            });

            $count = $rangeQuery->count();
            if ($count > 0) {
                $facets[] = [
                    'min' => $range['min'],
                    'max' => $range['max'],
                    'label' => $range['label'],
                    'count' => $count
                ];
            }
        }

        return $facets;
    }

    /**
     * Get rating facets.
     */
    protected function getRatingFacets(Builder $query): array
    {
        $facets = [];
        for ($rating = 5; $rating >= 1; $rating--) {
            $count = $query->clone()->where('average_rating', '>=', $rating)->count();
            if ($count > 0) {
                $facets[] = [
                    'rating' => $rating,
                    'label' => $rating . ' stars & up',
                    'count' => $count
                ];
            }
        }

        return $facets;
    }

    /**
     * Get tag facets.
     */
    protected function getTagFacets(Builder $query): array
    {
        // This is a simplified version - in production you might want to use a more sophisticated approach
        $products = $query->clone()->whereNotNull('tags')->pluck('tags');
        $tagCounts = [];

        foreach ($products as $tags) {
            if (is_array($tags)) {
                foreach ($tags as $tag) {
                    $tagCounts[$tag] = ($tagCounts[$tag] ?? 0) + 1;
                }
            }
        }

        arsort($tagCounts);
        
        return array_slice(array_map(function ($tag, $count) {
            return ['tag' => $tag, 'count' => $count];
        }, array_keys($tagCounts), $tagCounts), 0, 20, true);
    }

    /**
     * Get attribute facets.
     */
    protected function getAttributeFacets(Builder $query): array
    {
        return [
            'digital' => [
                ['value' => true, 'label' => 'Digital', 'count' => $query->clone()->where('digital', true)->count()],
                ['value' => false, 'label' => 'Physical', 'count' => $query->clone()->where('digital', false)->count()],
            ],
            'featured' => [
                ['value' => true, 'label' => 'Featured', 'count' => $query->clone()->where('featured', true)->count()],
            ],
            'on_sale' => [
                ['value' => true, 'label' => 'On Sale', 'count' => $query->clone()->whereNotNull('sale_price')->whereColumn('sale_price', '<', 'price')->count()],
            ]
        ];
    }

    /**
     * Track search analytics.
     */
    protected function trackSearchAnalytics(array $params, LengthAwarePaginator $results, float $duration, array $requestData): void
    {
        try {
            SearchAnalytics::recordSearch([
                'query' => $params['q'],
                'result_count' => $results->total(),
                'user_id' => Auth::id(),
                'session_id' => Session::getId(),
                'ip_address' => $requestData['ip'] ?? null,
                'user_agent' => $requestData['user_agent'] ?? null,
                'referer' => $requestData['referer'] ?? null,
                'filters_applied' => $this->getAppliedFilters($params),
                'sort_by' => $params['sort_by'] ?? null,
                'sort_order' => $params['sort_order'] ?? null,
                'page' => $results->currentPage(),
                'per_page' => $results->perPage(),
                'search_duration' => $duration,
            ]);
        } catch (\Exception $e) {
            // Log error but don't break search functionality
            \Log::error('Failed to track search analytics: ' . $e->getMessage());
        }
    }

    /**
     * Get applied filters for analytics.
     */
    protected function getAppliedFilters(array $params): array
    {
        $filters = [];

        $filterKeys = [
            'category_id', 'min_price', 'max_price', 'min_rating',
            'featured', 'digital', 'downloadable', 'on_sale', 'tags'
        ];

        foreach ($filterKeys as $key) {
            if (!empty($params[$key])) {
                $filters[$key] = $params[$key];
            }
        }

        return $filters;
    }

    /**
     * Track search query for analytics (legacy method for backward compatibility).
     */
    protected function trackSearchQuery(string $query, int $resultCount): void
    {
        // Keep cache-based tracking for quick access
        $cacheKey = "search_analytics_" . date('Y-m-d');
        $analytics = Cache::get($cacheKey, []);

        $analytics[$query] = [
            'count' => ($analytics[$query]['count'] ?? 0) + 1,
            'results' => $resultCount,
            'last_searched' => now()->toISOString()
        ];

        Cache::put($cacheKey, $analytics, 86400); // 24 hours
    }

    /**
     * Get popular search terms.
     */
    public function getPopularSearches(int $limit = 10): array
    {
        $cacheKey = "search_analytics_" . date('Y-m-d');
        $analytics = Cache::get($cacheKey, []);
        
        uasort($analytics, function ($a, $b) {
            return $b['count'] <=> $a['count'];
        });
        
        return array_slice(array_keys($analytics), 0, $limit);
    }
}
