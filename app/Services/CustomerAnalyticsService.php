<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductView;
use App\Models\DownloadLog;
use App\Models\ProductReview;
use App\Models\WishlistItem;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class CustomerAnalyticsService
{
    /**
     * Get comprehensive customer analytics overview.
     */
    public function getCustomerOverview(int $days = 30): array
    {
        $cacheKey = "customer_overview_{$days}";
        
        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);
            $endDate = now();
            
            // Current period stats
            $currentStats = $this->getPeriodCustomerStats($startDate, $endDate);
            
            // Previous period for comparison
            $previousStartDate = $startDate->copy()->subDays($days);
            $previousEndDate = $startDate->copy();
            $previousStats = $this->getPeriodCustomerStats($previousStartDate, $previousEndDate);
            
            return [
                'current' => $currentStats,
                'previous' => $previousStats,
                'growth' => $this->calculateGrowth($currentStats, $previousStats),
                'period_days' => $days,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ];
        });
    }

    /**
     * Get customer statistics for a specific period.
     */
    protected function getPeriodCustomerStats(Carbon $startDate, Carbon $endDate): array
    {
        // New customers registered in period
        $newCustomers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        
        // Active customers (made purchases in period)
        $activeCustomers = User::whereHas('orders', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->where('status', 'completed');
        })->count();
        
        // Returning customers (had orders before this period and also in this period)
        $returningCustomers = User::whereHas('orders', function ($query) use ($startDate) {
            $query->where('created_at', '<', $startDate)
                  ->where('status', 'completed');
        })->whereHas('orders', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->where('status', 'completed');
        })->count();
        
        // Customer lifetime value
        $avgLifetimeValue = User::whereHas('orders', function ($query) {
            $query->where('status', 'completed');
        })->withSum(['orders' => function ($query) {
            $query->where('status', 'completed');
        }], 'total_amount')
        ->get()
        ->avg('orders_sum_total_amount') ?? 0;
        
        // Average order value for period
        $avgOrderValue = Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')
            ->avg('total_amount') ?? 0;
        
        // Customer retention rate
        $totalCustomersAtStart = User::where('created_at', '<', $startDate)->count();
        $retentionRate = $totalCustomersAtStart > 0 ? ($returningCustomers / $totalCustomersAtStart) * 100 : 0;
        
        // Churn rate (customers who haven't made purchases in this period but had before)
        $churnedCustomers = User::whereHas('orders', function ($query) use ($startDate) {
            $query->where('created_at', '<', $startDate)
                  ->where('status', 'completed');
        })->whereDoesntHave('orders', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();
        
        $churnRate = $totalCustomersAtStart > 0 ? ($churnedCustomers / $totalCustomersAtStart) * 100 : 0;
        
        return [
            'new_customers' => $newCustomers,
            'active_customers' => $activeCustomers,
            'returning_customers' => $returningCustomers,
            'avg_lifetime_value' => round($avgLifetimeValue, 2),
            'avg_order_value' => round($avgOrderValue, 2),
            'retention_rate' => round($retentionRate, 2),
            'churn_rate' => round($churnRate, 2),
            'total_customers' => User::count(),
        ];
    }

    /**
     * Calculate growth percentages between periods.
     */
    protected function calculateGrowth(array $current, array $previous): array
    {
        $growth = [];
        
        foreach ($current as $key => $value) {
            if (isset($previous[$key]) && $previous[$key] > 0) {
                $growth[$key] = round((($value - $previous[$key]) / $previous[$key]) * 100, 2);
            } else {
                $growth[$key] = $value > 0 ? 100 : 0;
            }
        }
        
        return $growth;
    }

    /**
     * Get customer segmentation analysis.
     */
    public function getCustomerSegmentation(): array
    {
        $cacheKey = "customer_segmentation";
        
        return Cache::remember($cacheKey, 3600, function () {
            // RFM Analysis (Recency, Frequency, Monetary)
            $customers = User::whereHas('orders', function ($query) {
                $query->where('status', 'completed');
            })
            ->withCount(['orders' => function ($query) {
                $query->where('status', 'completed');
            }])
            ->withSum(['orders' => function ($query) {
                $query->where('status', 'completed');
            }], 'total_amount')
            ->with(['orders' => function ($query) {
                $query->where('status', 'completed')
                      ->latest()
                      ->limit(1);
            }])
            ->get();

            $segments = [
                'champions' => 0,      // High value, frequent, recent
                'loyal_customers' => 0, // High frequency, good value
                'potential_loyalists' => 0, // Recent, good frequency
                'new_customers' => 0,   // Recent, low frequency
                'promising' => 0,       // Recent, low frequency, good value
                'need_attention' => 0,  // Good value, not recent
                'about_to_sleep' => 0,  // Low recency, frequency
                'at_risk' => 0,         // Good value, low recency
                'cannot_lose' => 0,     // High value, very low recency
                'hibernating' => 0,     // Low value, very low recency
            ];

            foreach ($customers as $customer) {
                $recency = $customer->orders->first() ? 
                    now()->diffInDays($customer->orders->first()->created_at) : 999;
                $frequency = $customer->orders_count;
                $monetary = $customer->orders_sum_total_amount ?? 0;

                // Simplified segmentation logic
                if ($recency <= 30 && $frequency >= 5 && $monetary >= 500) {
                    $segments['champions']++;
                } elseif ($frequency >= 3 && $monetary >= 200) {
                    $segments['loyal_customers']++;
                } elseif ($recency <= 30 && $frequency >= 2) {
                    $segments['potential_loyalists']++;
                } elseif ($recency <= 30 && $frequency == 1) {
                    $segments['new_customers']++;
                } elseif ($recency <= 60 && $monetary >= 100) {
                    $segments['promising']++;
                } elseif ($recency <= 90 && $monetary >= 200) {
                    $segments['need_attention']++;
                } elseif ($recency <= 120 && $frequency <= 2) {
                    $segments['about_to_sleep']++;
                } elseif ($recency <= 180 && $monetary >= 200) {
                    $segments['at_risk']++;
                } elseif ($recency > 180 && $monetary >= 500) {
                    $segments['cannot_lose']++;
                } else {
                    $segments['hibernating']++;
                }
            }

            return $segments;
        });
    }

    /**
     * Get customer cohort analysis.
     */
    public function getCohortAnalysis(int $months = 12): array
    {
        $cacheKey = "cohort_analysis_{$months}";
        
        return Cache::remember($cacheKey, 3600, function () use ($months) {
            $cohorts = [];
            
            for ($i = $months - 1; $i >= 0; $i--) {
                $cohortMonth = now()->subMonths($i)->format('Y-m');
                $cohortStart = now()->subMonths($i)->startOfMonth();
                $cohortEnd = now()->subMonths($i)->endOfMonth();
                
                // Get customers who made their first purchase in this month
                $cohortCustomers = User::whereHas('orders', function ($query) use ($cohortStart, $cohortEnd) {
                    $query->whereBetween('created_at', [$cohortStart, $cohortEnd])
                          ->where('status', 'completed');
                })->whereDoesntHave('orders', function ($query) use ($cohortStart) {
                    $query->where('created_at', '<', $cohortStart)
                          ->where('status', 'completed');
                })->pluck('id');

                $cohortSize = $cohortCustomers->count();
                
                if ($cohortSize > 0) {
                    $retentionData = [];
                    
                    // Calculate retention for each subsequent month
                    for ($j = 0; $j <= $i; $j++) {
                        $periodStart = now()->subMonths($i - $j)->startOfMonth();
                        $periodEnd = now()->subMonths($i - $j)->endOfMonth();
                        
                        $activeCustomers = User::whereIn('id', $cohortCustomers)
                            ->whereHas('orders', function ($query) use ($periodStart, $periodEnd) {
                                $query->whereBetween('created_at', [$periodStart, $periodEnd])
                                      ->where('status', 'completed');
                            })->count();
                        
                        $retentionData[] = [
                            'period' => $j,
                            'customers' => $activeCustomers,
                            'retention_rate' => round(($activeCustomers / $cohortSize) * 100, 2),
                        ];
                    }
                    
                    $cohorts[] = [
                        'cohort_month' => $cohortMonth,
                        'cohort_size' => $cohortSize,
                        'retention_data' => $retentionData,
                    ];
                }
            }
            
            return $cohorts;
        });
    }

    /**
     * Get top customers by various metrics.
     */
    public function getTopCustomers(int $days = 30, int $limit = 10, string $metric = 'revenue'): array
    {
        $cacheKey = "top_customers_{$days}_{$limit}_{$metric}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days, $limit, $metric) {
            $startDate = now()->subDays($days);
            
            switch ($metric) {
                case 'orders':
                    return $this->getTopCustomersByOrders($startDate, $limit);
                case 'downloads':
                    return $this->getTopCustomersByDownloads($startDate, $limit);
                case 'engagement':
                    return $this->getTopCustomersByEngagement($startDate, $limit);
                default: // revenue
                    return $this->getTopCustomersByRevenue($startDate, $limit);
            }
        });
    }

    /**
     * Get top customers by revenue.
     */
    protected function getTopCustomersByRevenue(Carbon $startDate, int $limit): array
    {
        return User::whereHas('orders', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->withSum(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            }], 'total_amount')
            ->withCount(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            }])
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($customer) {
                return [
                    'customer' => $customer,
                    'metric_value' => round($customer->orders_sum_total_amount, 2),
                    'metric_label' => 'Revenue',
                    'secondary_metric' => $customer->orders_count,
                    'secondary_label' => 'Orders',
                ];
            })
            ->toArray();
    }

    /**
     * Get top customers by order count.
     */
    protected function getTopCustomersByOrders(Carbon $startDate, int $limit): array
    {
        return User::whereHas('orders', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })
            ->withCount(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            }])
            ->withSum(['orders' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            }], 'total_amount')
            ->orderBy('orders_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($customer) {
                return [
                    'customer' => $customer,
                    'metric_value' => $customer->orders_count,
                    'metric_label' => 'Orders',
                    'secondary_metric' => round($customer->orders_sum_total_amount, 2),
                    'secondary_label' => 'Revenue',
                ];
            })
            ->toArray();
    }

    /**
     * Get customer behavior patterns.
     */
    public function getCustomerBehaviorPatterns(int $days = 30): array
    {
        $cacheKey = "customer_behavior_patterns_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            // Purchase patterns by hour
            $hourlyPurchases = Order::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->groupBy('hour')
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as orders')
                ->orderBy('hour')
                ->get()
                ->toArray();

            // Purchase patterns by day of week
            $weeklyPurchases = Order::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->groupBy('day_of_week')
                ->selectRaw('DAYOFWEEK(created_at) as day_of_week, COUNT(*) as orders')
                ->orderBy('day_of_week')
                ->get()
                ->map(function ($item) {
                    $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                    return [
                        'day_of_week' => $item->day_of_week,
                        'day_name' => $days[$item->day_of_week - 1] ?? 'Unknown',
                        'orders' => $item->orders,
                    ];
                })
                ->toArray();

            // Average time between orders
            $avgTimeBetweenOrders = DB::select("
                SELECT AVG(days_between) as avg_days
                FROM (
                    SELECT user_id, 
                           DATEDIFF(created_at, LAG(created_at) OVER (PARTITION BY user_id ORDER BY created_at)) as days_between
                    FROM orders 
                    WHERE status = 'completed' 
                    AND created_at >= ?
                ) as order_gaps 
                WHERE days_between IS NOT NULL
            ", [$startDate]);

            return [
                'hourly_purchases' => $hourlyPurchases,
                'weekly_purchases' => $weeklyPurchases,
                'avg_days_between_orders' => round($avgTimeBetweenOrders[0]->avg_days ?? 0, 1),
            ];
        });
    }

    /**
     * Get customer summary for dashboard.
     */
    public function getCustomerSummary(int $days = 7): array
    {
        $cacheKey = "customer_summary_{$days}";
        
        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);
            
            $newCustomers = User::where('created_at', '>=', $startDate)->count();
            $activeCustomers = User::whereHas('orders', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate)
                      ->where('status', 'completed');
            })->count();
            
            $avgLifetimeValue = User::whereHas('orders', function ($query) {
                $query->where('status', 'completed');
            })->withSum(['orders' => function ($query) {
                $query->where('status', 'completed');
            }], 'total_amount')
            ->get()
            ->avg('orders_sum_total_amount') ?? 0;
            
            $topCustomer = User::whereHas('orders', function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                          ->where('status', 'completed');
                })
                ->withSum(['orders' => function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                          ->where('status', 'completed');
                }], 'total_amount')
                ->orderBy('orders_sum_total_amount', 'desc')
                ->first();
            
            return [
                'new_customers' => $newCustomers,
                'active_customers' => $activeCustomers,
                'avg_lifetime_value' => round($avgLifetimeValue, 2),
                'top_customer' => $topCustomer?->name,
                'top_customer_revenue' => round($topCustomer?->orders_sum_total_amount ?? 0, 2),
            ];
        });
    }
}
