<?php

namespace App\Services;

use App\Models\DigitalAsset;
use App\Models\Product;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class DigitalAssetService
{
    protected $disk;
    protected $maxFileSize;
    protected $allowedExtensions;

    public function __construct()
    {
        $this->disk = Storage::disk('digital_assets');
        $this->maxFileSize = config('app.digital_asset_max_file_size', 104857600); // 100MB default
        $this->allowedExtensions = explode(',', config('app.digital_asset_allowed_extensions', 'pdf,zip,rar'));
    }

    /**
     * Upload a file to S3 and create a digital asset record.
     */
    public function uploadFile(UploadedFile $file, Product $product, array $metadata = []): DigitalAsset
    {
        // Validate file
        $this->validateFile($file);

        // Generate unique filename
        $filename = $this->generateUniqueFilename($file);
        $path = "products/{$product->id}/{$filename}";

        try {
            // Upload to S3
            $uploaded = $this->disk->putFileAs(
                "products/{$product->id}",
                $file,
                $filename,
                'private'
            );

            if (!$uploaded) {
                throw new Exception('Failed to upload file to S3');
            }

            // Create digital asset record
            $digitalAsset = DigitalAsset::create([
                'product_id' => $product->id,
                'original_name' => $file->getClientOriginalName(),
                'file_name' => $filename,
                'file_path' => $path,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => hash_file('sha256', $file->getRealPath()),
                'storage_disk' => 'digital_assets',
                'is_active' => true,
                'metadata' => array_merge([
                    'uploaded_at' => now()->toISOString(),
                    'uploaded_by' => auth()->id(),
                    'original_extension' => $file->getClientOriginalExtension(),
                ], $metadata),
            ]);

            return $digitalAsset;

        } catch (Exception $e) {
            // Clean up if database creation fails
            if (isset($uploaded) && $uploaded) {
                $this->disk->delete($path);
            }
            throw $e;
        }
    }

    /**
     * Delete a digital asset file and record.
     */
    public function deleteAsset(DigitalAsset $asset): bool
    {
        try {
            // Delete from S3
            if ($this->disk->exists($asset->file_path)) {
                $this->disk->delete($asset->file_path);
            }

            // Delete database record
            $asset->delete();

            return true;
        } catch (Exception $e) {
            throw new Exception("Failed to delete digital asset: " . $e->getMessage());
        }
    }

    /**
     * Generate a secure download URL for a digital asset.
     */
    public function generateDownloadUrl(DigitalAsset $asset, int $expiresInHours = 24): string
    {
        try {
            $expiresAt = now()->addHours($expiresInHours);
            
            return $this->disk->temporaryUrl(
                $asset->file_path,
                $expiresAt
            );
        } catch (Exception $e) {
            throw new Exception("Failed to generate download URL: " . $e->getMessage());
        }
    }

    /**
     * Get file information without downloading.
     */
    public function getFileInfo(DigitalAsset $asset): array
    {
        try {
            return [
                'exists' => $this->disk->exists($asset->file_path),
                'size' => $this->disk->size($asset->file_path),
                'last_modified' => $this->disk->lastModified($asset->file_path),
                'url' => $this->disk->url($asset->file_path),
            ];
        } catch (Exception $e) {
            return [
                'exists' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate uploaded file.
     */
    protected function validateFile(UploadedFile $file): void
    {
        // Check file size
        if ($file->getSize() > $this->maxFileSize) {
            throw new Exception("File size exceeds maximum allowed size of " . $this->formatBytes($this->maxFileSize));
        }

        // Check file extension
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $this->allowedExtensions)) {
            throw new Exception("File type '{$extension}' is not allowed. Allowed types: " . implode(', ', $this->allowedExtensions));
        }

        // Check if file is valid
        if (!$file->isValid()) {
            throw new Exception("Invalid file upload");
        }
    }

    /**
     * Generate a unique filename.
     */
    protected function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $basename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $basename = Str::slug($basename);
        
        return $basename . '_' . Str::random(8) . '_' . time() . '.' . $extension;
    }

    /**
     * Format bytes to human readable format.
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Test S3 connection.
     */
    public function testConnection(): array
    {
        try {
            // Try to list files in the bucket
            $files = $this->disk->files('test');
            
            return [
                'success' => true,
                'message' => 'S3 connection successful',
                'bucket' => config('filesystems.disks.digital_assets.bucket'),
                'region' => config('filesystems.disks.digital_assets.region'),
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'S3 connection failed: ' . $e->getMessage(),
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get storage statistics.
     */
    public function getStorageStats(): array
    {
        try {
            $totalAssets = DigitalAsset::count();
            $totalSize = DigitalAsset::sum('file_size');
            $activeAssets = DigitalAsset::where('is_active', true)->count();
            
            return [
                'total_assets' => $totalAssets,
                'active_assets' => $activeAssets,
                'total_size' => $totalSize,
                'total_size_formatted' => $this->formatBytes($totalSize),
                'average_file_size' => $totalAssets > 0 ? $totalSize / $totalAssets : 0,
            ];
        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Bulk upload files.
     */
    public function bulkUpload(array $files, Product $product, array $metadata = []): array
    {
        $results = [];
        $successful = 0;
        $failed = 0;

        foreach ($files as $file) {
            try {
                $asset = $this->uploadFile($file, $product, $metadata);
                $results[] = [
                    'success' => true,
                    'file' => $file->getClientOriginalName(),
                    'asset_id' => $asset->id,
                ];
                $successful++;
            } catch (Exception $e) {
                $results[] = [
                    'success' => false,
                    'file' => $file->getClientOriginalName(),
                    'error' => $e->getMessage(),
                ];
                $failed++;
            }
        }

        return [
            'results' => $results,
            'summary' => [
                'total' => count($files),
                'successful' => $successful,
                'failed' => $failed,
            ],
        ];
    }
}
