<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class SalesAnalyticsService
{
    /**
     * Get comprehensive sales overview.
     */
    public function getSalesOverview(int $days = 30): array
    {
        $cacheKey = "sales_overview_{$days}";
        
        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);
            $endDate = now();
            
            // Current period stats
            $currentStats = $this->getPeriodStats($startDate, $endDate);
            
            // Previous period for comparison
            $previousStartDate = $startDate->copy()->subDays($days);
            $previousEndDate = $startDate->copy();
            $previousStats = $this->getPeriodStats($previousStartDate, $previousEndDate);
            
            return [
                'current' => $currentStats,
                'previous' => $previousStats,
                'growth' => $this->calculateGrowth($currentStats, $previousStats),
                'period_days' => $days,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ];
        });
    }

    /**
     * Get sales statistics for a specific period.
     */
    protected function getPeriodStats(Carbon $startDate, Carbon $endDate): array
    {
        $orders = Order::whereBetween('created_at', [$startDate, $endDate]);
        $completedOrders = $orders->clone()->where('status', 'completed');
        
        $totalRevenue = $completedOrders->clone()->sum('total_amount');
        $totalOrders = $orders->clone()->count();
        $completedOrdersCount = $completedOrders->clone()->count();
        $averageOrderValue = $completedOrdersCount > 0 ? $totalRevenue / $completedOrdersCount : 0;
        
        // Get refunds
        $refunds = Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'refunded')
            ->sum('total_amount');
        
        // Net revenue (revenue - refunds)
        $netRevenue = $totalRevenue - $refunds;
        
        // Get unique customers
        $uniqueCustomers = $orders->clone()->distinct('user_id')->count('user_id');
        
        // Get new customers (first order in this period)
        $newCustomers = User::whereHas('orders', function ($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->whereDoesntHave('orders', function ($query) use ($startDate) {
            $query->where('created_at', '<', $startDate);
        })->count();
        
        // Conversion rate (completed orders / total orders)
        $conversionRate = $totalOrders > 0 ? ($completedOrdersCount / $totalOrders) * 100 : 0;
        
        return [
            'total_revenue' => round($totalRevenue, 2),
            'net_revenue' => round($netRevenue, 2),
            'total_orders' => $totalOrders,
            'completed_orders' => $completedOrdersCount,
            'average_order_value' => round($averageOrderValue, 2),
            'refunds' => round($refunds, 2),
            'unique_customers' => $uniqueCustomers,
            'new_customers' => $newCustomers,
            'conversion_rate' => round($conversionRate, 2),
        ];
    }

    /**
     * Calculate growth percentages between periods.
     */
    protected function calculateGrowth(array $current, array $previous): array
    {
        $growth = [];
        
        foreach ($current as $key => $value) {
            if (isset($previous[$key]) && $previous[$key] > 0) {
                $growth[$key] = round((($value - $previous[$key]) / $previous[$key]) * 100, 2);
            } else {
                $growth[$key] = $value > 0 ? 100 : 0;
            }
        }
        
        return $growth;
    }

    /**
     * Get daily sales trends.
     */
    public function getDailySalesTrends(int $days = 30): array
    {
        $cacheKey = "daily_sales_trends_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return Order::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->groupBy('date')
                ->selectRaw('DATE(created_at) as date, COUNT(*) as orders, SUM(total_amount) as revenue')
                ->orderBy('date')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => $item->date,
                        'orders' => $item->orders,
                        'revenue' => round($item->revenue, 2),
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get hourly sales patterns.
     */
    public function getHourlySalesPattern(int $days = 30): array
    {
        $cacheKey = "hourly_sales_pattern_{$days}";
        
        return Cache::remember($cacheKey, 3600, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return Order::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->groupBy('hour')
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as orders, SUM(total_amount) as revenue')
                ->orderBy('hour')
                ->get()
                ->map(function ($item) {
                    return [
                        'hour' => $item->hour,
                        'orders' => $item->orders,
                        'revenue' => round($item->revenue, 2),
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get top selling products.
     */
    public function getTopSellingProducts(int $days = 30, int $limit = 10): array
    {
        $cacheKey = "top_selling_products_{$days}_{$limit}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            $startDate = now()->subDays($days);
            
            return OrderItem::whereHas('order', function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                          ->where('status', 'completed');
                })
                ->with('product:id,name,slug,price,sale_price,image')
                ->groupBy('product_id')
                ->selectRaw('product_id, SUM(quantity) as total_sold, SUM(unit_price * quantity) as total_revenue, COUNT(DISTINCT order_id) as order_count')
                ->orderBy('total_sold', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($item) {
                    return [
                        'product' => $item->product,
                        'total_sold' => $item->total_sold,
                        'total_revenue' => round($item->total_revenue, 2),
                        'order_count' => $item->order_count,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get revenue by product category.
     */
    public function getRevenueByCategory(int $days = 30): array
    {
        $cacheKey = "revenue_by_category_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return OrderItem::whereHas('order', function ($query) use ($startDate) {
                    $query->where('created_at', '>=', $startDate)
                          ->where('status', 'completed');
                })
                ->join('products', 'order_items.product_id', '=', 'products.id')
                ->join('product_categories', 'products.category_id', '=', 'product_categories.id')
                ->groupBy('product_categories.id', 'product_categories.name')
                ->selectRaw('product_categories.id, product_categories.name, SUM(order_items.unit_price * order_items.quantity) as revenue, SUM(order_items.quantity) as quantity_sold')
                ->orderBy('revenue', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'category_id' => $item->id,
                        'category_name' => $item->name,
                        'revenue' => round($item->revenue, 2),
                        'quantity_sold' => $item->quantity_sold,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get customer analytics.
     */
    public function getCustomerAnalytics(int $days = 30): array
    {
        $cacheKey = "customer_analytics_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            // Total customers with orders in period
            $totalCustomers = Order::where('created_at', '>=', $startDate)
                ->distinct('user_id')
                ->count('user_id');
            
            // New customers (first order in period)
            $newCustomers = User::whereHas('orders', function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            })->whereDoesntHave('orders', function ($query) use ($startDate) {
                $query->where('created_at', '<', $startDate);
            })->count();
            
            // Returning customers
            $returningCustomers = $totalCustomers - $newCustomers;
            
            // Customer lifetime value
            $customerLTV = User::whereHas('orders', function ($query) {
                $query->where('status', 'completed');
            })->withSum(['orders' => function ($query) {
                $query->where('status', 'completed');
            }], 'total_amount')
            ->get()
            ->avg('orders_sum_total_amount') ?? 0;
            
            // Average orders per customer
            $avgOrdersPerCustomer = $totalCustomers > 0 ? 
                Order::where('created_at', '>=', $startDate)->count() / $totalCustomers : 0;
            
            return [
                'total_customers' => $totalCustomers,
                'new_customers' => $newCustomers,
                'returning_customers' => $returningCustomers,
                'customer_ltv' => round($customerLTV, 2),
                'avg_orders_per_customer' => round($avgOrdersPerCustomer, 2),
                'new_customer_rate' => $totalCustomers > 0 ? round(($newCustomers / $totalCustomers) * 100, 2) : 0,
            ];
        });
    }

    /**
     * Get order status distribution.
     */
    public function getOrderStatusDistribution(int $days = 30): array
    {
        $cacheKey = "order_status_distribution_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return Order::where('created_at', '>=', $startDate)
                ->groupBy('status')
                ->selectRaw('status, COUNT(*) as count, SUM(total_amount) as revenue')
                ->get()
                ->map(function ($item) {
                    return [
                        'status' => $item->status,
                        'count' => $item->count,
                        'revenue' => round($item->revenue, 2),
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get payment method analytics.
     */
    public function getPaymentMethodAnalytics(int $days = 30): array
    {
        $cacheKey = "payment_method_analytics_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return Order::where('created_at', '>=', $startDate)
                ->where('status', 'completed')
                ->groupBy('payment_method')
                ->selectRaw('payment_method, COUNT(*) as count, SUM(total_amount) as revenue')
                ->orderBy('revenue', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'payment_method' => $item->payment_method ?? 'Unknown',
                        'count' => $item->count,
                        'revenue' => round($item->revenue, 2),
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get monthly comparison data.
     */
    public function getMonthlyComparison(int $months = 12): array
    {
        $cacheKey = "monthly_comparison_{$months}";
        
        return Cache::remember($cacheKey, 3600, function () use ($months) {
            $data = [];
            
            for ($i = $months - 1; $i >= 0; $i--) {
                $startDate = now()->subMonths($i)->startOfMonth();
                $endDate = now()->subMonths($i)->endOfMonth();
                
                $stats = Order::whereBetween('created_at', [$startDate, $endDate])
                    ->where('status', 'completed')
                    ->selectRaw('
                        COUNT(*) as orders,
                        SUM(total_amount) as revenue,
                        AVG(total_amount) as avg_order_value,
                        COUNT(DISTINCT user_id) as unique_customers
                    ')
                    ->first();
                
                $data[] = [
                    'month' => $startDate->format('Y-m'),
                    'month_name' => $startDate->format('M Y'),
                    'orders' => $stats->orders ?? 0,
                    'revenue' => round($stats->revenue ?? 0, 2),
                    'avg_order_value' => round($stats->avg_order_value ?? 0, 2),
                    'unique_customers' => $stats->unique_customers ?? 0,
                ];
            }
            
            return $data;
        });
    }

    /**
     * Get sales forecasting data.
     */
    public function getSalesForecast(int $days = 30): array
    {
        // Simple linear regression forecast based on recent trends
        $trends = $this->getDailySalesTrends($days);
        
        if (count($trends) < 7) {
            return []; // Need at least a week of data
        }
        
        // Calculate trend line
        $revenues = array_column($trends, 'revenue');
        $n = count($revenues);
        $x = range(1, $n);
        
        $sumX = array_sum($x);
        $sumY = array_sum($revenues);
        $sumXY = 0;
        $sumX2 = 0;
        
        for ($i = 0; $i < $n; $i++) {
            $sumXY += $x[$i] * $revenues[$i];
            $sumX2 += $x[$i] * $x[$i];
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;
        
        // Forecast next 7 days
        $forecast = [];
        for ($i = 1; $i <= 7; $i++) {
            $forecastValue = $slope * ($n + $i) + $intercept;
            $forecast[] = [
                'date' => now()->addDays($i)->toDateString(),
                'predicted_revenue' => max(0, round($forecastValue, 2)),
            ];
        }
        
        return $forecast;
    }
}
