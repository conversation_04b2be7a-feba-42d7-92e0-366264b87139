<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardService
{
    /**
     * Get comprehensive dashboard data for a user.
     */
    public function getDashboardData(User $user): array
    {
        $cacheKey = "dashboard_data_{$user->id}";
        
        return Cache::remember($cacheKey, 300, function () use ($user) {
            return [
                'stats' => $this->getStats($user),
                'activities' => $this->getRecentActivities($user),
                'transactions' => $this->getTransactionSummaries($user),
            ];
        });
    }

    /**
     * Get dashboard statistics.
     */
    public function getStats(User $user): array
    {
        $isAdmin = $user->hasAnyRole(['admin', 'super_admin']);
        
        if ($isAdmin) {
            return $this->getAdminStats();
        }
        
        return $this->getUserStats($user);
    }

    /**
     * Get admin-specific statistics.
     */
    protected function getAdminStats(): array
    {
        return [
            'products' => [
                'total' => $this->getProductCount(),
                'categories' => $this->getCategoryCount(),
                'subcategories' => $this->getSubcategoryCount(),
            ],
            'users' => [
                'total' => $this->getUserCount(),
                'listingAgents' => $this->getUserCountByRole('listing_agent'),
                'franchises' => $this->getUserCountByRole('franchise'),
            ],
            'tickets' => [
                'total' => $this->getTicketCount(),
                'pending' => $this->getTicketCount('pending'),
                'resolved' => $this->getTicketCount('resolved'),
            ],
            'advertisements' => [
                'total' => $this->getAdvertisementCount(),
                'published' => $this->getAdvertisementCount('published'),
                'pending' => $this->getAdvertisementCount('pending'),
            ],
            'packages' => [
                'total' => $this->getPackageCount(),
                'active' => $this->getPackageCount('active'),
                'inactive' => $this->getPackageCount('inactive'),
            ],
            'courses' => [
                'total' => $this->getCourseCount(),
                'active' => $this->getCourseCount('active'),
                'inactive' => $this->getCourseCount('inactive'),
            ],
            'companies' => [
                'total' => $this->getCompanyCount(),
                'pending' => $this->getCompanyCount('pending'),
            ],
            'invoices' => [
                'total' => $this->getInvoiceCount(),
                'paid' => $this->getInvoiceCount('paid'),
                'unpaid' => $this->getInvoiceCount('unpaid'),
            ],
            'participants' => [
                'total' => $this->getParticipantCount(),
                'active' => $this->getParticipantCount('active'),
                'pending' => $this->getParticipantCount('pending'),
            ],
            'onlineUsers' => [
                'total' => $this->getOnlineUserCount(),
                'system' => $this->getOnlineUserCount('system'),
                'customers' => $this->getOnlineUserCount('customers'),
            ],
        ];
    }

    /**
     * Get user-specific statistics.
     */
    protected function getUserStats(User $user): array
    {
        return [
            'myProducts' => [
                'total' => $this->getUserProductCount($user),
                'active' => $this->getUserProductCount($user, 'active'),
                'inactive' => $this->getUserProductCount($user, 'inactive'),
            ],
            'myOrders' => [
                'total' => $this->getUserOrderCount($user),
                'pending' => $this->getUserOrderCount($user, 'pending'),
                'completed' => $this->getUserOrderCount($user, 'completed'),
            ],
            'myDownloads' => [
                'total' => $this->getUserDownloadCount($user),
                'recent' => $this->getUserDownloadCount($user, 'recent'),
            ],
            'myInvoices' => [
                'total' => $this->getUserInvoiceCount($user),
                'paid' => $this->getUserInvoiceCount($user, 'paid'),
                'unpaid' => $this->getUserInvoiceCount($user, 'unpaid'),
            ],
        ];
    }

    /**
     * Get recent activities for a user.
     */
    public function getRecentActivities(User $user): array
    {
        $isAdmin = $user->hasAnyRole(['admin', 'super_admin']);
        
        if ($isAdmin) {
            return $this->getAdminActivities();
        }
        
        return $this->getUserActivities($user);
    }

    /**
     * Get admin activities.
     */
    protected function getAdminActivities(): array
    {
        return [
            'users' => $this->getRecentUserActivities(),
            'orders' => $this->getRecentOrderActivities(),
            'invoices' => $this->getRecentInvoiceActivities(),
            'transactions' => $this->getRecentTransactionActivities(),
        ];
    }

    /**
     * Get user activities.
     */
    protected function getUserActivities(User $user): array
    {
        return [
            'orders' => $this->getUserRecentOrders($user),
            'downloads' => $this->getUserRecentDownloads($user),
            'invoices' => $this->getUserRecentInvoices($user),
            'transactions' => $this->getUserRecentTransactions($user),
        ];
    }

    /**
     * Get transaction summaries.
     */
    public function getTransactionSummaries(User $user): array
    {
        return [
            'allTime' => $this->getTransactionSummary($user, 'all'),
            'past24Hours' => $this->getTransactionSummary($user, '24h'),
            'past7Days' => $this->getTransactionSummary($user, '7d'),
            'past30Days' => $this->getTransactionSummary($user, '30d'),
        ];
    }

    // Helper methods for counting various entities
    protected function getProductCount(): int
    {
        return 5; // Mock data - replace with actual query
    }

    protected function getCategoryCount(): int
    {
        return 2; // Mock data
    }

    protected function getSubcategoryCount(): int
    {
        return 4; // Mock data
    }

    protected function getUserCount(): int
    {
        return User::count();
    }

    protected function getUserCountByRole(string $role): int
    {
        return User::whereHas('roles', function ($query) use ($role) {
            $query->where('name', $role);
        })->count();
    }

    protected function getTicketCount(string $status = null): int
    {
        return 0; // Mock data - implement when tickets table exists
    }

    protected function getAdvertisementCount(string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getPackageCount(string $status = null): int
    {
        return 6; // Mock data
    }

    protected function getCourseCount(string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getCompanyCount(string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getInvoiceCount(string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getParticipantCount(string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getOnlineUserCount(string $type = null): int
    {
        if ($type === 'system') return 1;
        if ($type === 'customers') return 0;
        return 1; // Total online users
    }

    protected function getUserProductCount(User $user, string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getUserOrderCount(User $user, string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getUserDownloadCount(User $user, string $type = null): int
    {
        return 0; // Mock data
    }

    protected function getUserInvoiceCount(User $user, string $status = null): int
    {
        return 0; // Mock data
    }

    protected function getRecentUserActivities(): array
    {
        return [
            [
                'id' => '1',
                'type' => 'user',
                'title' => 'New user registration',
                'description' => '<EMAIL> joined the platform',
                'status' => 'complete',
                'time' => '2 minutes ago',
            ],
            [
                'id' => '2',
                'type' => 'user',
                'title' => 'Profile updated',
                'description' => '<EMAIL> updated their profile',
                'status' => 'complete',
                'time' => '15 minutes ago',
            ],
        ];
    }

    protected function getRecentOrderActivities(): array
    {
        return [
            [
                'id' => '1',
                'type' => 'order',
                'title' => 'New order received',
                'description' => 'Digital product purchase',
                'amount' => '$29.99',
                'status' => 'pending',
                'time' => '5 minutes ago',
            ],
        ];
    }

    protected function getRecentInvoiceActivities(): array
    {
        return [
            [
                'id' => '1',
                'type' => 'invoice',
                'title' => 'Invoice generated',
                'description' => 'Monthly subscription invoice',
                'amount' => '$99.00',
                'status' => 'pending',
                'time' => '1 hour ago',
            ],
        ];
    }

    protected function getRecentTransactionActivities(): array
    {
        return [
            [
                'id' => '1',
                'type' => 'transaction',
                'title' => 'Payment received',
                'description' => 'Stripe payment processed',
                'amount' => '$29.99',
                'status' => 'complete',
                'time' => '30 minutes ago',
            ],
        ];
    }

    protected function getUserRecentOrders(User $user): array
    {
        return []; // Mock data
    }

    protected function getUserRecentDownloads(User $user): array
    {
        return []; // Mock data
    }

    protected function getUserRecentInvoices(User $user): array
    {
        return []; // Mock data
    }

    protected function getUserRecentTransactions(User $user): array
    {
        return []; // Mock data
    }

    protected function getTransactionSummary(User $user, string $period): array
    {
        return [
            'amount' => '0.00BDT',
            'count' => 0,
            'change' => null,
        ];
    }
}
