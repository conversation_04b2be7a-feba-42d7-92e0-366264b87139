<?php

namespace App\Services;

use App\Models\DigitalAsset;
use App\Models\DownloadLog;
use App\Models\DownloadToken;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class FileAnalyticsService
{
    /**
     * Get comprehensive download analytics for a product.
     */
    public function getProductAnalytics(Product $product, array $dateRange = []): array
    {
        $startDate = $dateRange['start'] ?? now()->subDays(30);
        $endDate = $dateRange['end'] ?? now();

        $assets = $product->digitalAssets;
        $assetIds = $assets->pluck('id');

        return [
            'overview' => $this->getProductOverview($product, $assetIds, $startDate, $endDate),
            'download_trends' => $this->getDownloadTrends($assetIds, $startDate, $endDate),
            'top_files' => $this->getTopDownloadedFiles($assetIds, $startDate, $endDate),
            'user_analytics' => $this->getUserAnalytics($assetIds, $startDate, $endDate),
            'geographic_data' => $this->getGeographicData($assetIds, $startDate, $endDate),
            'device_analytics' => $this->getDeviceAnalytics($assetIds, $startDate, $endDate),
            'time_analytics' => $this->getTimeAnalytics($assetIds, $startDate, $endDate),
        ];
    }

    /**
     * Get product overview statistics.
     */
    protected function getProductOverview(Product $product, $assetIds, $startDate, $endDate): array
    {
        $totalDownloads = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->count();

        $uniqueUsers = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->distinct('user_id')
            ->count('user_id');

        $uniqueIPs = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->distinct('ip_address')
            ->count('ip_address');

        $totalFileSize = $product->digitalAssets->sum('file_size');

        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays($endDate));
        $previousPeriodEnd = $startDate->copy();

        $previousDownloads = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$previousPeriodStart, $previousPeriodEnd])
            ->count();

        $downloadGrowth = $previousDownloads > 0 
            ? (($totalDownloads - $previousDownloads) / $previousDownloads) * 100 
            : 0;

        return [
            'total_downloads' => $totalDownloads,
            'unique_users' => $uniqueUsers,
            'unique_ips' => $uniqueIPs,
            'total_files' => $product->digitalAssets->count(),
            'active_files' => $product->digitalAssets->where('is_active', true)->count(),
            'total_file_size' => $totalFileSize,
            'total_file_size_formatted' => $this->formatBytes($totalFileSize),
            'download_growth_percentage' => round($downloadGrowth, 2),
            'average_downloads_per_file' => $product->digitalAssets->count() > 0 
                ? round($totalDownloads / $product->digitalAssets->count(), 2) 
                : 0,
        ];
    }

    /**
     * Get download trends over time.
     */
    protected function getDownloadTrends($assetIds, $startDate, $endDate): array
    {
        $downloads = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->selectRaw('DATE(downloaded_at) as date, COUNT(*) as downloads')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $trends = [];
        $current = $startDate->copy();

        while ($current <= $endDate) {
            $dateStr = $current->format('Y-m-d');
            $downloadCount = $downloads->where('date', $dateStr)->first()?->downloads ?? 0;
            
            $trends[] = [
                'date' => $dateStr,
                'downloads' => $downloadCount,
                'formatted_date' => $current->format('M j'),
            ];
            
            $current->addDay();
        }

        return $trends;
    }

    /**
     * Get top downloaded files.
     */
    protected function getTopDownloadedFiles($assetIds, $startDate, $endDate, int $limit = 10): array
    {
        return DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->select('digital_asset_id', DB::raw('COUNT(*) as download_count'))
            ->groupBy('digital_asset_id')
            ->orderByDesc('download_count')
            ->limit($limit)
            ->with('digitalAsset')
            ->get()
            ->map(function ($log) {
                return [
                    'asset_id' => $log->digital_asset_id,
                    'name' => $log->digitalAsset->original_name,
                    'file_size' => $log->digitalAsset->file_size,
                    'file_size_formatted' => $this->formatBytes($log->digitalAsset->file_size),
                    'download_count' => $log->download_count,
                    'mime_type' => $log->digitalAsset->mime_type,
                ];
            })
            ->toArray();
    }

    /**
     * Get user analytics.
     */
    protected function getUserAnalytics($assetIds, $startDate, $endDate): array
    {
        $registeredUsers = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->distinct('user_id')
            ->count();

        $guestUsers = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->whereNull('user_id')
            ->distinct('ip_address')
            ->count();

        $topUsers = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->select('user_id', DB::raw('COUNT(*) as download_count'))
            ->groupBy('user_id')
            ->orderByDesc('download_count')
            ->limit(10)
            ->with('user')
            ->get()
            ->map(function ($log) {
                return [
                    'user_id' => $log->user_id,
                    'name' => $log->user->name ?? 'Unknown',
                    'email' => $log->user->email ?? 'Unknown',
                    'download_count' => $log->download_count,
                ];
            });

        return [
            'registered_users' => $registeredUsers,
            'guest_users' => $guestUsers,
            'total_unique_users' => $registeredUsers + $guestUsers,
            'registered_vs_guest_ratio' => $guestUsers > 0 
                ? round($registeredUsers / $guestUsers, 2) 
                : $registeredUsers,
            'top_users' => $topUsers,
        ];
    }

    /**
     * Get geographic data (simplified IP-based analysis).
     */
    protected function getGeographicData($assetIds, $startDate, $endDate): array
    {
        $ipData = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->select('ip_address', DB::raw('COUNT(*) as download_count'))
            ->groupBy('ip_address')
            ->orderByDesc('download_count')
            ->limit(20)
            ->get();

        // In a real implementation, you would use a GeoIP service here
        // For now, we'll just return IP-based data
        return [
            'top_ips' => $ipData->map(function ($item) {
                return [
                    'ip_address' => $item->ip_address,
                    'download_count' => $item->download_count,
                    'country' => 'Unknown', // Would be resolved via GeoIP
                    'city' => 'Unknown',    // Would be resolved via GeoIP
                ];
            })->toArray(),
            'unique_ips' => $ipData->count(),
        ];
    }

    /**
     * Get device analytics based on user agents.
     */
    protected function getDeviceAnalytics($assetIds, $startDate, $endDate): array
    {
        $userAgents = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->select('user_agent', DB::raw('COUNT(*) as download_count'))
            ->groupBy('user_agent')
            ->orderByDesc('download_count')
            ->get();

        $browsers = [];
        $operatingSystems = [];

        foreach ($userAgents as $ua) {
            // Simple user agent parsing (in production, use a proper library)
            $browser = $this->extractBrowser($ua->user_agent);
            $os = $this->extractOS($ua->user_agent);

            $browsers[$browser] = ($browsers[$browser] ?? 0) + $ua->download_count;
            $operatingSystems[$os] = ($operatingSystems[$os] ?? 0) + $ua->download_count;
        }

        return [
            'browsers' => collect($browsers)->map(function ($count, $browser) {
                return ['name' => $browser, 'count' => $count];
            })->sortByDesc('count')->values()->take(10)->toArray(),
            'operating_systems' => collect($operatingSystems)->map(function ($count, $os) {
                return ['name' => $os, 'count' => $count];
            })->sortByDesc('count')->values()->take(10)->toArray(),
        ];
    }

    /**
     * Get time-based analytics.
     */
    protected function getTimeAnalytics($assetIds, $startDate, $endDate): array
    {
        $hourlyData = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->selectRaw('HOUR(downloaded_at) as hour, COUNT(*) as download_count')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->keyBy('hour');

        $dailyData = DownloadLog::whereIn('digital_asset_id', $assetIds)
            ->whereBetween('downloaded_at', [$startDate, $endDate])
            ->selectRaw('DAYOFWEEK(downloaded_at) as day_of_week, COUNT(*) as download_count')
            ->groupBy('day_of_week')
            ->orderBy('day_of_week')
            ->get()
            ->keyBy('day_of_week');

        $hourlyDistribution = [];
        for ($i = 0; $i < 24; $i++) {
            $hourlyDistribution[] = [
                'hour' => $i,
                'hour_formatted' => sprintf('%02d:00', $i),
                'download_count' => $hourlyData->get($i)?->download_count ?? 0,
            ];
        }

        $weeklyDistribution = [];
        $dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        for ($i = 1; $i <= 7; $i++) {
            $weeklyDistribution[] = [
                'day_of_week' => $i,
                'day_name' => $dayNames[$i - 1],
                'download_count' => $dailyData->get($i)?->download_count ?? 0,
            ];
        }

        return [
            'hourly_distribution' => $hourlyDistribution,
            'weekly_distribution' => $weeklyDistribution,
            'peak_hour' => collect($hourlyDistribution)->sortByDesc('download_count')->first(),
            'peak_day' => collect($weeklyDistribution)->sortByDesc('download_count')->first(),
        ];
    }

    /**
     * Get download token analytics.
     */
    public function getTokenAnalytics($assetIds, $startDate, $endDate): array
    {
        $tokens = DownloadToken::whereIn('file_id', $assetIds)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->get();

        return [
            'total_tokens_generated' => $tokens->count(),
            'active_tokens' => $tokens->where('is_active', true)->count(),
            'expired_tokens' => $tokens->filter(fn($t) => $t->isExpired())->count(),
            'exhausted_tokens' => $tokens->filter(fn($t) => $t->hasReachedLimit())->count(),
            'average_downloads_per_token' => $tokens->count() > 0 
                ? round($tokens->avg('download_count'), 2) 
                : 0,
            'token_usage_rate' => $tokens->count() > 0 
                ? round(($tokens->where('download_count', '>', 0)->count() / $tokens->count()) * 100, 2) 
                : 0,
        ];
    }

    /**
     * Format bytes to human readable format.
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Extract browser from user agent (simplified).
     */
    protected function extractBrowser(string $userAgent): string
    {
        if (strpos($userAgent, 'Chrome') !== false) return 'Chrome';
        if (strpos($userAgent, 'Firefox') !== false) return 'Firefox';
        if (strpos($userAgent, 'Safari') !== false) return 'Safari';
        if (strpos($userAgent, 'Edge') !== false) return 'Edge';
        if (strpos($userAgent, 'Opera') !== false) return 'Opera';
        return 'Other';
    }

    /**
     * Extract OS from user agent (simplified).
     */
    protected function extractOS(string $userAgent): string
    {
        if (strpos($userAgent, 'Windows') !== false) return 'Windows';
        if (strpos($userAgent, 'Macintosh') !== false) return 'macOS';
        if (strpos($userAgent, 'Linux') !== false) return 'Linux';
        if (strpos($userAgent, 'Android') !== false) return 'Android';
        if (strpos($userAgent, 'iPhone') !== false) return 'iOS';
        return 'Other';
    }
}
