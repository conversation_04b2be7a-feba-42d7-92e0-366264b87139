<?php

namespace App\Services;

use App\Models\DownloadLog;
use App\Models\DigitalAsset;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DownloadAnalyticsService
{
    /**
     * Get comprehensive download analytics overview.
     */
    public function getDownloadOverview(int $days = 30): array
    {
        $cacheKey = "download_overview_{$days}";
        
        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);
            $endDate = now();
            
            // Current period stats
            $currentStats = $this->getPeriodDownloadStats($startDate, $endDate);
            
            // Previous period for comparison
            $previousStartDate = $startDate->copy()->subDays($days);
            $previousEndDate = $startDate->copy();
            $previousStats = $this->getPeriodDownloadStats($previousStartDate, $previousEndDate);
            
            return [
                'current' => $currentStats,
                'previous' => $previousStats,
                'growth' => $this->calculateGrowth($currentStats, $previousStats),
                'period_days' => $days,
                'start_date' => $startDate->toDateString(),
                'end_date' => $endDate->toDateString(),
            ];
        });
    }

    /**
     * Get download statistics for a specific period.
     */
    protected function getPeriodDownloadStats(Carbon $startDate, Carbon $endDate): array
    {
        $downloads = DownloadLog::whereBetween('download_logs.created_at', [$startDate, $endDate]);
        
        $totalDownloads = $downloads->clone()->count();
        $successfulDownloads = $totalDownloads; // All logged downloads are successful
        $failedDownloads = 0; // No failed downloads are logged in this table
        $uniqueUsers = $downloads->clone()->distinct('user_id')->count('user_id');
        $uniqueFiles = $downloads->clone()->distinct('digital_asset_id')->count('digital_asset_id');

        // Calculate success rate (100% since only successful downloads are logged)
        $successRate = $totalDownloads > 0 ? 100 : 0;
        
        // Calculate total bandwidth used (in MB)
        $totalBandwidth = $downloads->clone()
            ->join('digital_assets', 'download_logs.digital_asset_id', '=', 'digital_assets.id')
            ->sum('digital_assets.file_size') / (1024 * 1024); // Convert to MB
        
        // Average downloads per user
        $avgDownloadsPerUser = $uniqueUsers > 0 ? $totalDownloads / $uniqueUsers : 0;
        
        return [
            'total_downloads' => $totalDownloads,
            'successful_downloads' => $successfulDownloads,
            'failed_downloads' => $failedDownloads,
            'success_rate' => round($successRate, 2),
            'unique_users' => $uniqueUsers,
            'unique_files' => $uniqueFiles,
            'total_bandwidth_mb' => round($totalBandwidth, 2),
            'avg_downloads_per_user' => round($avgDownloadsPerUser, 2),
        ];
    }

    /**
     * Calculate growth percentages between periods.
     */
    protected function calculateGrowth(array $current, array $previous): array
    {
        $growth = [];
        
        foreach ($current as $key => $value) {
            if (isset($previous[$key]) && $previous[$key] > 0) {
                $growth[$key] = round((($value - $previous[$key]) / $previous[$key]) * 100, 2);
            } else {
                $growth[$key] = $value > 0 ? 100 : 0;
            }
        }
        
        return $growth;
    }

    /**
     * Get daily download trends.
     */
    public function getDailyDownloadTrends(int $days = 30): array
    {
        $cacheKey = "daily_download_trends_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return DownloadLog::where('created_at', '>=', $startDate)
                ->groupBy('date')
                ->selectRaw('DATE(created_at) as date, COUNT(*) as downloads, COUNT(*) as successful')
                ->orderBy('date')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => $item->date,
                        'downloads' => $item->downloads,
                        'successful' => $item->successful,
                        'failed' => $item->downloads - $item->successful,
                        'success_rate' => $item->downloads > 0 ? round(($item->successful / $item->downloads) * 100, 2) : 0,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get hourly download patterns.
     */
    public function getHourlyDownloadPattern(int $days = 30): array
    {
        $cacheKey = "hourly_download_pattern_{$days}";
        
        return Cache::remember($cacheKey, 3600, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return DownloadLog::where('created_at', '>=', $startDate)
                ->groupBy('hour')
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as downloads, COUNT(*) as successful')
                ->orderBy('hour')
                ->get()
                ->map(function ($item) {
                    return [
                        'hour' => $item->hour,
                        'downloads' => $item->downloads,
                        'successful' => $item->successful,
                        'success_rate' => $item->downloads > 0 ? round(($item->successful / $item->downloads) * 100, 2) : 0,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get most downloaded files.
     */
    public function getMostDownloadedFiles(int $days = 30, int $limit = 10): array
    {
        $cacheKey = "most_downloaded_files_{$days}_{$limit}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            $startDate = now()->subDays($days);
            
            return DownloadLog::where('created_at', '>=', $startDate)
                ->with(['digitalAsset.product:id,name,slug', 'digitalAsset:id,product_id,original_name,file_size,mime_type'])
                ->groupBy('digital_asset_id')
                ->selectRaw('digital_asset_id, COUNT(*) as download_count, COUNT(*) as successful_downloads, COUNT(DISTINCT user_id) as unique_downloaders')
                ->orderBy('download_count', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($log) {
                    return [
                        'digital_asset' => $log->digitalAsset,
                        'product' => $log->digitalAsset->product,
                        'download_count' => $log->download_count,
                        'successful_downloads' => $log->successful_downloads,
                        'unique_downloaders' => $log->unique_downloaders,
                        'success_rate' => $log->download_count > 0 ? round(($log->successful_downloads / $log->download_count) * 100, 2) : 0,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get download statistics by file type.
     */
    public function getDownloadsByFileType(int $days = 30): array
    {
        $cacheKey = "downloads_by_file_type_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            return DownloadLog::where('download_logs.created_at', '>=', $startDate)
                ->join('digital_assets', 'download_logs.digital_asset_id', '=', 'digital_assets.id')
                ->selectRaw('
                    UPPER(SUBSTRING_INDEX(digital_assets.original_name, ".", -1)) as file_type,
                    COUNT(*) as download_count,
                    COUNT(*) as successful_downloads,
                    SUM(digital_assets.file_size) as total_size
                ')
                ->groupBy('file_type')
                ->orderBy('download_count', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'file_type' => $item->file_type ?: 'Unknown',
                        'download_count' => $item->download_count,
                        'successful_downloads' => $item->successful_downloads,
                        'success_rate' => $item->download_count > 0 ? round(($item->successful_downloads / $item->download_count) * 100, 2) : 0,
                        'total_size_mb' => round($item->total_size / (1024 * 1024), 2),
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get top downloading users.
     */
    public function getTopDownloadingUsers(int $days = 30, int $limit = 10): array
    {
        $cacheKey = "top_downloading_users_{$days}_{$limit}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days, $limit) {
            $startDate = now()->subDays($days);
            
            return DownloadLog::where('created_at', '>=', $startDate)
                ->with('user:id,name,email')
                ->whereNotNull('user_id')
                ->groupBy('user_id')
                ->selectRaw('user_id, COUNT(*) as download_count, COUNT(*) as successful_downloads, COUNT(DISTINCT digital_asset_id) as unique_files')
                ->orderBy('download_count', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($log) {
                    return [
                        'user' => $log->user,
                        'download_count' => $log->download_count,
                        'successful_downloads' => $log->successful_downloads,
                        'unique_files' => $log->unique_files,
                        'success_rate' => $log->download_count > 0 ? round(($log->successful_downloads / $log->download_count) * 100, 2) : 0,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Get download failure analysis.
     */
    public function getDownloadFailureAnalysis(int $days = 30): array
    {
        $cacheKey = "download_failure_analysis_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            // Since only successful downloads are logged, there are no failure records
            $failureReasons = [];
            $failuresByFileType = [];
            $failuresByHour = [];

            return [
                'failure_reasons' => $failureReasons,
                'failures_by_file_type' => $failuresByFileType,
                'failures_by_hour' => $failuresByHour,
            ];
        });
    }

    /**
     * Get bandwidth usage statistics.
     */
    public function getBandwidthUsage(int $days = 30): array
    {
        $cacheKey = "bandwidth_usage_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            // Daily bandwidth usage
            $dailyUsage = DownloadLog::where('download_logs.created_at', '>=', $startDate)
                ->join('digital_assets', 'download_logs.digital_asset_id', '=', 'digital_assets.id')
                ->groupBy('date')
                ->selectRaw('DATE(download_logs.created_at) as date, SUM(digital_assets.file_size) as total_bytes')
                ->orderBy('date')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => $item->date,
                        'total_mb' => round($item->total_bytes / (1024 * 1024), 2),
                        'total_gb' => round($item->total_bytes / (1024 * 1024 * 1024), 3),
                    ];
                })
                ->toArray();

            // Total bandwidth for period
            $totalBandwidth = DownloadLog::where('download_logs.created_at', '>=', $startDate)
                ->join('digital_assets', 'download_logs.digital_asset_id', '=', 'digital_assets.id')
                ->sum('digital_assets.file_size');

            return [
                'daily_usage' => $dailyUsage,
                'total_bytes' => $totalBandwidth,
                'total_mb' => round($totalBandwidth / (1024 * 1024), 2),
                'total_gb' => round($totalBandwidth / (1024 * 1024 * 1024), 3),
                'avg_daily_mb' => count($dailyUsage) > 0 ? round(array_sum(array_column($dailyUsage, 'total_mb')) / count($dailyUsage), 2) : 0,
            ];
        });
    }

    /**
     * Get download expiry analysis.
     */
    public function getDownloadExpiryAnalysis(int $days = 30): array
    {
        $cacheKey = "download_expiry_analysis_{$days}";
        
        return Cache::remember($cacheKey, 1800, function () use ($days) {
            $startDate = now()->subDays($days);
            
            $expiryStats = DownloadLog::where('created_at', '>=', $startDate)
                ->selectRaw('
                    COUNT(*) as total_downloads,
                    COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at < NOW() THEN 1 END) as expired_downloads,
                    COUNT(CASE WHEN expires_at IS NOT NULL AND expires_at > NOW() THEN 1 END) as active_downloads,
                    COUNT(CASE WHEN expires_at IS NULL THEN 1 END) as permanent_downloads
                ')
                ->first();

            return [
                'total_downloads' => $expiryStats->total_downloads ?? 0,
                'expired_downloads' => $expiryStats->expired_downloads ?? 0,
                'active_downloads' => $expiryStats->active_downloads ?? 0,
                'permanent_downloads' => $expiryStats->permanent_downloads ?? 0,
                'expiry_rate' => $expiryStats->total_downloads > 0 ? 
                    round(($expiryStats->expired_downloads / $expiryStats->total_downloads) * 100, 2) : 0,
            ];
        });
    }

    /**
     * Get download summary for dashboard.
     */
    public function getDownloadSummary(int $days = 7): array
    {
        $cacheKey = "download_summary_{$days}";

        return Cache::remember($cacheKey, 900, function () use ($days) {
            $startDate = now()->subDays($days);

            $stats = DownloadLog::where('created_at', '>=', $startDate)
                ->selectRaw('
                    COUNT(*) as total_downloads,
                    COUNT(*) as successful_downloads,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT digital_asset_id) as unique_files
                ')
                ->first();

            $successRate = $stats->total_downloads > 0 ?
                ($stats->successful_downloads / $stats->total_downloads) * 100 : 0;

            $topFile = DownloadLog::where('created_at', '>=', $startDate)
                ->with('digitalAsset:id,filename')
                ->groupBy('digital_asset_id')
                ->selectRaw('digital_asset_id, COUNT(*) as download_count')
                ->orderBy('download_count', 'desc')
                ->first();

            return [
                'total_downloads' => $stats->total_downloads ?? 0,
                'successful_downloads' => $stats->successful_downloads ?? 0,
                'success_rate' => round($successRate, 1),
                'unique_users' => $stats->unique_users ?? 0,
                'unique_files' => $stats->unique_files ?? 0,
                'top_file' => $topFile?->digitalAsset?->filename,
                'top_file_downloads' => $topFile?->download_count ?? 0,
            ];
        });
    }
}
