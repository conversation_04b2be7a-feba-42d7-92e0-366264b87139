<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductReview;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ProductRatingService
{
    /**
     * Update product rating statistics.
     */
    public function updateProductRatingStats(Product $product): void
    {
        $approvedReviews = $product->reviews()->approved();
        
        $totalReviews = $approvedReviews->count();
        $totalRatings = $totalReviews; // Same for now, could be different if we allow ratings without reviews
        
        if ($totalReviews > 0) {
            $averageRating = $approvedReviews->avg('rating');
            
            // Calculate rating breakdown
            $ratingBreakdown = [];
            for ($i = 1; $i <= 5; $i++) {
                $ratingBreakdown[$i] = $approvedReviews->where('rating', $i)->count();
            }
        } else {
            $averageRating = 0;
            $ratingBreakdown = [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0];
        }

        $product->update([
            'average_rating' => round($averageRating, 2),
            'total_reviews' => $totalReviews,
            'total_ratings' => $totalRatings,
            'rating_breakdown' => $ratingBreakdown,
        ]);

        // Clear cache for this product
        $this->clearProductRatingCache($product->id);
    }

    /**
     * Get cached product rating statistics.
     */
    public function getProductRatingStats(Product $product): array
    {
        $cacheKey = "product_rating_stats_{$product->id}";
        
        return Cache::remember($cacheKey, 3600, function () use ($product) {
            return [
                'average_rating' => $product->average_rating,
                'total_reviews' => $product->total_reviews,
                'total_ratings' => $product->total_ratings,
                'rating_breakdown' => $product->rating_breakdown ?: [1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0],
                'rating_breakdown_percentages' => $product->getRatingBreakdownPercentages(),
                'reviews_enabled' => $product->reviews_enabled,
            ];
        });
    }

    /**
     * Get top rated products.
     */
    public function getTopRatedProducts(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return Product::active()
            ->where('total_reviews', '>=', 3) // Minimum 3 reviews
            ->orderBy('average_rating', 'desc')
            ->orderBy('total_reviews', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get products by rating range.
     */
    public function getProductsByRatingRange(float $minRating, float $maxRating = 5.0, int $limit = null): \Illuminate\Database\Eloquent\Builder
    {
        $query = Product::active()
            ->whereBetween('average_rating', [$minRating, $maxRating])
            ->orderBy('average_rating', 'desc')
            ->orderBy('total_reviews', 'desc');

        if ($limit) {
            $query->limit($limit);
        }

        return $query;
    }

    /**
     * Get rating distribution for all products.
     */
    public function getOverallRatingDistribution(): array
    {
        $distribution = ProductReview::approved()
            ->select('rating', DB::raw('COUNT(*) as count'))
            ->groupBy('rating')
            ->orderBy('rating')
            ->pluck('count', 'rating')
            ->toArray();

        // Fill missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (!isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }

        ksort($distribution);
        return $distribution;
    }

    /**
     * Get average rating across all products.
     */
    public function getOverallAverageRating(): float
    {
        return ProductReview::approved()->avg('rating') ?: 0;
    }

    /**
     * Get products that need rating updates.
     */
    public function getProductsNeedingRatingUpdate(): \Illuminate\Database\Eloquent\Collection
    {
        // Products that have reviews but outdated rating stats
        return Product::whereHas('reviews', function ($query) {
            $query->approved();
        })
        ->where(function ($query) {
            $query->whereNull('average_rating')
                  ->orWhere('average_rating', 0)
                  ->orWhereNull('total_reviews')
                  ->orWhere('total_reviews', 0);
        })
        ->get();
    }

    /**
     * Bulk update rating statistics for multiple products.
     */
    public function bulkUpdateRatingStats(array $productIds = null): int
    {
        $query = Product::query();
        
        if ($productIds) {
            $query->whereIn('id', $productIds);
        } else {
            // Update products that have reviews
            $query->whereHas('reviews');
        }

        $products = $query->get();
        $updated = 0;

        foreach ($products as $product) {
            $this->updateProductRatingStats($product);
            $updated++;
        }

        return $updated;
    }

    /**
     * Get rating trends for a product over time.
     */
    public function getProductRatingTrends(Product $product, int $days = 30): array
    {
        $reviews = $product->reviews()
            ->approved()
            ->where('created_at', '>=', now()->subDays($days))
            ->select('rating', 'created_at')
            ->orderBy('created_at')
            ->get();

        $trends = [];
        $runningTotal = 0;
        $count = 0;

        foreach ($reviews as $review) {
            $runningTotal += $review->rating;
            $count++;
            $trends[] = [
                'date' => $review->created_at->format('Y-m-d'),
                'rating' => $review->rating,
                'average_so_far' => round($runningTotal / $count, 2),
            ];
        }

        return $trends;
    }

    /**
     * Get similar rated products.
     */
    public function getSimilarRatedProducts(Product $product, int $limit = 5): \Illuminate\Database\Eloquent\Collection
    {
        $ratingRange = 0.5; // +/- 0.5 stars
        
        return Product::active()
            ->where('id', '!=', $product->id)
            ->whereBetween('average_rating', [
                max(0, $product->average_rating - $ratingRange),
                min(5, $product->average_rating + $ratingRange)
            ])
            ->where('total_reviews', '>=', 1)
            ->orderBy('total_reviews', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clear product rating cache.
     */
    public function clearProductRatingCache(int $productId): void
    {
        Cache::forget("product_rating_stats_{$productId}");
    }

    /**
     * Clear all rating caches.
     */
    public function clearAllRatingCaches(): void
    {
        $productIds = Product::pluck('id');
        
        foreach ($productIds as $productId) {
            $this->clearProductRatingCache($productId);
        }
    }

    /**
     * Get rating statistics for admin dashboard.
     */
    public function getAdminRatingStats(): array
    {
        return [
            'total_reviews' => ProductReview::count(),
            'approved_reviews' => ProductReview::approved()->count(),
            'pending_reviews' => ProductReview::where('status', 'pending')->count(),
            'average_rating' => $this->getOverallAverageRating(),
            'rating_distribution' => $this->getOverallRatingDistribution(),
            'products_with_reviews' => Product::whereHas('reviews')->count(),
            'products_without_reviews' => Product::whereDoesntHave('reviews')->count(),
            'top_rated_products' => $this->getTopRatedProducts(5),
        ];
    }

    /**
     * Generate rating summary for a product.
     */
    public function generateRatingSummary(Product $product): string
    {
        if ($product->total_reviews === 0) {
            return 'No reviews yet';
        }

        $rating = $product->average_rating;
        $count = $product->total_reviews;
        
        $description = match (true) {
            $rating >= 4.5 => 'Excellent',
            $rating >= 4.0 => 'Very Good',
            $rating >= 3.5 => 'Good',
            $rating >= 3.0 => 'Average',
            $rating >= 2.0 => 'Below Average',
            default => 'Poor'
        };

        return sprintf(
            '%s (%s/5) based on %d %s',
            $description,
            number_format($rating, 1),
            $count,
            $count === 1 ? 'review' : 'reviews'
        );
    }
}
