<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CartService
{
    /**
     * Get or create cart for current user/session.
     */
    public function getCart(): Cart
    {
        $user = Auth::user();
        $sessionId = Session::getId();

        if ($user) {
            // For authenticated users, find or create cart by user_id
            $cart = Cart::active()
                ->forUser($user->id)
                ->first();

            if (!$cart) {
                $cart = Cart::create([
                    'user_id' => $user->id,
                    'session_id' => $sessionId,
                ]);
            }

            // Merge any guest cart with user cart
            $this->mergeGuestCart($cart, $sessionId);
        } else {
            // For guests, find or create cart by session_id
            $cart = Cart::active()
                ->forSession($sessionId)
                ->whereNull('user_id')
                ->first();

            if (!$cart) {
                $cart = Cart::create([
                    'session_id' => $sessionId,
                ]);
            }
        }

        return $cart;
    }

    /**
     * Add product to cart.
     */
    public function addToCart(int $productId, int $quantity = 1, array $options = []): CartItem
    {
        $product = Product::findOrFail($productId);
        
        if (!$product->isAvailable()) {
            throw new \Exception('Product is not available');
        }

        $cart = $this->getCart();
        return $cart->addItem($product, $quantity, $options);
    }

    /**
     * Update cart item quantity.
     */
    public function updateQuantity(int $cartItemId, int $quantity): bool
    {
        $cart = $this->getCart();
        return $cart->updateItemQuantity($cartItemId, $quantity);
    }

    /**
     * Remove item from cart.
     */
    public function removeItem(int $cartItemId): bool
    {
        $cart = $this->getCart();
        return $cart->removeItem($cartItemId);
    }

    /**
     * Clear entire cart.
     */
    public function clearCart(): void
    {
        $cart = $this->getCart();
        $cart->clear();
    }

    /**
     * Get cart with items.
     */
    public function getCartWithItems(): Cart
    {
        return $this->getCart()->load('items.product');
    }

    /**
     * Get cart item count.
     */
    public function getCartItemCount(): int
    {
        $cart = $this->getCart();
        return $cart->total_items;
    }

    /**
     * Get cart total amount.
     */
    public function getCartTotal(): float
    {
        $cart = $this->getCart();
        return $cart->total_amount;
    }

    /**
     * Check if product is in cart.
     */
    public function isProductInCart(int $productId): bool
    {
        $cart = $this->getCart();
        return $cart->items()->where('product_id', $productId)->exists();
    }

    /**
     * Get product quantity in cart.
     */
    public function getProductQuantityInCart(int $productId): int
    {
        $cart = $this->getCart();
        $item = $cart->items()->where('product_id', $productId)->first();
        return $item ? $item->quantity : 0;
    }

    /**
     * Merge guest cart with user cart when user logs in.
     */
    protected function mergeGuestCart(Cart $userCart, string $sessionId): void
    {
        $guestCart = Cart::active()
            ->forSession($sessionId)
            ->whereNull('user_id')
            ->first();

        if (!$guestCart || $guestCart->isEmpty()) {
            return;
        }

        foreach ($guestCart->items as $guestItem) {
            $existingItem = $userCart->items()
                ->where('product_id', $guestItem->product_id)
                ->first();

            if ($existingItem) {
                // Update quantity if item already exists
                $existingItem->quantity += $guestItem->quantity;
                $existingItem->total_price = $existingItem->quantity * $existingItem->unit_price;
                $existingItem->save();
            } else {
                // Move item to user cart
                $guestItem->update(['cart_id' => $userCart->id]);
            }
        }

        // Delete guest cart
        $guestCart->delete();
        
        // Recalculate user cart totals
        $userCart->calculateTotals();
    }

    /**
     * Clean up expired carts.
     */
    public function cleanupExpiredCarts(): int
    {
        $expiredCarts = Cart::expired()->get();
        $count = $expiredCarts->count();
        
        foreach ($expiredCarts as $cart) {
            $cart->delete();
        }
        
        return $count;
    }

    /**
     * Convert cart to order.
     */
    public function convertToOrder(array $billingDetails = []): \App\Models\Order
    {
        $cart = $this->getCartWithItems();
        
        if ($cart->isEmpty()) {
            throw new \Exception('Cannot create order from empty cart');
        }

        $order = $cart->convertToOrder($billingDetails);
        
        // Clear cart after successful order creation
        $cart->clear();
        
        return $order;
    }

    /**
     * Get cart summary for display.
     */
    public function getCartSummary(): array
    {
        $cart = $this->getCartWithItems();
        
        return [
            'items' => $cart->items->map(function ($item) {
                return [
                    'id' => $item->id,
                    'product_id' => $item->product_id,
                    'product_name' => $item->product_name,
                    'product_sku' => $item->product_sku,
                    'product_image' => $item->product_image,
                    'quantity' => $item->quantity,
                    'unit_price' => $item->unit_price,
                    'total_price' => $item->total_price,
                    'formatted_unit_price' => $item->formatted_unit_price,
                    'formatted_total_price' => $item->formatted_total_price,
                    'is_available' => $item->isProductAvailable(),
                    'options' => $item->options,
                ];
            }),
            'totals' => [
                'subtotal' => $cart->subtotal,
                'tax_amount' => $cart->tax_amount,
                'discount_amount' => $cart->discount_amount,
                'total_amount' => $cart->total_amount,
                'formatted_subtotal' => '$' . number_format($cart->subtotal, 2),
                'formatted_tax_amount' => '$' . number_format($cart->tax_amount, 2),
                'formatted_discount_amount' => '$' . number_format($cart->discount_amount, 2),
                'formatted_total_amount' => '$' . number_format($cart->total_amount, 2),
            ],
            'item_count' => $cart->total_items,
            'is_empty' => $cart->isEmpty(),
        ];
    }

    /**
     * Validate cart before checkout.
     */
    public function validateCart(): array
    {
        $cart = $this->getCartWithItems();
        $errors = [];

        if ($cart->isEmpty()) {
            $errors[] = 'Cart is empty';
            return $errors;
        }

        foreach ($cart->items as $item) {
            if (!$item->isProductAvailable()) {
                $errors[] = "Product '{$item->product_name}' is no longer available";
            }
            
            if ($item->quantity <= 0) {
                $errors[] = "Invalid quantity for product '{$item->product_name}'";
            }
        }

        return $errors;
    }
}
