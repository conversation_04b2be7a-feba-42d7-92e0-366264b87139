<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Models\OrderItem;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

class RelatedProductsService
{
    /**
     * Get related products for a given product.
     */
    public function getRelatedProducts(Product $product, int $limit = 6): Collection
    {
        $cacheKey = "related_products_{$product->id}_{$limit}";
        
        return Cache::remember($cacheKey, 3600, function () use ($product, $limit) {
            $relatedProducts = collect();
            
            // Strategy 1: Same category products (40% weight)
            $categoryProducts = $this->getProductsByCategory($product, ceil($limit * 0.4));
            $relatedProducts = $relatedProducts->merge($categoryProducts);
            
            // Strategy 2: Similar tags (30% weight)
            $tagProducts = $this->getProductsByTags($product, ceil($limit * 0.3));
            $relatedProducts = $relatedProducts->merge($tagProducts);
            
            // Strategy 3: Frequently bought together (20% weight)
            $boughtTogetherProducts = $this->getFrequentlyBoughtTogether($product, ceil($limit * 0.2));
            $relatedProducts = $relatedProducts->merge($boughtTogetherProducts);
            
            // Strategy 4: Similar price range (10% weight)
            $priceRangeProducts = $this->getProductsByPriceRange($product, ceil($limit * 0.1));
            $relatedProducts = $relatedProducts->merge($priceRangeProducts);
            
            // Remove duplicates and the original product
            $relatedProducts = $relatedProducts
                ->unique('id')
                ->reject(function ($relatedProduct) use ($product) {
                    return $relatedProduct->id === $product->id;
                });
            
            // If we don't have enough products, fill with popular products
            if ($relatedProducts->count() < $limit) {
                $popularProducts = $this->getPopularProducts($limit - $relatedProducts->count(), $relatedProducts->pluck('id')->toArray());
                $relatedProducts = $relatedProducts->merge($popularProducts);
            }
            
            return $relatedProducts->take($limit);
        });
    }

    /**
     * Get products from the same category.
     */
    protected function getProductsByCategory(Product $product, int $limit): Collection
    {
        if (!$product->category_id) {
            return collect();
        }

        return Product::active()
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->orderBy('average_rating', 'desc')
            ->orderBy('total_reviews', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get products with similar tags.
     */
    protected function getProductsByTags(Product $product, int $limit): Collection
    {
        if (!$product->tags || empty($product->tags)) {
            return collect();
        }

        $tags = is_array($product->tags) ? $product->tags : json_decode($product->tags, true);
        
        if (empty($tags)) {
            return collect();
        }

        return Product::active()
            ->where('id', '!=', $product->id)
            ->where(function ($query) use ($tags) {
                foreach ($tags as $tag) {
                    $query->orWhereJsonContains('tags', $tag);
                }
            })
            ->orderBy('average_rating', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get products frequently bought together.
     */
    protected function getFrequentlyBoughtTogether(Product $product, int $limit): Collection
    {
        // Find orders that contain this product
        $orderIds = OrderItem::where('product_id', $product->id)
            ->whereHas('order', function ($query) {
                $query->where('status', 'completed');
            })
            ->pluck('order_id')
            ->unique();

        if ($orderIds->isEmpty()) {
            return collect();
        }

        // Find other products in those orders
        $relatedProductIds = OrderItem::whereIn('order_id', $orderIds)
            ->where('product_id', '!=', $product->id)
            ->select('product_id', DB::raw('COUNT(*) as frequency'))
            ->groupBy('product_id')
            ->orderBy('frequency', 'desc')
            ->limit($limit)
            ->pluck('product_id');

        return Product::active()
            ->whereIn('id', $relatedProductIds)
            ->get();
    }

    /**
     * Get products in similar price range.
     */
    protected function getProductsByPriceRange(Product $product, int $limit): Collection
    {
        $priceRange = $product->price * 0.3; // 30% price range
        $minPrice = max(0, $product->price - $priceRange);
        $maxPrice = $product->price + $priceRange;

        return Product::active()
            ->where('id', '!=', $product->id)
            ->whereBetween('price', [$minPrice, $maxPrice])
            ->orderBy('average_rating', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get popular products as fallback.
     */
    protected function getPopularProducts(int $limit, array $excludeIds = []): Collection
    {
        return Product::active()
            ->whereNotIn('id', $excludeIds)
            ->orderBy('total_reviews', 'desc')
            ->orderBy('average_rating', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get personalized recommendations for a user.
     */
    public function getPersonalizedRecommendations(User $user, int $limit = 10): Collection
    {
        $cacheKey = "personalized_recommendations_{$user->id}_{$limit}";
        
        return Cache::remember($cacheKey, 1800, function () use ($user, $limit) {
            $recommendations = collect();
            
            // Get user's purchase history
            $purchasedProductIds = OrderItem::whereHas('order', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->where('status', 'completed');
            })->pluck('product_id')->unique();

            if ($purchasedProductIds->isNotEmpty()) {
                // Strategy 1: Based on purchased products
                foreach ($purchasedProductIds->take(3) as $productId) {
                    $product = Product::find($productId);
                    if ($product) {
                        $related = $this->getRelatedProducts($product, 3);
                        $recommendations = $recommendations->merge($related);
                    }
                }
            }

            // Strategy 2: Based on browsing history (if available)
            // This would require implementing view tracking
            
            // Strategy 3: Popular products in categories user has purchased from
            if ($purchasedProductIds->isNotEmpty()) {
                $categoryIds = Product::whereIn('id', $purchasedProductIds)
                    ->pluck('category_id')
                    ->unique()
                    ->filter();

                if ($categoryIds->isNotEmpty()) {
                    $categoryProducts = Product::active()
                        ->whereIn('category_id', $categoryIds)
                        ->whereNotIn('id', $purchasedProductIds)
                        ->orderBy('average_rating', 'desc')
                        ->orderBy('total_reviews', 'desc')
                        ->limit(5)
                        ->get();
                    
                    $recommendations = $recommendations->merge($categoryProducts);
                }
            }

            // Remove duplicates and products user already owns
            $recommendations = $recommendations
                ->unique('id')
                ->reject(function ($product) use ($purchasedProductIds) {
                    return $purchasedProductIds->contains($product->id);
                });

            // Fill with trending products if needed
            if ($recommendations->count() < $limit) {
                $trending = $this->getTrendingProducts($limit - $recommendations->count(), $recommendations->pluck('id')->toArray());
                $recommendations = $recommendations->merge($trending);
            }

            return $recommendations->take($limit);
        });
    }

    /**
     * Get trending products.
     */
    public function getTrendingProducts(int $limit = 10, array $excludeIds = []): Collection
    {
        $cacheKey = "trending_products_{$limit}_" . md5(implode(',', $excludeIds));
        
        return Cache::remember($cacheKey, 1800, function () use ($limit, $excludeIds) {
            // Products with recent orders and good ratings
            return Product::active()
                ->whereNotIn('id', $excludeIds)
                ->whereHas('orderItems', function ($query) {
                    $query->whereHas('order', function ($orderQuery) {
                        $orderQuery->where('created_at', '>=', now()->subDays(30))
                                  ->where('status', 'completed');
                    });
                })
                ->withCount(['orderItems as recent_orders_count' => function ($query) {
                    $query->whereHas('order', function ($orderQuery) {
                        $orderQuery->where('created_at', '>=', now()->subDays(30))
                                  ->where('status', 'completed');
                    });
                }])
                ->orderBy('recent_orders_count', 'desc')
                ->orderBy('average_rating', 'desc')
                ->limit($limit)
                ->get();
        });
    }

    /**
     * Get products similar to user's wishlist (if implemented).
     */
    public function getSimilarToWishlist(User $user, int $limit = 6): Collection
    {
        // This would be implemented when wishlist feature is added
        return collect();
    }

    /**
     * Clear related products cache.
     */
    public function clearRelatedProductsCache(int $productId): void
    {
        $patterns = [
            "related_products_{$productId}_*",
            "personalized_recommendations_*",
            "trending_products_*"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Clear all recommendation caches.
     */
    public function clearAllRecommendationCaches(): void
    {
        // This would require a more sophisticated cache tagging system
        // For now, we'll clear specific known patterns
        Cache::flush(); // Nuclear option - use with caution in production
    }

    /**
     * Get recommendation statistics for admin.
     */
    public function getRecommendationStats(): array
    {
        return [
            'total_products' => Product::active()->count(),
            'products_with_categories' => Product::active()->whereNotNull('category_id')->count(),
            'products_with_tags' => Product::active()->whereNotNull('tags')->count(),
            'products_with_orders' => Product::active()->whereHas('orderItems')->count(),
            'avg_related_products' => 6, // This could be calculated dynamically
        ];
    }
}
