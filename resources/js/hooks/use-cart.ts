import { useCart as useCartContext } from '@/contexts/cart-context';
import { useCallback } from 'react';

export function useCart() {
    const { state, addToCart, updateQuantity, removeItem, clearCart, refreshCart } = useCartContext();

    const addProduct = useCallback(async (productId: number, quantity = 1, options = {}) => {
        try {
            await addToCart(productId, quantity, options);
            return { success: true };
        } catch (error: any) {
            return { success: false, error: error.message };
        }
    }, [addToCart]);

    const updateItemQuantity = useCallback(async (itemId: number, quantity: number) => {
        try {
            await updateQuantity(itemId, quantity);
            return { success: true };
        } catch (error: any) {
            return { success: false, error: error.message };
        }
    }, [updateQuantity]);

    const removeCartItem = useCallback(async (itemId: number) => {
        try {
            await removeItem(itemId);
            return { success: true };
        } catch (error: any) {
            return { success: false, error: error.message };
        }
    }, [removeItem]);

    const emptyCart = useCallback(async () => {
        try {
            await clearCart();
            return { success: true };
        } catch (error: any) {
            return { success: false, error: error.message };
        }
    }, [clearCart]);

    const reloadCart = useCallback(async () => {
        try {
            await refreshCart();
            return { success: true };
        } catch (error: any) {
            return { success: false, error: error.message };
        }
    }, [refreshCart]);

    return {
        // State
        cart: state,
        items: state.items,
        totals: state.totals,
        itemCount: state.item_count,
        isEmpty: state.is_empty,
        isLoading: state.isLoading,
        error: state.error,

        // Actions
        addProduct,
        updateItemQuantity,
        removeCartItem,
        emptyCart,
        reloadCart,

        // Utilities
        isProductInCart: (productId: number) => 
            state.items.some(item => item.product_id === productId),
        
        getProductQuantity: (productId: number) => {
            const item = state.items.find(item => item.product_id === productId);
            return item ? item.quantity : 0;
        },

        getTotalPrice: () => state.totals.total_amount,
        getFormattedTotalPrice: () => state.totals.formatted_total_amount,
    };
}
