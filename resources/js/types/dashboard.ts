export interface DashboardStats {
  products?: {
    total: number;
    categories: number;
    subcategories: number;
  };
  users?: {
    total: number;
    listingAgents: number;
    franchises: number;
  };
  tickets?: {
    total: number;
    pending: number;
    resolved: number;
  };
  advertisements?: {
    total: number;
    published: number;
    pending: number;
  };
  packages?: {
    total: number;
    active: number;
    inactive: number;
  };
  courses?: {
    total: number;
    active: number;
    inactive: number;
  };
  companies?: {
    total: number;
    pending: number;
  };
  invoices?: {
    total: number;
    paid: number;
    unpaid: number;
  };
  participants?: {
    total: number;
    active: number;
    pending: number;
  };
  onlineUsers?: {
    total: number;
    system: number;
    customers: number;
  };
}

export interface ActivityItem {
  id: string;
  type: 'user' | 'order' | 'invoice' | 'transaction';
  title: string;
  description: string;
  amount?: string;
  status: 'pending' | 'complete' | 'inactive';
  time: string;
}

export interface DashboardActivities {
  users?: ActivityItem[];
  orders?: ActivityItem[];
  invoices?: ActivityItem[];
  transactions?: ActivityItem[];
}

export interface DashboardData {
  stats: DashboardStats;
  activities: DashboardActivities;
  transactions: ActivityItem[];
}

export interface SystemHealthItem {
  status: 'operational' | 'error' | 'warning';
  message: string;
  response_time?: string;
  error?: string;
  provider?: string;
  driver?: string;
  disk?: string;
}

export interface SystemHealth {
  database: SystemHealthItem;
  email: SystemHealthItem;
  storage: SystemHealthItem;
  cache: SystemHealthItem;
}

export interface AdminDashboardProps {
  dashboardData: DashboardData;
  systemHealth: SystemHealth;
}

export interface PaginationMeta {
  current_page: number;
  from: number;
  last_page: number;
  per_page: number;
  to: number;
  total: number;
}

export interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  links: PaginationLink[];
  meta: PaginationMeta;
}
