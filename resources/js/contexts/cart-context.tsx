import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import axios from 'axios';

// Types
interface CartItem {
    id: number;
    product_id: number;
    product_name: string;
    product_sku?: string;
    product_image?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    formatted_unit_price: string;
    formatted_total_price: string;
    is_available: boolean;
    options?: Record<string, any>;
}

interface CartTotals {
    subtotal: number;
    tax_amount: number;
    discount_amount: number;
    total_amount: number;
    formatted_subtotal: string;
    formatted_tax_amount: string;
    formatted_discount_amount: string;
    formatted_total_amount: string;
}

interface CartState {
    items: CartItem[];
    totals: CartTotals;
    item_count: number;
    is_empty: boolean;
    isLoading: boolean;
    error: string | null;
}

// Actions
type CartAction =
    | { type: 'SET_LOADING'; payload: boolean }
    | { type: 'SET_ERROR'; payload: string | null }
    | { type: 'SET_CART'; payload: Omit<CartState, 'isLoading' | 'error'> }
    | { type: 'CLEAR_CART' };

// Initial state
const initialState: CartState = {
    items: [],
    totals: {
        subtotal: 0,
        tax_amount: 0,
        discount_amount: 0,
        total_amount: 0,
        formatted_subtotal: '$0.00',
        formatted_tax_amount: '$0.00',
        formatted_discount_amount: '$0.00',
        formatted_total_amount: '$0.00',
    },
    item_count: 0,
    is_empty: true,
    isLoading: false,
    error: null,
};

// Reducer
function cartReducer(state: CartState, action: CartAction): CartState {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, isLoading: action.payload };
        case 'SET_ERROR':
            return { ...state, error: action.payload, isLoading: false };
        case 'SET_CART':
            return { 
                ...state, 
                ...action.payload, 
                isLoading: false, 
                error: null 
            };
        case 'CLEAR_CART':
            return { 
                ...initialState, 
                isLoading: false, 
                error: null 
            };
        default:
            return state;
    }
}

// Context
interface CartContextType {
    state: CartState;
    addToCart: (productId: number, quantity?: number, options?: Record<string, any>) => Promise<void>;
    updateQuantity: (itemId: number, quantity: number) => Promise<void>;
    removeItem: (itemId: number) => Promise<void>;
    clearCart: () => Promise<void>;
    refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider
interface CartProviderProps {
    children: ReactNode;
}

export function CartProvider({ children }: CartProviderProps) {
    const [state, dispatch] = useReducer(cartReducer, initialState);

    // API calls
    const apiCall = async (method: string, url: string, data?: any) => {
        try {
            const response = await axios({
                method,
                url: `/api/cart${url}`,
                data,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });
            return response.data;
        } catch (error: any) {
            throw new Error(error.response?.data?.message || 'An error occurred');
        }
    };

    const refreshCart = async () => {
        dispatch({ type: 'SET_LOADING', payload: true });
        try {
            const response = await apiCall('GET', '/');
            if (response.success) {
                dispatch({ type: 'SET_CART', payload: response.data });
            } else {
                dispatch({ type: 'SET_ERROR', payload: response.message });
            }
        } catch (error: any) {
            dispatch({ type: 'SET_ERROR', payload: error.message });
        }
    };

    const addToCart = async (productId: number, quantity = 1, options = {}) => {
        dispatch({ type: 'SET_LOADING', payload: true });
        try {
            const response = await apiCall('POST', '/', {
                product_id: productId,
                quantity,
                options,
            });
            if (response.success) {
                dispatch({ type: 'SET_CART', payload: response.data.cart });
            } else {
                dispatch({ type: 'SET_ERROR', payload: response.message });
            }
        } catch (error: any) {
            dispatch({ type: 'SET_ERROR', payload: error.message });
        }
    };

    const updateQuantity = async (itemId: number, quantity: number) => {
        dispatch({ type: 'SET_LOADING', payload: true });
        try {
            const response = await apiCall('PUT', `/${itemId}`, { quantity });
            if (response.success) {
                dispatch({ type: 'SET_CART', payload: response.data });
            } else {
                dispatch({ type: 'SET_ERROR', payload: response.message });
            }
        } catch (error: any) {
            dispatch({ type: 'SET_ERROR', payload: error.message });
        }
    };

    const removeItem = async (itemId: number) => {
        dispatch({ type: 'SET_LOADING', payload: true });
        try {
            const response = await apiCall('DELETE', `/${itemId}`);
            if (response.success) {
                dispatch({ type: 'SET_CART', payload: response.data });
            } else {
                dispatch({ type: 'SET_ERROR', payload: response.message });
            }
        } catch (error: any) {
            dispatch({ type: 'SET_ERROR', payload: error.message });
        }
    };

    const clearCart = async () => {
        dispatch({ type: 'SET_LOADING', payload: true });
        try {
            const response = await apiCall('DELETE', '/');
            if (response.success) {
                dispatch({ type: 'CLEAR_CART' });
            } else {
                dispatch({ type: 'SET_ERROR', payload: response.message });
            }
        } catch (error: any) {
            dispatch({ type: 'SET_ERROR', payload: error.message });
        }
    };

    // Load cart on mount
    useEffect(() => {
        refreshCart();
    }, []);

    const value: CartContextType = {
        state,
        addToCart,
        updateQuantity,
        removeItem,
        clearCart,
        refreshCart,
    };

    return (
        <CartContext.Provider value={value}>
            {children}
        </CartContext.Provider>
    );
}

// Hook
export function useCart() {
    const context = useContext(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
}
