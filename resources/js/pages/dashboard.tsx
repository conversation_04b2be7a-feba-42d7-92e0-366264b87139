import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { DefaultTransactionsSummaries } from '@/components/dashboard/transactions-summary';
import SmartLayout from '@/layouts/smart-layout';
import { type BreadcrumbItem } from '@/types';
import { DashboardData } from '@/types/dashboard';
import { Head, usePage } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Sample data for recent activities
const recentUsers = [
    {
        id: '1',
        type: 'user' as const,
        title: 'New user registration',
        description: '<EMAIL> joined the platform',
        status: 'complete' as const,
        time: '2 minutes ago',
    },
    {
        id: '2',
        type: 'user' as const,
        title: 'Profile updated',
        description: '<EMAIL> updated their profile',
        status: 'complete' as const,
        time: '15 minutes ago',
    },
];

const recentOrders = [
    {
        id: '1',
        type: 'order' as const,
        title: 'New order received',
        description: 'Digital product purchase',
        amount: '$29.99',
        status: 'pending' as const,
        time: '5 minutes ago',
    },
];

const recentInvoices = [
    {
        id: '1',
        type: 'invoice' as const,
        title: 'Invoice generated',
        description: 'Monthly subscription invoice',
        amount: '$99.00',
        status: 'pending' as const,
        time: '1 hour ago',
    },
];

const recentTransactions = [
    {
        id: '1',
        type: 'transaction' as const,
        title: 'Payment received',
        description: 'Stripe payment processed',
        amount: '$29.99',
        status: 'complete' as const,
        time: '30 minutes ago',
    },
];

interface DashboardProps {
    dashboardData: DashboardData;
}

export default function Dashboard() {
    const { dashboardData } = usePage<DashboardProps>().props;

    return (
        <SmartLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="space-y-8">
                {/* Header */}
                <div className="border-b border-slate-200 dark:border-slate-700 pb-6">
                    <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
                        Dashboard
                    </h1>
                    <p className="mt-2 text-slate-600 dark:text-slate-400">
                        Welcome back! Here's what's happening with your platform.
                    </p>
                </div>

                {/* Dashboard Statistics */}
                <DashboardStats data={dashboardData?.stats} />

                {/* Recent Activities Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
                    <RecentActivity
                        title="Users"
                        items={dashboardData?.activities?.users || recentUsers}
                    />

                    <RecentActivity
                        title="Orders"
                        items={dashboardData?.activities?.orders || recentOrders}
                    />

                    <RecentActivity
                        title="Invoices"
                        items={dashboardData?.activities?.invoices || recentInvoices}
                    />

                    <RecentActivity
                        title="Transactions"
                        items={dashboardData?.activities?.transactions || recentTransactions}
                    />
                </div>

                {/* Transaction Summaries */}
                <DefaultTransactionsSummaries />
            </div>
        </SmartLayout>
    );
}
