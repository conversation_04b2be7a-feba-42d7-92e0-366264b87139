import { Head, <PERSON>, usePage } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ShoppingBag, 
    Download, 
    DollarSign, 
    Clock, 
    CheckCircle, 
    AlertCircle,
    Package,
    FileText,
    User,
    Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface User {
    id: number;
    name: string;
    email: string;
    created_at: string;
}

interface Order {
    id: number;
    order_number: string;
    status: string;
    total_amount: number;
    formatted_total_amount: string;
    created_at: string;
    items: Array<{
        id: number;
        product_name: string;
        quantity: number;
        product: {
            id: number;
            name: string;
            slug: string;
        };
    }>;
}

interface DownloadStats {
    total_downloads: number;
    active_downloads: number;
    expired_downloads: number;
}

interface OrderStats {
    total_orders: number;
    completed_orders: number;
    pending_orders: number;
    total_spent: number;
}

interface PageProps {
    user: User;
    recentOrders: Order[];
    downloadStats: DownloadStats;
    orderStats: OrderStats;
}

export default function CustomerDashboard() {
    const { user, recentOrders, downloadStats, orderStats } = usePage<PageProps>().props;

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'processing':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-4 w-4" />;
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'processing':
                return <Package className="h-4 w-4" />;
            case 'cancelled':
                return <AlertCircle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <>
            <Head title="My Account Dashboard" />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white shadow-sm border-b">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Welcome back, {user.name}!</h1>
                                <p className="text-gray-600 mt-1">Manage your orders, downloads, and account settings</p>
                            </div>
                            <div className="mt-4 sm:mt-0 flex gap-3">
                                <Button variant="outline" asChild>
                                    <Link href={route('customer.profile')}>
                                        <User className="h-4 w-4 mr-2" />
                                        Profile
                                    </Link>
                                </Button>
                                <Button variant="outline" asChild>
                                    <Link href={route('shop.index')}>
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        Browse Shop
                                    </Link>
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Total Orders</p>
                                        <p className="text-2xl font-bold text-gray-900">{orderStats.total_orders}</p>
                                    </div>
                                    <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <ShoppingBag className="h-6 w-6 text-blue-600" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Total Spent</p>
                                        <p className="text-2xl font-bold text-gray-900">
                                            {formatCurrency(orderStats.total_spent)}
                                        </p>
                                    </div>
                                    <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <DollarSign className="h-6 w-6 text-green-600" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Active Downloads</p>
                                        <p className="text-2xl font-bold text-gray-900">{downloadStats.active_downloads}</p>
                                    </div>
                                    <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                        <Download className="h-6 w-6 text-purple-600" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Pending Orders</p>
                                        <p className="text-2xl font-bold text-gray-900">{orderStats.pending_orders}</p>
                                    </div>
                                    <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                        <Clock className="h-6 w-6 text-yellow-600" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Recent Orders */}
                        <div className="lg:col-span-2">
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between">
                                    <CardTitle>Recent Orders</CardTitle>
                                    <Button variant="outline" size="sm" asChild>
                                        <Link href={route('customer.orders')}>View All</Link>
                                    </Button>
                                </CardHeader>
                                <CardContent>
                                    {recentOrders.length > 0 ? (
                                        <div className="space-y-4">
                                            {recentOrders.map((order) => (
                                                <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                                                    <div className="flex-1">
                                                        <div className="flex items-center gap-3 mb-2">
                                                            <Link 
                                                                href={route('customer.order', order.order_number)}
                                                                className="font-medium text-gray-900 hover:text-blue-600"
                                                            >
                                                                #{order.order_number}
                                                            </Link>
                                                            <Badge className={cn('text-xs', getStatusColor(order.status))}>
                                                                <span className="flex items-center gap-1">
                                                                    {getStatusIcon(order.status)}
                                                                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                                </span>
                                                            </Badge>
                                                        </div>
                                                        <p className="text-sm text-gray-600">
                                                            {order.items.length} item{order.items.length !== 1 ? 's' : ''} • {order.formatted_total_amount}
                                                        </p>
                                                        <p className="text-xs text-gray-500 mt-1">
                                                            {new Date(order.created_at).toLocaleDateString()}
                                                        </p>
                                                    </div>
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('customer.order', order.order_number)}>
                                                            View
                                                        </Link>
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                            <p className="text-gray-500">No orders yet</p>
                                            <Button className="mt-4" asChild>
                                                <Link href={route('shop.index')}>Start Shopping</Link>
                                            </Button>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>

                        {/* Quick Actions & Download Stats */}
                        <div className="space-y-6">
                            {/* Quick Actions */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Quick Actions</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    <Button className="w-full justify-start" variant="outline" asChild>
                                        <Link href={route('customer.orders')}>
                                            <ShoppingBag className="h-4 w-4 mr-2" />
                                            View All Orders
                                        </Link>
                                    </Button>
                                    <Button className="w-full justify-start" variant="outline" asChild>
                                        <Link href={route('customer.downloads')}>
                                            <Download className="h-4 w-4 mr-2" />
                                            My Downloads
                                        </Link>
                                    </Button>
                                    <Button className="w-full justify-start" variant="outline" asChild>
                                        <Link href={route('customer.profile')}>
                                            <User className="h-4 w-4 mr-2" />
                                            Edit Profile
                                        </Link>
                                    </Button>
                                    <Button className="w-full justify-start" variant="outline" asChild>
                                        <Link href={route('customer.support')}>
                                            <Settings className="h-4 w-4 mr-2" />
                                            Get Support
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>

                            {/* Download Statistics */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Download Statistics</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Total Downloads</span>
                                        <span className="font-medium">{downloadStats.total_downloads}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Active Downloads</span>
                                        <span className="font-medium text-green-600">{downloadStats.active_downloads}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Expired Downloads</span>
                                        <span className="font-medium text-red-600">{downloadStats.expired_downloads}</span>
                                    </div>
                                    <Button className="w-full mt-4" variant="outline" asChild>
                                        <Link href={route('customer.downloads')}>
                                            <Download className="h-4 w-4 mr-2" />
                                            Manage Downloads
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}
