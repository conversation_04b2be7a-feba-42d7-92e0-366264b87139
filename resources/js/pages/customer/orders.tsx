import { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    Search, 
    Filter, 
    ShoppingBag, 
    Calendar,
    CheckCircle, 
    Clock, 
    AlertCircle,
    Package,
    FileText,
    ArrowLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Order {
    id: number;
    order_number: string;
    status: string;
    total_amount: number;
    formatted_total_amount: string;
    created_at: string;
    items: Array<{
        id: number;
        product_name: string;
        quantity: number;
        formatted_total_price: string;
        product: {
            id: number;
            name: string;
            slug: string;
        };
    }>;
}

interface PageProps {
    orders: {
        data: Order[];
        links: any[];
        meta: any;
    };
    filters: {
        status?: string;
        from_date?: string;
        to_date?: string;
        search?: string;
    };
}

export default function CustomerOrders() {
    const { orders, filters } = usePage<PageProps>().props;
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('customer.orders'), {
            ...filters,
            search: searchTerm,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(route('customer.orders'), {
            ...filters,
            [key]: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'processing':
                return 'bg-blue-100 text-blue-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="h-4 w-4" />;
            case 'pending':
                return <Clock className="h-4 w-4" />;
            case 'processing':
                return <Package className="h-4 w-4" />;
            case 'cancelled':
                return <AlertCircle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <>
            <Head title="My Orders" />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white shadow-sm border-b">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <div className="flex items-center gap-4">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href={route('customer.dashboard')}>
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to Dashboard
                                    </Link>
                                </Button>
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900">My Orders</h1>
                                    <p className="text-gray-600 mt-1">Track and manage your order history</p>
                                </div>
                            </div>
                            
                            {/* Search */}
                            <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
                                <Input
                                    type="text"
                                    placeholder="Search by order number..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" size="icon">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </form>
                        </div>
                    </div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Filters */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                {/* Status Filter */}
                                <div>
                                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                                        Order Status
                                    </label>
                                    <Select
                                        value={filters.status || ''}
                                        onValueChange={(value) => handleFilterChange('status', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Statuses" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All Statuses</SelectItem>
                                            <SelectItem value="pending">Pending</SelectItem>
                                            <SelectItem value="processing">Processing</SelectItem>
                                            <SelectItem value="completed">Completed</SelectItem>
                                            <SelectItem value="cancelled">Cancelled</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Date Range */}
                                <div>
                                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                                        From Date
                                    </label>
                                    <Input
                                        type="date"
                                        value={filters.from_date || ''}
                                        onChange={(e) => handleFilterChange('from_date', e.target.value)}
                                    />
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                                        To Date
                                    </label>
                                    <Input
                                        type="date"
                                        value={filters.to_date || ''}
                                        onChange={(e) => handleFilterChange('to_date', e.target.value)}
                                    />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Orders List */}
                    {orders.data.length > 0 ? (
                        <div className="space-y-4">
                            {orders.data.map((order) => (
                                <Card key={order.id} className="hover:shadow-md transition-shadow">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-3">
                                                    <Link 
                                                        href={route('customer.order', order.order_number)}
                                                        className="text-lg font-semibold text-gray-900 hover:text-blue-600"
                                                    >
                                                        Order #{order.order_number}
                                                    </Link>
                                                    <Badge className={cn('text-xs', getStatusColor(order.status))}>
                                                        <span className="flex items-center gap-1">
                                                            {getStatusIcon(order.status)}
                                                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                                        </span>
                                                    </Badge>
                                                </div>
                                                
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                                                    <div className="flex items-center gap-2">
                                                        <Calendar className="h-4 w-4" />
                                                        <span>Ordered on {new Date(order.created_at).toLocaleDateString()}</span>
                                                    </div>
                                                    <div className="flex items-center gap-2">
                                                        <ShoppingBag className="h-4 w-4" />
                                                        <span>{order.items.length} item{order.items.length !== 1 ? 's' : ''}</span>
                                                    </div>
                                                </div>

                                                {/* Order Items Preview */}
                                                <div className="mt-3">
                                                    <div className="text-sm text-gray-600">
                                                        {order.items.slice(0, 2).map((item, index) => (
                                                            <span key={item.id}>
                                                                {item.product_name}
                                                                {index < Math.min(order.items.length, 2) - 1 && ', '}
                                                            </span>
                                                        ))}
                                                        {order.items.length > 2 && (
                                                            <span> and {order.items.length - 2} more item{order.items.length - 2 !== 1 ? 's' : ''}</span>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="flex flex-col lg:items-end gap-3">
                                                <div className="text-right">
                                                    <div className="text-2xl font-bold text-gray-900">
                                                        {order.formatted_total_amount}
                                                    </div>
                                                </div>
                                                <Button asChild>
                                                    <Link href={route('customer.order', order.order_number)}>
                                                        View Details
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="text-center py-12">
                                <ShoppingBag className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                                <p className="text-gray-500 mb-6">
                                    {Object.keys(filters).some(key => filters[key as keyof typeof filters]) 
                                        ? "Try adjusting your filters to see more orders."
                                        : "You haven't placed any orders yet."
                                    }
                                </p>
                                <Button asChild>
                                    <Link href={route('shop.index')}>
                                        <ShoppingBag className="h-4 w-4 mr-2" />
                                        Start Shopping
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    )}

                    {/* Pagination */}
                    {orders.links && orders.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex gap-2">
                                {orders.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        disabled={!link.url}
                                        onClick={() => link.url && router.visit(link.url)}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
