import { useState } from 'react';
import { <PERSON>, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    Search, 
    Filter, 
    Download, 
    Clock,
    CheckCircle, 
    AlertCircle,
    FileText,
    ArrowLeft,
    Calendar,
    ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface DownloadToken {
    id: number;
    token: string;
    expires_at: string;
    download_count: number;
    max_downloads: number;
    is_active: boolean;
    created_at: string;
    used_at?: string;
    digital_asset: {
        id: number;
        original_name: string;
        file_size: number;
        formatted_file_size: string;
        product: {
            id: number;
            name: string;
            slug: string;
        };
    };
    order: {
        id: number;
        order_number: string;
    };
}

interface PageProps {
    downloads: {
        data: DownloadToken[];
        links: any[];
        meta: any;
    };
    filters: {
        status?: string;
        search?: string;
    };
}

export default function CustomerDownloads() {
    const { downloads, filters } = usePage<PageProps>().props;
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('customer.downloads'), {
            ...filters,
            search: searchTerm,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(route('customer.downloads'), {
            ...filters,
            [key]: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const isExpired = (expiresAt: string) => {
        return new Date(expiresAt) <= new Date();
    };

    const getDownloadStatus = (download: DownloadToken) => {
        if (!download.is_active) return 'inactive';
        if (isExpired(download.expires_at)) return 'expired';
        if (download.download_count >= download.max_downloads) return 'exhausted';
        return 'active';
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active':
                return 'bg-green-100 text-green-800';
            case 'expired':
                return 'bg-red-100 text-red-800';
            case 'exhausted':
                return 'bg-orange-100 text-orange-800';
            case 'inactive':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <CheckCircle className="h-4 w-4" />;
            case 'expired':
                return <Clock className="h-4 w-4" />;
            case 'exhausted':
                return <AlertCircle className="h-4 w-4" />;
            case 'inactive':
                return <FileText className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    const getStatusLabel = (status: string) => {
        switch (status) {
            case 'active':
                return 'Available';
            case 'expired':
                return 'Expired';
            case 'exhausted':
                return 'Download Limit Reached';
            case 'inactive':
                return 'Inactive';
            default:
                return 'Unknown';
        }
    };

    const handleDownload = (token: string) => {
        window.open(route('secure.download', token), '_blank');
    };

    return (
        <>
            <Head title="My Downloads" />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white shadow-sm border-b">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <div className="flex items-center gap-4">
                                <Button variant="outline" size="sm" asChild>
                                    <Link href={route('customer.dashboard')}>
                                        <ArrowLeft className="h-4 w-4 mr-2" />
                                        Back to Dashboard
                                    </Link>
                                </Button>
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900">My Downloads</h1>
                                    <p className="text-gray-600 mt-1">Access and manage your digital downloads</p>
                                </div>
                            </div>
                            
                            {/* Search */}
                            <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
                                <Input
                                    type="text"
                                    placeholder="Search by product name..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" size="icon">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </form>
                        </div>
                    </div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Filters */}
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Filter className="h-5 w-5" />
                                Filters
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {/* Status Filter */}
                                <div>
                                    <label className="text-sm font-medium text-gray-700 mb-2 block">
                                        Download Status
                                    </label>
                                    <Select
                                        value={filters.status || ''}
                                        onValueChange={(value) => handleFilterChange('status', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All Downloads" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="">All Downloads</SelectItem>
                                            <SelectItem value="active">Available</SelectItem>
                                            <SelectItem value="expired">Expired</SelectItem>
                                            <SelectItem value="used">Previously Downloaded</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Downloads List */}
                    {downloads.data.length > 0 ? (
                        <div className="space-y-4">
                            {downloads.data.map((download) => {
                                const status = getDownloadStatus(download);
                                const canDownload = status === 'active';
                                
                                return (
                                    <Card key={download.id} className="hover:shadow-md transition-shadow">
                                        <CardContent className="p-6">
                                            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-3">
                                                        <h3 className="text-lg font-semibold text-gray-900">
                                                            {download.digital_asset.product.name}
                                                        </h3>
                                                        <Badge className={cn('text-xs', getStatusColor(status))}>
                                                            <span className="flex items-center gap-1">
                                                                {getStatusIcon(status)}
                                                                {getStatusLabel(status)}
                                                            </span>
                                                        </Badge>
                                                    </div>
                                                    
                                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                                                        <div>
                                                            <span className="font-medium">File:</span> {download.digital_asset.original_name}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Size:</span> {download.digital_asset.formatted_file_size}
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Order:</span> 
                                                            <Link 
                                                                href={route('customer.order', download.order.order_number)}
                                                                className="text-blue-600 hover:text-blue-800 ml-1"
                                                            >
                                                                #{download.order.order_number}
                                                            </Link>
                                                        </div>
                                                        <div className="flex items-center gap-1">
                                                            <Calendar className="h-4 w-4" />
                                                            <span>Expires: {new Date(download.expires_at).toLocaleDateString()}</span>
                                                        </div>
                                                        <div>
                                                            <span className="font-medium">Downloads:</span> {download.download_count}/{download.max_downloads}
                                                        </div>
                                                        {download.used_at && (
                                                            <div>
                                                                <span className="font-medium">Last Downloaded:</span> {new Date(download.used_at).toLocaleDateString()}
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                <div className="flex flex-col gap-3">
                                                    <Button 
                                                        disabled={!canDownload}
                                                        onClick={() => canDownload && handleDownload(download.token)}
                                                        className="min-w-[120px]"
                                                    >
                                                        <Download className="h-4 w-4 mr-2" />
                                                        {canDownload ? 'Download' : 'Unavailable'}
                                                    </Button>
                                                    
                                                    <Button variant="outline" size="sm" asChild>
                                                        <Link href={route('shop.product', download.digital_asset.product.slug)}>
                                                            <ExternalLink className="h-4 w-4 mr-2" />
                                                            View Product
                                                        </Link>
                                                    </Button>
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>
                    ) : (
                        <Card>
                            <CardContent className="text-center py-12">
                                <Download className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No downloads found</h3>
                                <p className="text-gray-500 mb-6">
                                    {Object.keys(filters).some(key => filters[key as keyof typeof filters]) 
                                        ? "Try adjusting your filters to see more downloads."
                                        : "You don't have any downloads yet. Purchase digital products to get started."
                                    }
                                </p>
                                <Button asChild>
                                    <Link href={route('shop.index')}>
                                        Browse Digital Products
                                    </Link>
                                </Button>
                            </CardContent>
                        </Card>
                    )}

                    {/* Pagination */}
                    {downloads.links && downloads.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <div className="flex gap-2">
                                {downloads.links.map((link, index) => (
                                    <Button
                                        key={index}
                                        variant={link.active ? 'default' : 'outline'}
                                        size="sm"
                                        disabled={!link.url}
                                        onClick={() => link.url && router.visit(link.url)}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
