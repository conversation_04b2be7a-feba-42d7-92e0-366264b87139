import { useState } from 'react';
import { <PERSON>, Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
    ShoppingCart, 
    Star, 
    Download, 
    Shield, 
    Clock, 
    ArrowLeft,
    Heart,
    Share2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Product {
    id: number;
    name: string;
    slug: string;
    description: string;
    short_description: string;
    price: number;
    formatted_price: string;
    sale_price?: number;
    formatted_sale_price?: string;
    featured: boolean;
    image?: string;
    gallery?: string[];
    average_rating: number;
    total_reviews: number;
    category: {
        id: number;
        name: string;
        slug: string;
    };
    files_count: number;
    download_limit?: number;
    download_expiry_days?: number;
}

interface PageProps {
    product: Product;
    relatedProducts: Product[];
}

export default function ProductDetail({ product, relatedProducts }: PageProps) {
    const [selectedImage, setSelectedImage] = useState(0);
    const [quantity, setQuantity] = useState(1);

    const handleAddToCart = () => {
        router.post(route('cart.add'), {
            product_id: product.id,
            quantity: quantity,
        });
    };

    const renderStars = (rating: number) => {
        return Array.from({ length: 5 }, (_, i) => (
            <Star
                key={i}
                className={cn(
                    "h-4 w-4",
                    i < Math.floor(rating) 
                        ? "fill-yellow-400 text-yellow-400" 
                        : "text-gray-300"
                )}
            />
        ));
    };

    return (
        <>
            <Head title={`${product.name} - Digital Downloads`} />
            
            <div className="min-h-screen bg-gray-50">
                {/* Breadcrumb */}
                <div className="bg-white border-b">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Link href={route('home')} className="hover:text-gray-900">
                                Home
                            </Link>
                            <span>/</span>
                            <Link href={route('shop.index')} className="hover:text-gray-900">
                                Shop
                            </Link>
                            <span>/</span>
                            <Link 
                                href={route('shop.category', product.category.slug)} 
                                className="hover:text-gray-900"
                            >
                                {product.category.name}
                            </Link>
                            <span>/</span>
                            <span className="text-gray-900 font-medium">{product.name}</span>
                        </div>
                    </div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    {/* Back Button */}
                    <Button
                        variant="ghost"
                        onClick={() => window.history.back()}
                        className="mb-6"
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Shop
                    </Button>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                        {/* Product Images */}
                        <div className="space-y-4">
                            <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                                {product.image ? (
                                    <img
                                        src={product.image}
                                        alt={product.name}
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                                        <div className="text-center">
                                            <Download className="h-16 w-16 mx-auto mb-4" />
                                            <p className="text-lg font-medium">Digital Product</p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Gallery thumbnails */}
                            {product.gallery && product.gallery.length > 0 && (
                                <div className="grid grid-cols-4 gap-2">
                                    {product.gallery.map((image, index) => (
                                        <button
                                            key={index}
                                            onClick={() => setSelectedImage(index)}
                                            className={cn(
                                                "aspect-square bg-gray-100 rounded-lg overflow-hidden border-2",
                                                selectedImage === index 
                                                    ? "border-blue-500" 
                                                    : "border-transparent"
                                            )}
                                        >
                                            <img
                                                src={image}
                                                alt={`${product.name} ${index + 1}`}
                                                className="w-full h-full object-cover"
                                            />
                                        </button>
                                    ))}
                                </div>
                            )}
                        </div>

                        {/* Product Info */}
                        <div className="space-y-6">
                            <div>
                                <div className="flex items-center gap-2 mb-2">
                                    <Badge variant="secondary">{product.category.name}</Badge>
                                    {product.featured && (
                                        <Badge variant="default">Featured</Badge>
                                    )}
                                </div>
                                <h1 className="text-3xl font-bold text-gray-900 mb-4">
                                    {product.name}
                                </h1>
                                
                                {/* Rating */}
                                <div className="flex items-center gap-2 mb-4">
                                    <div className="flex items-center">
                                        {renderStars(product.average_rating)}
                                    </div>
                                    <span className="text-sm text-gray-600">
                                        ({product.total_reviews} reviews)
                                    </span>
                                </div>

                                <p className="text-gray-600 text-lg leading-relaxed">
                                    {product.short_description}
                                </p>
                            </div>

                            {/* Price */}
                            <div className="flex items-center gap-3">
                                {product.sale_price ? (
                                    <>
                                        <span className="text-3xl font-bold text-green-600">
                                            {product.formatted_sale_price}
                                        </span>
                                        <span className="text-xl text-gray-500 line-through">
                                            {product.formatted_price}
                                        </span>
                                        <Badge variant="destructive">
                                            Save {Math.round(((product.price - product.sale_price) / product.price) * 100)}%
                                        </Badge>
                                    </>
                                ) : (
                                    <span className="text-3xl font-bold text-gray-900">
                                        {product.formatted_price}
                                    </span>
                                )}
                            </div>

                            {/* Product Features */}
                            <div className="grid grid-cols-2 gap-4">
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Download className="h-4 w-4" />
                                    <span>{product.files_count} files included</span>
                                </div>
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Shield className="h-4 w-4" />
                                    <span>Secure download</span>
                                </div>
                                {product.download_limit && (
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Clock className="h-4 w-4" />
                                        <span>{product.download_limit} downloads</span>
                                    </div>
                                )}
                                {product.download_expiry_days && (
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <Clock className="h-4 w-4" />
                                        <span>{product.download_expiry_days} days access</span>
                                    </div>
                                )}
                            </div>

                            {/* Actions */}
                            <div className="space-y-4">
                                <Button
                                    onClick={handleAddToCart}
                                    size="lg"
                                    className="w-full"
                                >
                                    <ShoppingCart className="h-5 w-5 mr-2" />
                                    Add to Cart
                                </Button>
                                
                                <div className="flex gap-2">
                                    <Button variant="outline" size="sm" className="flex-1">
                                        <Heart className="h-4 w-4 mr-2" />
                                        Wishlist
                                    </Button>
                                    <Button variant="outline" size="sm" className="flex-1">
                                        <Share2 className="h-4 w-4 mr-2" />
                                        Share
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Product Description */}
                    <Card className="mb-8">
                        <CardHeader>
                            <h2 className="text-2xl font-bold">Product Description</h2>
                        </CardHeader>
                        <CardContent>
                            <div 
                                className="prose max-w-none"
                                dangerouslySetInnerHTML={{ __html: product.description }}
                            />
                        </CardContent>
                    </Card>

                    {/* Related Products */}
                    {relatedProducts.length > 0 && (
                        <div>
                            <h2 className="text-2xl font-bold mb-6">Related Products</h2>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                                {relatedProducts.map((relatedProduct) => (
                                    <Card key={relatedProduct.id} className="group hover:shadow-lg transition-shadow">
                                        <CardContent className="p-4">
                                            <div className="aspect-square bg-gray-100 rounded-lg mb-4 overflow-hidden">
                                                {relatedProduct.image ? (
                                                    <img
                                                        src={relatedProduct.image}
                                                        alt={relatedProduct.name}
                                                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                                                        <Download className="h-8 w-8" />
                                                    </div>
                                                )}
                                            </div>
                                            <h3 className="font-semibold mb-2 line-clamp-2">
                                                <Link 
                                                    href={route('shop.product', relatedProduct.slug)}
                                                    className="hover:text-blue-600"
                                                >
                                                    {relatedProduct.name}
                                                </Link>
                                            </h3>
                                            <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                                {relatedProduct.short_description}
                                            </p>
                                            <div className="flex items-center justify-between">
                                                <span className="font-bold text-lg">
                                                    {relatedProduct.formatted_sale_price || relatedProduct.formatted_price}
                                                </span>
                                                <Button size="sm">
                                                    Add to Cart
                                                </Button>
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
