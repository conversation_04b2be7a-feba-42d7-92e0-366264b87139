import { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Grid, List, ShoppingCart, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Product {
    id: number;
    name: string;
    slug: string;
    description: string;
    short_description: string;
    price: number;
    formatted_price: string;
    sale_price?: number;
    formatted_sale_price?: string;
    featured: boolean;
    image?: string;
    category: {
        id: number;
        name: string;
        slug: string;
    };
    files_count: number;
}

interface Category {
    id: number;
    name: string;
    slug: string;
}

interface PriceRange {
    min_price: number;
    max_price: number;
}

interface PageProps {
    products: {
        data: Product[];
        links: any[];
        meta: any;
    };
    categories: Category[];
    priceRange: PriceRange;
    filters: {
        search?: string;
        category?: string;
        min_price?: string;
        max_price?: string;
        featured?: string;
    };
    sort: {
        by: string;
        order: string;
    };
}

export default function ShopIndex() {
    const { products, categories, priceRange, filters, sort } = usePage<PageProps>().props;
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
    const [searchTerm, setSearchTerm] = useState(filters.search || '');

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(route('shop.index'), {
            ...filters,
            search: searchTerm,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleFilterChange = (key: string, value: string) => {
        router.get(route('shop.index'), {
            ...filters,
            [key]: value,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSortChange = (sortBy: string) => {
        const newOrder = sort.by === sortBy && sort.order === 'asc' ? 'desc' : 'asc';
        router.get(route('shop.index'), {
            ...filters,
            sort_by: sortBy,
            sort_order: newOrder,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    return (
        <>
            <Head title="Shop - Digital Downloads" />
            
            <div className="min-h-screen bg-gray-50">
                {/* Header */}
                <div className="bg-white shadow-sm border-b">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Digital Downloads</h1>
                                <p className="text-gray-600 mt-1">Discover amazing digital products</p>
                            </div>
                            
                            {/* Search */}
                            <form onSubmit={handleSearch} className="flex gap-2 max-w-md w-full lg:w-auto">
                                <Input
                                    type="text"
                                    placeholder="Search products..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="flex-1"
                                />
                                <Button type="submit" size="icon">
                                    <Search className="h-4 w-4" />
                                </Button>
                            </form>
                        </div>
                    </div>
                </div>

                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <div className="flex flex-col lg:flex-row gap-8">
                        {/* Sidebar Filters */}
                        <div className="lg:w-64 space-y-6">
                            <Card>
                                <CardHeader>
                                    <h3 className="font-semibold flex items-center gap-2">
                                        <Filter className="h-4 w-4" />
                                        Filters
                                    </h3>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Categories */}
                                    <div>
                                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                                            Category
                                        </label>
                                        <Select
                                            value={filters.category || 'all'}
                                            onValueChange={(value) => handleFilterChange('category', value === 'all' ? '' : value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="All Categories" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="all">All Categories</SelectItem>
                                                {categories.map((category) => (
                                                    <SelectItem key={category.id} value={category.id.toString()}>
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    {/* Price Range */}
                                    <div>
                                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                                            Price Range
                                        </label>
                                        <div className="flex gap-2">
                                            <Input
                                                type="number"
                                                placeholder="Min"
                                                value={filters.min_price || ''}
                                                onChange={(e) => handleFilterChange('min_price', e.target.value)}
                                                min={0}
                                                max={priceRange.max_price}
                                            />
                                            <Input
                                                type="number"
                                                placeholder="Max"
                                                value={filters.max_price || ''}
                                                onChange={(e) => handleFilterChange('max_price', e.target.value)}
                                                min={priceRange.min_price}
                                                max={priceRange.max_price}
                                            />
                                        </div>
                                    </div>

                                    {/* Featured */}
                                    <div>
                                        <label className="flex items-center space-x-2">
                                            <input
                                                type="checkbox"
                                                checked={filters.featured === 'true'}
                                                onChange={(e) => handleFilterChange('featured', e.target.checked ? 'true' : '')}
                                                className="rounded border-gray-300"
                                            />
                                            <span className="text-sm text-gray-700">Featured only</span>
                                        </label>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Main Content */}
                        <div className="flex-1">
                            {/* Toolbar */}
                            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                                <div className="text-sm text-gray-600">
                                    Showing {products.meta?.from || 0} to {products.meta?.to || 0} of {products.meta?.total || 0} products
                                </div>
                                
                                <div className="flex items-center gap-4">
                                    {/* Sort */}
                                    <Select
                                        value={`${sort.by}_${sort.order}`}
                                        onValueChange={(value) => {
                                            const [sortBy, sortOrder] = value.split('_');
                                            router.get(route('shop.index'), {
                                                ...filters,
                                                sort_by: sortBy,
                                                sort_order: sortOrder,
                                            });
                                        }}
                                    >
                                        <SelectTrigger className="w-48">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="created_at_desc">Newest First</SelectItem>
                                            <SelectItem value="created_at_asc">Oldest First</SelectItem>
                                            <SelectItem value="name_asc">Name A-Z</SelectItem>
                                            <SelectItem value="name_desc">Name Z-A</SelectItem>
                                            <SelectItem value="price_asc">Price Low to High</SelectItem>
                                            <SelectItem value="price_desc">Price High to Low</SelectItem>
                                        </SelectContent>
                                    </Select>

                                    {/* View Mode */}
                                    <div className="flex border rounded-md">
                                        <Button
                                            variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                            size="sm"
                                            onClick={() => setViewMode('grid')}
                                            className="rounded-r-none"
                                        >
                                            <Grid className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant={viewMode === 'list' ? 'default' : 'ghost'}
                                            size="sm"
                                            onClick={() => setViewMode('list')}
                                            className="rounded-l-none"
                                        >
                                            <List className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            {/* Products Grid/List */}
                            {products.data.length > 0 ? (
                                <div className={cn(
                                    viewMode === 'grid' 
                                        ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'
                                        : 'space-y-4'
                                )}>
                                    {products.data.map((product) => (
                                        <ProductCard 
                                            key={product.id} 
                                            product={product} 
                                            viewMode={viewMode}
                                        />
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-12">
                                    <p className="text-gray-500 text-lg">No products found.</p>
                                    <p className="text-gray-400 mt-2">Try adjusting your filters or search terms.</p>
                                </div>
                            )}

                            {/* Pagination */}
                            {products.links && products.links.length > 3 && (
                                <div className="mt-8 flex justify-center">
                                    <div className="flex gap-2">
                                        {products.links.map((link, index) => (
                                            <Button
                                                key={index}
                                                variant={link.active ? 'default' : 'outline'}
                                                size="sm"
                                                disabled={!link.url}
                                                onClick={() => link.url && router.visit(link.url)}
                                                dangerouslySetInnerHTML={{ __html: link.label }}
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}

function ProductCard({ product, viewMode }: { product: Product; viewMode: 'grid' | 'list' }) {
    if (viewMode === 'list') {
        return (
            <Card className="flex flex-col sm:flex-row">
                <div className="sm:w-48 h-48 sm:h-auto bg-gray-100 flex items-center justify-center">
                    {product.image ? (
                        <img 
                            src={product.image} 
                            alt={product.name}
                            className="w-full h-full object-cover"
                        />
                    ) : (
                        <div className="text-gray-400">No Image</div>
                    )}
                </div>
                <div className="flex-1 p-6">
                    <div className="flex justify-between items-start">
                        <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                                <Link 
                                    href={route('shop.product', product.slug)}
                                    className="text-lg font-semibold text-gray-900 hover:text-blue-600"
                                >
                                    {product.name}
                                </Link>
                                {product.featured && (
                                    <Badge variant="secondary">
                                        <Star className="h-3 w-3 mr-1" />
                                        Featured
                                    </Badge>
                                )}
                            </div>
                            <p className="text-gray-600 mb-2">{product.short_description}</p>
                            <div className="flex items-center gap-2 text-sm text-gray-500">
                                <span>{product.category.name}</span>
                                <span>•</span>
                                <span>{product.files_count} files</span>
                            </div>
                        </div>
                        <div className="text-right ml-4">
                            <div className="text-2xl font-bold text-gray-900">
                                {product.formatted_sale_price || product.formatted_price}
                            </div>
                            {product.sale_price && (
                                <div className="text-sm text-gray-500 line-through">
                                    {product.formatted_price}
                                </div>
                            )}
                            <Button className="mt-2" size="sm">
                                <ShoppingCart className="h-4 w-4 mr-2" />
                                Add to Cart
                            </Button>
                        </div>
                    </div>
                </div>
            </Card>
        );
    }

    return (
        <Card className="group hover:shadow-lg transition-shadow">
            <div className="aspect-square bg-gray-100 relative overflow-hidden">
                {product.image ? (
                    <img 
                        src={product.image} 
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                    />
                ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                        No Image
                    </div>
                )}
                {product.featured && (
                    <Badge className="absolute top-2 left-2">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                    </Badge>
                )}
            </div>
            <CardHeader className="pb-2">
                <Link 
                    href={route('shop.product', product.slug)}
                    className="font-semibold text-gray-900 hover:text-blue-600 line-clamp-2"
                >
                    {product.name}
                </Link>
                <p className="text-sm text-gray-600 line-clamp-2">{product.short_description}</p>
            </CardHeader>
            <CardContent className="pt-0">
                <div className="flex items-center justify-between text-sm text-gray-500 mb-3">
                    <span>{product.category.name}</span>
                    <span>{product.files_count} files</span>
                </div>
                <div className="flex items-center justify-between">
                    <div>
                        <div className="text-lg font-bold text-gray-900">
                            {product.formatted_sale_price || product.formatted_price}
                        </div>
                        {product.sale_price && (
                            <div className="text-sm text-gray-500 line-through">
                                {product.formatted_price}
                            </div>
                        )}
                    </div>
                    <Button size="sm">
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
