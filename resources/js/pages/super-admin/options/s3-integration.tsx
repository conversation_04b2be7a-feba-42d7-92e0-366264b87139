import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
    Database,
    Save,
    TestTube,
    CheckCircle,
    XCircle,
    AlertTriangle,
    Info,
    Settings,
    Cloud,
    Shield,
    HardDrive
} from 'lucide-react';
import { useState } from 'react';

interface S3Settings {
    aws_access_key_id: string;
    aws_secret_access_key: string;
    aws_default_region: string;
    aws_bucket: string;
    aws_digital_assets_bucket: string;
    aws_url: string;
    aws_endpoint: string;
    aws_use_path_style_endpoint: boolean;
    digital_assets_driver: string;
    s3_enabled: boolean;
}

interface ConnectionTestResult {
    success: boolean;
    message: string;
    details?: any;
}

interface PageProps {
    settings: S3Settings;
}

export default function S3Integration() {
    const { settings } = usePage<PageProps>().props;
    const [connectionStatus, setConnectionStatus] = useState<ConnectionTestResult | null>(null);
    const [isTestingConnection, setIsTestingConnection] = useState(false);

    const { data, setData, post, processing, errors, reset } = useForm<S3Settings>({
        aws_access_key_id: settings?.aws_access_key_id || '',
        aws_secret_access_key: settings?.aws_secret_access_key || '',
        aws_default_region: settings?.aws_default_region || 'us-east-1',
        aws_bucket: settings?.aws_bucket || '',
        aws_digital_assets_bucket: settings?.aws_digital_assets_bucket || '',
        aws_url: settings?.aws_url || '',
        aws_endpoint: settings?.aws_endpoint || '',
        aws_use_path_style_endpoint: settings?.aws_use_path_style_endpoint || false,
        digital_assets_driver: settings?.digital_assets_driver || 'local',
        s3_enabled: settings?.s3_enabled || false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('super-admin.options.s3-integration.update'), {
            onSuccess: () => {
                setConnectionStatus(null);
            },
        });
    };

    const testConnection = async () => {
        setIsTestingConnection(true);
        setConnectionStatus(null);

        try {
            const response = await fetch(route('super-admin.options.s3-integration.test-connection'), {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            const result = await response.json();
            setConnectionStatus(result);
        } catch (error) {
            setConnectionStatus({
                success: false,
                message: 'Failed to test connection. Please check your network and try again.',
            });
        } finally {
            setIsTestingConnection(false);
        }
    };

    return (
        <SuperAdminLayout>
            <Head title="S3 Integration Settings" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">S3 Integration</h1>
                        <p className="text-muted-foreground">
                            Configure Amazon S3 cloud storage for digital assets and file management
                        </p>
                    </div>
                    <Badge variant="outline" className="flex items-center gap-2">
                        <Database className="h-4 w-4" />
                        Cloud Storage
                    </Badge>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* General Errors */}
                    {(errors.error || errors.s3_connection || errors.s3_enabled) && (
                        <Alert className="border-red-200 bg-red-50">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                            <AlertDescription className="text-red-800">
                                {errors.error || errors.s3_connection || errors.s3_enabled}
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* S3 Configuration */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Cloud className="h-5 w-5" />
                                AWS S3 Configuration
                            </CardTitle>
                            <CardDescription>
                                Configure your Amazon Web Services S3 credentials and bucket settings
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="s3_enabled"
                                    checked={data.s3_enabled}
                                    onCheckedChange={(checked) => setData('s3_enabled', checked)}
                                />
                                <Label htmlFor="s3_enabled">Enable S3 Integration</Label>
                            </div>

                            <Separator />

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="aws_access_key_id">
                                        AWS Access Key ID
                                        {data.s3_enabled && <span className="text-red-500 ml-1">*</span>}
                                    </Label>
                                    <Input
                                        id="aws_access_key_id"
                                        type="password"
                                        value={data.aws_access_key_id}
                                        onChange={(e) => setData('aws_access_key_id', e.target.value)}
                                        placeholder="AKIA1234567890EXAMPLE"
                                        disabled={!data.s3_enabled}
                                        className={errors.aws_access_key_id ? 'border-red-500' : ''}
                                    />
                                    {errors.aws_access_key_id && (
                                        <p className="text-sm text-destructive">{errors.aws_access_key_id}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        Must start with AKIA and be 20 characters long
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="aws_secret_access_key">
                                        AWS Secret Access Key
                                        {data.s3_enabled && <span className="text-red-500 ml-1">*</span>}
                                    </Label>
                                    <Input
                                        id="aws_secret_access_key"
                                        type="password"
                                        value={data.aws_secret_access_key}
                                        onChange={(e) => setData('aws_secret_access_key', e.target.value)}
                                        placeholder="wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                                        disabled={!data.s3_enabled}
                                        className={errors.aws_secret_access_key ? 'border-red-500' : ''}
                                    />
                                    {errors.aws_secret_access_key && (
                                        <p className="text-sm text-destructive">{errors.aws_secret_access_key}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        Must be exactly 40 characters long
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="aws_default_region">
                                        AWS Region
                                        {data.s3_enabled && <span className="text-red-500 ml-1">*</span>}
                                    </Label>
                                    <Input
                                        id="aws_default_region"
                                        value={data.aws_default_region}
                                        onChange={(e) => setData('aws_default_region', e.target.value)}
                                        placeholder="us-east-1"
                                        disabled={!data.s3_enabled}
                                        className={errors.aws_default_region ? 'border-red-500' : ''}
                                    />
                                    {errors.aws_default_region && (
                                        <p className="text-sm text-destructive">{errors.aws_default_region}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        e.g., us-east-1, eu-west-1, ap-southeast-1
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="aws_bucket">
                                        Default S3 Bucket
                                        {data.s3_enabled && <span className="text-red-500 ml-1">*</span>}
                                    </Label>
                                    <Input
                                        id="aws_bucket"
                                        value={data.aws_bucket}
                                        onChange={(e) => setData('aws_bucket', e.target.value)}
                                        placeholder="my-app-bucket"
                                        disabled={!data.s3_enabled}
                                        className={errors.aws_bucket ? 'border-red-500' : ''}
                                    />
                                    {errors.aws_bucket && (
                                        <p className="text-sm text-destructive">{errors.aws_bucket}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        3-63 characters, lowercase letters, numbers, dots, and hyphens only
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="aws_digital_assets_bucket">Digital Assets Bucket</Label>
                                    <Input
                                        id="aws_digital_assets_bucket"
                                        value={data.aws_digital_assets_bucket}
                                        onChange={(e) => setData('aws_digital_assets_bucket', e.target.value)}
                                        placeholder="my-digital-assets-bucket (optional)"
                                        disabled={!data.s3_enabled}
                                    />
                                    {errors.aws_digital_assets_bucket && (
                                        <p className="text-sm text-destructive">{errors.aws_digital_assets_bucket}</p>
                                    )}
                                    <p className="text-xs text-muted-foreground">
                                        Leave empty to use the default bucket for digital assets
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="aws_url">Custom S3 URL (Optional)</Label>
                                    <Input
                                        id="aws_url"
                                        value={data.aws_url}
                                        onChange={(e) => setData('aws_url', e.target.value)}
                                        placeholder="https://s3.amazonaws.com"
                                        disabled={!data.s3_enabled}
                                    />
                                    {errors.aws_url && (
                                        <p className="text-sm text-destructive">{errors.aws_url}</p>
                                    )}
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="aws_endpoint">Custom Endpoint (Optional)</Label>
                                <Input
                                    id="aws_endpoint"
                                    value={data.aws_endpoint}
                                    onChange={(e) => setData('aws_endpoint', e.target.value)}
                                    placeholder="For S3-compatible services like DigitalOcean Spaces"
                                    disabled={!data.s3_enabled}
                                />
                                {errors.aws_endpoint && (
                                    <p className="text-sm text-destructive">{errors.aws_endpoint}</p>
                                )}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="aws_use_path_style_endpoint"
                                    checked={data.aws_use_path_style_endpoint}
                                    onCheckedChange={(checked) => setData('aws_use_path_style_endpoint', checked)}
                                    disabled={!data.s3_enabled}
                                />
                                <Label htmlFor="aws_use_path_style_endpoint">Use Path Style Endpoint</Label>
                                <Info className="h-4 w-4 text-muted-foreground" />
                            </div>
                        </CardContent>
                    </Card>

                    {/* Storage Configuration */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <HardDrive className="h-5 w-5" />
                                Storage Configuration
                            </CardTitle>
                            <CardDescription>
                                Configure how digital assets are stored and managed
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="digital_assets_driver">Digital Assets Storage Driver</Label>
                                <select
                                    id="digital_assets_driver"
                                    value={data.digital_assets_driver}
                                    onChange={(e) => setData('digital_assets_driver', e.target.value)}
                                    className="w-full px-3 py-2 border border-input bg-background rounded-md"
                                >
                                    <option value="local">Local Storage</option>
                                    <option value="s3">Amazon S3</option>
                                </select>
                                {errors.digital_assets_driver && (
                                    <p className="text-sm text-destructive">{errors.digital_assets_driver}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Connection Test */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TestTube className="h-5 w-5" />
                                Connection Test
                            </CardTitle>
                            <CardDescription>
                                Test your S3 configuration to ensure it's working correctly
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={testConnection}
                                disabled={isTestingConnection || !data.s3_enabled}
                                className="w-full sm:w-auto"
                            >
                                {isTestingConnection ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                        Testing Connection...
                                    </>
                                ) : (
                                    <>
                                        <TestTube className="h-4 w-4 mr-2" />
                                        Test S3 Connection
                                    </>
                                )}
                            </Button>

                            {connectionStatus && (
                                <Alert className={connectionStatus.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                                    <div className="flex items-start gap-2">
                                        {connectionStatus.success ? (
                                            <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                                        ) : (
                                            <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
                                        )}
                                        <div className="flex-1">
                                            <AlertDescription className={connectionStatus.success ? 'text-green-800' : 'text-red-800'}>
                                                <div className="font-medium">{connectionStatus.message}</div>
                                                {connectionStatus.details && (
                                                    <div className="mt-2 text-sm opacity-90">
                                                        {connectionStatus.success ? (
                                                            <div>
                                                                <div>Bucket: {connectionStatus.details.bucket}</div>
                                                                <div>Region: {connectionStatus.details.region}</div>
                                                                {connectionStatus.details.request_id && (
                                                                    <div>Request ID: {connectionStatus.details.request_id}</div>
                                                                )}
                                                            </div>
                                                        ) : (
                                                            <div>
                                                                {connectionStatus.details.error_code && (
                                                                    <div>Error Code: {connectionStatus.details.error_code}</div>
                                                                )}
                                                                {connectionStatus.details.request_id && (
                                                                    <div>Request ID: {connectionStatus.details.request_id}</div>
                                                                )}
                                                            </div>
                                                        )}
                                                    </div>
                                                )}
                                            </AlertDescription>
                                        </div>
                                    </div>
                                </Alert>
                            )}
                        </CardContent>
                    </Card>

                    {/* Security Notice */}
                    <Alert>
                        <Shield className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Security Notice:</strong> Your AWS credentials are stored securely and encrypted. 
                            Never share your AWS credentials with unauthorized users. Consider using IAM roles with 
                            minimal required permissions for enhanced security.
                        </AlertDescription>
                    </Alert>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => reset()}
                            disabled={processing}
                        >
                            Reset
                        </Button>
                        <Button type="submit" disabled={processing}>
                            {processing ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Configuration
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </div>
        </SuperAdminLayout>
    );
}
