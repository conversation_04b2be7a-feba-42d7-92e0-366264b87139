import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { DefaultTransactionsSummaries } from '@/components/dashboard/transactions-summary';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { type BreadcrumbItem } from '@/types';
import { DashboardData } from '@/types/dashboard';
import { Head, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Users, 
    Shield, 
    Activity, 
    Database, 
    Server, 
    HardDrive,
    Cpu,
    AlertTriangle,
    CheckCircle,
    XCircle
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Super Admin Dashboard',
        href: '/super-admin/dashboard',
    },
];

interface SystemHealth {
    database: {
        status: string;
        response_time?: string;
        connection: string;
        error?: string;
    };
    storage: {
        status: string;
        usage_percentage: number;
        free_space: string;
        total_space: string;
        used_space: string;
        error?: string;
    };
    cache: {
        status: string;
        driver: string;
        test_result?: string;
        error?: string;
    };
    queue: {
        status: string;
        driver: string;
        connection: string;
        error?: string;
    };
    memory: {
        current: string;
        peak: string;
        limit: string;
        usage_percentage: number;
    };
    disk: {
        free: string;
        total: string;
        used: string;
        usage_percentage: number;
    };
}

interface SuperAdminStats {
    total_users: number;
    active_users: number;
    suspended_users: number;
    total_roles: number;
    total_permissions: number;
    admin_users: number;
    recent_logins: number;
    failed_login_attempts: number;
    locked_accounts: number;
}

interface SuperAdminDashboardProps {
    dashboardData: DashboardData;
    systemHealth: SystemHealth;
    superAdminStats: SuperAdminStats;
}

const getStatusIcon = (status: string) => {
    switch (status) {
        case 'healthy':
            return <CheckCircle className="h-4 w-4 text-green-500" />;
        case 'warning':
            return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
        case 'unhealthy':
            return <XCircle className="h-4 w-4 text-red-500" />;
        default:
            return <Activity className="h-4 w-4 text-gray-500" />;
    }
};

const getStatusBadge = (status: string) => {
    switch (status) {
        case 'healthy':
            return <Badge variant="default" className="bg-green-100 text-green-800">Healthy</Badge>;
        case 'warning':
            return <Badge variant="destructive" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
        case 'unhealthy':
            return <Badge variant="destructive">Unhealthy</Badge>;
        default:
            return <Badge variant="secondary">Unknown</Badge>;
    }
};

export default function SuperAdminDashboard() {
    const { dashboardData, systemHealth, superAdminStats } = usePage<SuperAdminDashboardProps>().props;

    return (
        <SuperAdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Super Admin Dashboard" />

            <div className="space-y-8">
                {/* Header */}
                <div className="border-b border-slate-200 dark:border-slate-700 pb-6">
                    <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
                        Super Admin Dashboard
                    </h1>
                    <p className="mt-2 text-slate-600 dark:text-slate-400">
                        System overview and administrative controls for the platform.
                    </p>
                </div>

                {/* Super Admin Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{superAdminStats.total_users}</div>
                            <p className="text-xs text-muted-foreground">
                                {superAdminStats.active_users} active, {superAdminStats.suspended_users} suspended
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Admin Users</CardTitle>
                            <Shield className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{superAdminStats.admin_users}</div>
                            <p className="text-xs text-muted-foreground">
                                {superAdminStats.total_roles} roles, {superAdminStats.total_permissions} permissions
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
                            <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{superAdminStats.recent_logins}</div>
                            <p className="text-xs text-muted-foreground">
                                Logins in last 7 days
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {superAdminStats.failed_login_attempts + superAdminStats.locked_accounts}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                {superAdminStats.failed_login_attempts} failed logins, {superAdminStats.locked_accounts} locked
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* System Health */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Server className="h-5 w-5" />
                                System Health
                            </CardTitle>
                            <CardDescription>
                                Real-time system status and performance metrics
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Database className="h-4 w-4" />
                                    <span className="text-sm font-medium">Database</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    {getStatusIcon(systemHealth.database.status)}
                                    {getStatusBadge(systemHealth.database.status)}
                                    {systemHealth.database.response_time && (
                                        <span className="text-xs text-muted-foreground">
                                            {systemHealth.database.response_time}
                                        </span>
                                    )}
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <HardDrive className="h-4 w-4" />
                                    <span className="text-sm font-medium">Storage</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    {getStatusIcon(systemHealth.storage.status)}
                                    {getStatusBadge(systemHealth.storage.status)}
                                    <span className="text-xs text-muted-foreground">
                                        {systemHealth.storage.usage_percentage}% used
                                    </span>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Activity className="h-4 w-4" />
                                    <span className="text-sm font-medium">Cache</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    {getStatusIcon(systemHealth.cache.status)}
                                    {getStatusBadge(systemHealth.cache.status)}
                                    <span className="text-xs text-muted-foreground">
                                        {systemHealth.cache.driver}
                                    </span>
                                </div>
                            </div>

                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <Cpu className="h-4 w-4" />
                                    <span className="text-sm font-medium">Queue</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    {getStatusIcon(systemHealth.queue.status)}
                                    {getStatusBadge(systemHealth.queue.status)}
                                    <span className="text-xs text-muted-foreground">
                                        {systemHealth.queue.driver}
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Cpu className="h-5 w-5" />
                                Resource Usage
                            </CardTitle>
                            <CardDescription>
                                Memory and disk usage statistics
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Memory Usage</span>
                                    <span className="text-sm text-muted-foreground">
                                        {systemHealth.memory.current} / {systemHealth.memory.limit}
                                    </span>
                                </div>
                                <Progress value={systemHealth.memory.usage_percentage} className="h-2" />
                                <p className="text-xs text-muted-foreground mt-1">
                                    Peak: {systemHealth.memory.peak}
                                </p>
                            </div>

                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Disk Usage</span>
                                    <span className="text-sm text-muted-foreground">
                                        {systemHealth.disk.used} / {systemHealth.disk.total}
                                    </span>
                                </div>
                                <Progress value={systemHealth.disk.usage_percentage} className="h-2" />
                                <p className="text-xs text-muted-foreground mt-1">
                                    Free: {systemHealth.disk.free}
                                </p>
                            </div>

                            <div>
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium">Storage Usage</span>
                                    <span className="text-sm text-muted-foreground">
                                        {systemHealth.storage.used_space} / {systemHealth.storage.total_space}
                                    </span>
                                </div>
                                <Progress value={systemHealth.storage.usage_percentage} className="h-2" />
                                <p className="text-xs text-muted-foreground mt-1">
                                    Free: {systemHealth.storage.free_space}
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Dashboard Statistics */}
                <DashboardStats data={dashboardData?.stats} />

                {/* Transaction Summaries */}
                <DefaultTransactionsSummaries />
            </div>
        </SuperAdminLayout>
    );
}
