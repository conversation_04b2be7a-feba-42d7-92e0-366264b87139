import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
    Server,
    Database,
    HardDrive,
    Cpu,
    Activity,
    CheckCircle,
    AlertTriangle,
    XCircle
} from 'lucide-react';

interface SystemInfo {
    php_version: string;
    laravel_version: string;
    server_software: string;
    database_version: string;
    memory_limit: string;
    max_execution_time: string;
    upload_max_filesize: string;
    post_max_size: string;
    disk_usage: {
        free: string;
        used: string;
        total: string;
        percentage: number;
        status: string;
    };
    cache_driver: string;
    queue_driver: string;
    mail_driver: string;
}

interface Props {
    systemInfo: SystemInfo;
}

export default function SystemIndex({ systemInfo }: Props) {
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'healthy':
                return <CheckCircle className="h-5 w-5 text-green-500" />;
            case 'warning':
                return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
            case 'error':
                return <XCircle className="h-5 w-5 text-red-500" />;
            default:
                return <Activity className="h-5 w-5 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'healthy':
                return 'bg-green-100 text-green-800';
            case 'warning':
                return 'bg-yellow-100 text-yellow-800';
            case 'error':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <SuperAdminLayout>
            <Head title="System Management" />
            
            <div className="space-y-6">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">System Management</h1>
                    <p className="text-muted-foreground">
                        Monitor and manage system information and health
                    </p>
                </div>

                {/* System Overview */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">PHP Version</CardTitle>
                            <Server className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemInfo.php_version}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Laravel Version</CardTitle>
                            <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemInfo.laravel_version}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Database</CardTitle>
                            <Database className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemInfo.database_version}</div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Memory Limit</CardTitle>
                            <Cpu className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{systemInfo.memory_limit}</div>
                        </CardContent>
                    </Card>
                </div>

                {/* Disk Usage */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <HardDrive className="h-5 w-5" />
                            Disk Usage
                        </CardTitle>
                        <CardDescription>
                            Current disk space utilization
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="space-y-1">
                                <p className="text-sm font-medium">
                                    {systemInfo.disk_usage.used} / {systemInfo.disk_usage.total} used
                                </p>
                                <p className="text-sm text-muted-foreground">
                                    {systemInfo.disk_usage.free} free
                                </p>
                            </div>
                            <Badge className={getStatusColor(systemInfo.disk_usage.status)}>
                                {systemInfo.disk_usage.status}
                            </Badge>
                        </div>
                        <Progress value={systemInfo.disk_usage.percentage} className="w-full" />
                        <p className="text-sm text-muted-foreground">
                            {systemInfo.disk_usage.percentage}% used
                        </p>
                    </CardContent>
                </Card>

                {/* System Configuration */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Server Configuration</CardTitle>
                            <CardDescription>
                                Current server and PHP configuration
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm font-medium">Server Software</p>
                                    <p className="text-sm text-muted-foreground">{systemInfo.server_software}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Max Execution Time</p>
                                    <p className="text-sm text-muted-foreground">{systemInfo.max_execution_time}s</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Upload Max Filesize</p>
                                    <p className="text-sm text-muted-foreground">{systemInfo.upload_max_filesize}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium">Post Max Size</p>
                                    <p className="text-sm text-muted-foreground">{systemInfo.post_max_size}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Service Configuration</CardTitle>
                            <CardDescription>
                                Current service drivers and configuration
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4">
                                <div className="flex items-center justify-between">
                                    <p className="text-sm font-medium">Cache Driver</p>
                                    <Badge variant="outline">{systemInfo.cache_driver}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <p className="text-sm font-medium">Queue Driver</p>
                                    <Badge variant="outline">{systemInfo.queue_driver}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <p className="text-sm font-medium">Mail Driver</p>
                                    <Badge variant="outline">{systemInfo.mail_driver}</Badge>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
