import React from 'react';
import { Head } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Users, 
    TrendingUp, 
    UserPlus,
    UserCheck,
    DollarSign,
    ArrowUpRight,
    ArrowDownRight,
    Clock
} from 'lucide-react';

interface CustomerAnalyticsProps {
    overview: any;
    segmentation: any;
    topCustomers: any[];
    behaviorPatterns: any;
}

export default function CustomerAnalytics({
    overview,
    segmentation,
    topCustomers,
    behaviorPatterns
}: CustomerAnalyticsProps) {
    const getGrowthColor = (growth: number) => {
        if (growth > 0) return 'text-green-600';
        if (growth < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    const getGrowthIcon = (growth: number) => {
        if (growth > 0) return ArrowUpRight;
        if (growth < 0) return ArrowDownRight;
        return null;
    };

    const segmentColors = {
        champions: 'bg-green-500',
        loyal_customers: 'bg-blue-500',
        potential_loyalists: 'bg-purple-500',
        new_customers: 'bg-yellow-500',
        promising: 'bg-indigo-500',
        need_attention: 'bg-orange-500',
        about_to_sleep: 'bg-red-400',
        at_risk: 'bg-red-500',
        cannot_lose: 'bg-red-600',
        hibernating: 'bg-gray-500',
    };

    const segmentLabels = {
        champions: 'Champions',
        loyal_customers: 'Loyal Customers',
        potential_loyalists: 'Potential Loyalists',
        new_customers: 'New Customers',
        promising: 'Promising',
        need_attention: 'Need Attention',
        about_to_sleep: 'About to Sleep',
        at_risk: 'At Risk',
        cannot_lose: 'Cannot Lose',
        hibernating: 'Hibernating',
    };

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

    return (
        <SuperAdminLayout>
            <Head title="Customer Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Customer Analytics</h1>
                        <p className="text-gray-600 mt-2">
                            Understand customer behavior, segmentation, and lifetime value
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Users className="h-8 w-8 text-blue-600" />
                    </div>
                </div>

                {/* Overview Cards */}
                {overview && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <UserPlus className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">New Customers</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.new_customers?.toLocaleString() || 0}
                                        </p>
                                        {overview.growth?.new_customers && (
                                            <div className="flex items-center gap-1 mt-1">
                                                {React.createElement(getGrowthIcon(overview.growth.new_customers), {
                                                    className: `h-3 w-3 ${getGrowthColor(overview.growth.new_customers)}`
                                                })}
                                                <span className={`text-xs ${getGrowthColor(overview.growth.new_customers)}`}>
                                                    {overview.growth.new_customers >= 0 ? '+' : ''}{overview.growth.new_customers.toFixed(1)}%
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <UserCheck className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Active Customers</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.active_customers?.toLocaleString() || 0}
                                        </p>
                                        {overview.growth?.active_customers && (
                                            <div className="flex items-center gap-1 mt-1">
                                                {React.createElement(getGrowthIcon(overview.growth.active_customers), {
                                                    className: `h-3 w-3 ${getGrowthColor(overview.growth.active_customers)}`
                                                })}
                                                <span className={`text-xs ${getGrowthColor(overview.growth.active_customers)}`}>
                                                    {overview.growth.active_customers >= 0 ? '+' : ''}{overview.growth.active_customers.toFixed(1)}%
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <DollarSign className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Avg. Lifetime Value</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            ${overview.current?.avg_lifetime_value?.toFixed(2) || '0.00'}
                                        </p>
                                        {overview.growth?.avg_lifetime_value && (
                                            <div className="flex items-center gap-1 mt-1">
                                                {React.createElement(getGrowthIcon(overview.growth.avg_lifetime_value), {
                                                    className: `h-3 w-3 ${getGrowthColor(overview.growth.avg_lifetime_value)}`
                                                })}
                                                <span className={`text-xs ${getGrowthColor(overview.growth.avg_lifetime_value)}`}>
                                                    {overview.growth.avg_lifetime_value >= 0 ? '+' : ''}{overview.growth.avg_lifetime_value.toFixed(1)}%
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-orange-100 rounded-lg">
                                        <TrendingUp className="h-5 w-5 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Retention Rate</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.retention_rate?.toFixed(1) || 0}%
                                        </p>
                                        {overview.growth?.retention_rate && (
                                            <div className="flex items-center gap-1 mt-1">
                                                {React.createElement(getGrowthIcon(overview.growth.retention_rate), {
                                                    className: `h-3 w-3 ${getGrowthColor(overview.growth.retention_rate)}`
                                                })}
                                                <span className={`text-xs ${getGrowthColor(overview.growth.retention_rate)}`}>
                                                    {overview.growth.retention_rate >= 0 ? '+' : ''}{overview.growth.retention_rate.toFixed(1)}%
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Customer Segmentation */}
                {segmentation && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Customer Segmentation (RFM Analysis)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                                {Object.entries(segmentation).map(([segment, count]) => (
                                    <div key={segment} className="text-center">
                                        <div className={`w-16 h-16 ${segmentColors[segment]} rounded-lg mx-auto mb-2 flex items-center justify-center text-white font-bold text-lg`}>
                                            {count}
                                        </div>
                                        <p className="text-sm font-medium text-gray-900">
                                            {segmentLabels[segment]}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Top Customers and Behavior Patterns */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Top Customers */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Top Customers by Revenue
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {topCustomers && topCustomers.length > 0 ? (
                                <div className="space-y-4">
                                    {topCustomers.slice(0, 8).map((customer, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                                                    {customer.customer?.name?.charAt(0) || '?'}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        {customer.customer?.name || 'Unknown Customer'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        {customer.secondary_metric || 0} orders
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    ${customer.metric_value?.toFixed(2) || '0.00'}
                                                </p>
                                                <p className="text-sm text-gray-600">
                                                    {customer.metric_label}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No customer data available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Purchase Patterns */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="h-5 w-5" />
                                Purchase Patterns
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {behaviorPatterns ? (
                                <div className="space-y-6">
                                    {/* Weekly Pattern */}
                                    {behaviorPatterns.weekly_purchases && (
                                        <div>
                                            <h4 className="font-medium text-gray-900 mb-3">Orders by Day of Week</h4>
                                            <div className="space-y-2">
                                                {behaviorPatterns.weekly_purchases.map((day, index) => {
                                                    const maxOrders = Math.max(...behaviorPatterns.weekly_purchases.map(d => d.orders));
                                                    const percentage = maxOrders > 0 ? (day.orders / maxOrders) * 100 : 0;
                                                    
                                                    return (
                                                        <div key={index} className="space-y-1">
                                                            <div className="flex items-center justify-between text-sm">
                                                                <span className="text-gray-600">{day.day_name}</span>
                                                                <span className="font-medium">{day.orders}</span>
                                                            </div>
                                                            <Progress value={percentage} className="h-2" />
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    )}

                                    {/* Average Time Between Orders */}
                                    {behaviorPatterns.avg_days_between_orders && (
                                        <div className="text-center p-4 bg-gray-50 rounded-lg">
                                            <p className="text-2xl font-bold text-gray-900">
                                                {behaviorPatterns.avg_days_between_orders}
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                Average days between orders
                                            </p>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No behavior pattern data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Additional Metrics */}
                {overview && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <Card>
                            <CardContent className="p-6 text-center">
                                <p className="text-2xl font-bold text-gray-900">
                                    {overview.current?.churn_rate?.toFixed(1) || 0}%
                                </p>
                                <p className="text-sm text-gray-600">Churn Rate</p>
                                {overview.growth?.churn_rate && (
                                    <p className={`text-xs mt-1 ${getGrowthColor(-overview.growth.churn_rate)}`}>
                                        {overview.growth.churn_rate <= 0 ? '+' : ''}{(-overview.growth.churn_rate).toFixed(1)}%
                                    </p>
                                )}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6 text-center">
                                <p className="text-2xl font-bold text-gray-900">
                                    {overview.current?.returning_customers?.toLocaleString() || 0}
                                </p>
                                <p className="text-sm text-gray-600">Returning Customers</p>
                                {overview.growth?.returning_customers && (
                                    <p className={`text-xs mt-1 ${getGrowthColor(overview.growth.returning_customers)}`}>
                                        {overview.growth.returning_customers >= 0 ? '+' : ''}{overview.growth.returning_customers.toFixed(1)}%
                                    </p>
                                )}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="p-6 text-center">
                                <p className="text-2xl font-bold text-gray-900">
                                    {overview.current?.total_customers?.toLocaleString() || 0}
                                </p>
                                <p className="text-sm text-gray-600">Total Customers</p>
                                {overview.growth?.total_customers && (
                                    <p className={`text-xs mt-1 ${getGrowthColor(overview.growth.total_customers)}`}>
                                        {overview.growth.total_customers >= 0 ? '+' : ''}{overview.growth.total_customers.toFixed(1)}%
                                    </p>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                )}
            </div>
        </SuperAdminLayout>
    );
}
