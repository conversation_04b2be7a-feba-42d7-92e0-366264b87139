import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
    Package, 
    TrendingUp, 
    Eye,
    ShoppingCart,
    Download,
    Calendar,
    RefreshCw,
    Star
} from 'lucide-react';

interface ProductPerformanceProps {
    topProducts: any[];
    summary: any;
    days: number;
    metric: string;
    availableMetrics: Record<string, string>;
}

export default function ProductPerformance({
    topProducts,
    summary,
    days,
    metric,
    availableMetrics
}: ProductPerformanceProps) {
    const [selectedPeriod, setSelectedPeriod] = useState(days.toString());
    const [selectedMetric, setSelectedMetric] = useState(metric);
    const [isLoading, setIsLoading] = useState(false);

    const handlePeriodChange = (newPeriod: string) => {
        setSelectedPeriod(newPeriod);
        setIsLoading(true);
        
        window.location.href = route('super-admin.analytics.products', { 
            days: newPeriod, 
            metric: selectedMetric 
        });
    };

    const handleMetricChange = (newMetric: string) => {
        setSelectedMetric(newMetric);
        setIsLoading(true);
        
        window.location.href = route('super-admin.analytics.products', { 
            days: selectedPeriod, 
            metric: newMetric 
        });
    };

    const handleRefresh = () => {
        setIsLoading(true);
        window.location.reload();
    };

    const getMetricIcon = (metricType: string) => {
        switch (metricType) {
            case 'views':
                return Eye;
            case 'sales':
                return ShoppingCart;
            case 'downloads':
                return Download;
            case 'revenue':
                return TrendingUp;
            default:
                return Package;
        }
    };

    const formatMetricValue = (value: number, metricType: string) => {
        switch (metricType) {
            case 'revenue':
                return `$${value.toFixed(2)}`;
            case 'conversion':
                return `${value.toFixed(2)}%`;
            default:
                return value.toLocaleString();
        }
    };

    return (
        <SuperAdminLayout>
            <Head title="Product Performance Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Product Performance</h1>
                        <p className="text-gray-600 mt-2">
                            Monitor product views, conversions, and performance metrics
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Select value={selectedMetric} onValueChange={handleMetricChange}>
                            <SelectTrigger className="w-40">
                                <Package className="h-4 w-4 mr-2" />
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                {Object.entries(availableMetrics).map(([key, label]) => (
                                    <SelectItem key={key} value={key}>{label}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        
                        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                            <SelectTrigger className="w-40">
                                <Calendar className="h-4 w-4 mr-2" />
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="7">Last 7 days</SelectItem>
                                <SelectItem value="30">Last 30 days</SelectItem>
                                <SelectItem value="90">Last 90 days</SelectItem>
                                <SelectItem value="365">Last year</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                {summary && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <Eye className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Views</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {summary.total_views?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <ShoppingCart className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Sales</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {summary.total_sales?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <TrendingUp className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Revenue</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            ${summary.total_revenue?.toFixed(2) || '0.00'}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-orange-100 rounded-lg">
                                        <Package className="h-5 w-5 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Conversion Rate</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {summary.avg_conversion_rate?.toFixed(2) || 0}%
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Top Performing Products */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            {React.createElement(getMetricIcon(selectedMetric), { className: "h-5 w-5" })}
                            Top Performing Products by {availableMetrics[selectedMetric]}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {topProducts && topProducts.length > 0 ? (
                            <div className="space-y-4">
                                {topProducts.map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                                        <div className="flex items-center gap-4">
                                            <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-sm font-bold">
                                                {index + 1}
                                            </div>
                                            
                                            <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                                                {item.product?.image ? (
                                                    <img
                                                        src={`/storage/${item.product.image}`}
                                                        alt={item.product.name}
                                                        className="w-full h-full object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                        <Package className="h-6 w-6 text-gray-400" />
                                                    </div>
                                                )}
                                            </div>
                                            
                                            <div className="flex-1">
                                                <h3 className="font-medium text-gray-900">
                                                    {item.product?.name || 'Unknown Product'}
                                                </h3>
                                                <p className="text-sm text-gray-600">
                                                    SKU: {item.product?.sku || 'N/A'}
                                                </p>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <Badge variant="secondary" className="text-xs">
                                                        ${item.product?.price || '0.00'}
                                                    </Badge>
                                                    {item.product?.average_rating && (
                                                        <div className="flex items-center gap-1">
                                                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                                            <span className="text-xs text-gray-600">
                                                                {item.product.average_rating.toFixed(1)}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div className="text-right">
                                            <p className="text-lg font-bold text-gray-900">
                                                {formatMetricValue(item.metric_value, selectedMetric)}
                                            </p>
                                            <p className="text-sm text-gray-600">
                                                {item.metric_label}
                                            </p>
                                            {item.secondary_metric && (
                                                <p className="text-xs text-gray-500 mt-1">
                                                    {formatMetricValue(item.secondary_metric, 'number')} {item.secondary_label}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-12">
                                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    No Product Data Available
                                </h3>
                                <p className="text-gray-600">
                                    No product performance data found for the selected period and metric.
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Performance Insights */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Performance Insights</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Top Product</span>
                                    <span className="font-medium">
                                        {summary?.top_product || 'N/A'}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Top Product Sales</span>
                                    <span className="font-medium">
                                        {summary?.top_product_sales?.toLocaleString() || 0}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-gray-600">Average Conversion</span>
                                    <span className="font-medium">
                                        {summary?.avg_conversion_rate?.toFixed(2) || 0}%
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <Button variant="outline" className="w-full justify-start">
                                    <Package className="h-4 w-4 mr-2" />
                                    View All Products
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <TrendingUp className="h-4 w-4 mr-2" />
                                    Product Comparison
                                </Button>
                                <Button variant="outline" className="w-full justify-start">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
