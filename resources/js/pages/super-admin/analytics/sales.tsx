import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { SalesOverview } from '@/components/analytics/sales-overview';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
    BarChart3, 
    TrendingUp, 
    Download,
    Calendar,
    RefreshCw
} from 'lucide-react';

interface SalesAnalyticsProps {
    overview: any;
    dailyTrends: any[];
    topProducts: any[];
    revenueByCategory: any[];
    customerAnalytics: any;
    orderStatusDistribution: any[];
    paymentMethods: any[];
    days: number;
}

export default function SalesAnalytics({
    overview,
    dailyTrends,
    topProducts,
    revenueByCategory,
    customerAnalytics,
    orderStatusDistribution,
    paymentMethods,
    days
}: SalesAnalyticsProps) {
    const [selectedPeriod, setSelectedPeriod] = useState(days.toString());
    const [isLoading, setIsLoading] = useState(false);

    const handlePeriodChange = (newPeriod: string) => {
        setSelectedPeriod(newPeriod);
        setIsLoading(true);
        
        // Redirect with new period
        window.location.href = route('super-admin.analytics.sales', { days: newPeriod });
    };

    const handleRefresh = () => {
        setIsLoading(true);
        window.location.reload();
    };

    const handleExport = () => {
        window.open(route('super-admin.analytics.sales.export', { days: selectedPeriod }), '_blank');
    };

    return (
        <SuperAdminLayout>
            <Head title="Sales Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Sales Analytics</h1>
                        <p className="text-gray-600 mt-2">
                            Track revenue, orders, and sales performance with detailed insights
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                            <SelectTrigger className="w-40">
                                <Calendar className="h-4 w-4 mr-2" />
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="7">Last 7 days</SelectItem>
                                <SelectItem value="30">Last 30 days</SelectItem>
                                <SelectItem value="90">Last 90 days</SelectItem>
                                <SelectItem value="365">Last year</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                        
                        <Button onClick={handleExport}>
                            <Download className="h-4 w-4 mr-2" />
                            Export
                        </Button>
                    </div>
                </div>

                {/* Sales Overview */}
                {overview && <SalesOverview data={overview} />}

                {/* Charts and Additional Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Top Products */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5" />
                                Top Selling Products
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {topProducts && topProducts.length > 0 ? (
                                <div className="space-y-4">
                                    {topProducts.slice(0, 5).map((item, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center text-sm font-medium">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        {item.product?.name || 'Unknown Product'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        {item.total_sold} units sold
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    ${item.total_revenue?.toFixed(2) || '0.00'}
                                                </p>
                                                <p className="text-sm text-gray-600">
                                                    {item.order_count} orders
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No sales data available for this period
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Revenue by Category */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Revenue by Category
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {revenueByCategory && revenueByCategory.length > 0 ? (
                                <div className="space-y-4">
                                    {revenueByCategory.slice(0, 5).map((category, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div>
                                                <p className="font-medium text-gray-900">
                                                    {category.category_name || 'Uncategorized'}
                                                </p>
                                                <p className="text-sm text-gray-600">
                                                    {category.quantity_sold} units sold
                                                </p>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    ${category.revenue?.toFixed(2) || '0.00'}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No category data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Customer Analytics */}
                {customerAnalytics && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Customer Analytics</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {customerAnalytics.total_customers || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Total Customers</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {customerAnalytics.new_customers || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">New Customers</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {customerAnalytics.returning_customers || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Returning Customers</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        ${customerAnalytics.customer_ltv?.toFixed(2) || '0.00'}
                                    </p>
                                    <p className="text-sm text-gray-600">Avg. Lifetime Value</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Order Status & Payment Methods */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Order Status Distribution */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Order Status Distribution</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {orderStatusDistribution && orderStatusDistribution.length > 0 ? (
                                <div className="space-y-3">
                                    {orderStatusDistribution.map((status, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <div className={`w-3 h-3 rounded-full ${
                                                    status.status === 'completed' ? 'bg-green-500' :
                                                    status.status === 'pending' ? 'bg-yellow-500' :
                                                    status.status === 'cancelled' ? 'bg-red-500' :
                                                    'bg-gray-500'
                                                }`} />
                                                <span className="capitalize text-gray-900">
                                                    {status.status}
                                                </span>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium">{status.count}</p>
                                                <p className="text-sm text-gray-600">
                                                    ${status.revenue?.toFixed(2) || '0.00'}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No order status data available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Payment Methods */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Methods</CardTitle>
                        </CardHeader>
                        <CardContent>
                            {paymentMethods && paymentMethods.length > 0 ? (
                                <div className="space-y-3">
                                    {paymentMethods.map((method, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <span className="text-gray-900">
                                                {method.payment_method || 'Unknown'}
                                            </span>
                                            <div className="text-right">
                                                <p className="font-medium">{method.count}</p>
                                                <p className="text-sm text-gray-600">
                                                    ${method.revenue?.toFixed(2) || '0.00'}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No payment method data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </SuperAdminLayout>
    );
}
