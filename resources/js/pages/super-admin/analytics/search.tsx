import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Search, 
    TrendingUp, 
    Users,
    MousePointer,
    AlertTriangle,
    Calendar,
    RefreshCw,
    Eye,
    Target
} from 'lucide-react';

interface SearchAnalyticsProps {
    metrics: any;
    popularQueries: any[];
    noResultQueries: any[];
    trends: any[];
    clickThroughRates: any;
    mostClickedProducts: any[];
    filterUsage: any;
    days: number;
}

export default function SearchAnalytics({
    metrics,
    popularQueries,
    noResultQueries,
    trends,
    clickThroughRates,
    mostClickedProducts,
    filterUsage,
    days
}: SearchAnalyticsProps) {
    const [selectedPeriod, setSelectedPeriod] = useState(days.toString());
    const [isLoading, setIsLoading] = useState(false);

    const handlePeriodChange = (newPeriod: string) => {
        setSelectedPeriod(newPeriod);
        setIsLoading(true);
        
        window.location.href = route('super-admin.analytics.search', { days: newPeriod });
    };

    const handleRefresh = () => {
        setIsLoading(true);
        window.location.reload();
    };

    return (
        <SuperAdminLayout>
            <Head title="Search Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Search Analytics</h1>
                        <p className="text-gray-600 mt-2">
                            Monitor search queries, popular terms, and search performance
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                            <SelectTrigger className="w-40">
                                <Calendar className="h-4 w-4 mr-2" />
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="7">Last 7 days</SelectItem>
                                <SelectItem value="30">Last 30 days</SelectItem>
                                <SelectItem value="90">Last 90 days</SelectItem>
                                <SelectItem value="365">Last year</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                    </div>
                </div>

                {/* Overview Cards */}
                {metrics && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <Search className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Searches</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {metrics.total_searches?.toLocaleString() || 0}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <Target className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Success Rate</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {metrics.success_rate?.toFixed(1) || 0}%
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <MousePointer className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Click-Through Rate</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {clickThroughRates?.click_through_rate?.toFixed(1) || 0}%
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-orange-100 rounded-lg">
                                        <Eye className="h-5 w-5 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Avg. Results</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {metrics.avg_result_count?.toFixed(1) || 0}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Popular Queries and No Results */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Popular Search Queries */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <TrendingUp className="h-5 w-5" />
                                Popular Search Queries
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {popularQueries && popularQueries.length > 0 ? (
                                <div className="space-y-4">
                                    {popularQueries.slice(0, 10).map((query, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center text-xs font-medium">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        "{query.normalized_query || 'Unknown Query'}"
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        Avg. {query.avg_results?.toFixed(1) || 0} results
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    {query.search_count?.toLocaleString() || 0}
                                                </p>
                                                <p className="text-sm text-gray-600">searches</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No search query data available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* No Results Queries */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-red-500" />
                                Queries with No Results
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {noResultQueries && noResultQueries.length > 0 ? (
                                <div className="space-y-4">
                                    {noResultQueries.slice(0, 10).map((query, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-6 h-6 bg-red-100 rounded flex items-center justify-center text-xs font-medium text-red-600">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        "{query.normalized_query || 'Unknown Query'}"
                                                    </p>
                                                    <p className="text-sm text-red-600">
                                                        No results found
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <Badge variant="destructive">
                                                    {query.search_count?.toLocaleString() || 0}
                                                </Badge>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No failed search data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Most Clicked Products and Filter Usage */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Most Clicked Products from Search */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <MousePointer className="h-5 w-5" />
                                Most Clicked Products
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {mostClickedProducts && mostClickedProducts.length > 0 ? (
                                <div className="space-y-4">
                                    {mostClickedProducts.slice(0, 8).map((item, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center text-xs font-medium">
                                                    {index + 1}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        {item.product?.name || 'Unknown Product'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        ${item.product?.price || '0.00'}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    {item.click_count?.toLocaleString() || 0}
                                                </p>
                                                <p className="text-sm text-gray-600">clicks</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No click data available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Filter Usage */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Target className="h-5 w-5" />
                                Filter Usage
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {filterUsage && Object.keys(filterUsage).length > 0 ? (
                                <div className="space-y-4">
                                    {Object.entries(filterUsage)
                                        .sort(([,a], [,b]) => b - a)
                                        .slice(0, 8)
                                        .map(([filter, count], index) => {
                                            const maxUsage = Math.max(...Object.values(filterUsage));
                                            const percentage = maxUsage > 0 ? (count / maxUsage) * 100 : 0;
                                            
                                            return (
                                                <div key={index} className="space-y-2">
                                                    <div className="flex items-center justify-between">
                                                        <span className="text-sm font-medium text-gray-900 capitalize">
                                                            {filter.replace('_', ' ')}
                                                        </span>
                                                        <span className="text-sm text-gray-600">
                                                            {count?.toLocaleString() || 0}
                                                        </span>
                                                    </div>
                                                    <Progress value={percentage} className="h-2" />
                                                </div>
                                            );
                                        })}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No filter usage data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Search Performance Metrics */}
                {clickThroughRates && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Search Performance Metrics</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {clickThroughRates.total_searches?.toLocaleString() || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Total Searches</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {clickThroughRates.clicked_searches?.toLocaleString() || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Clicked Searches</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {clickThroughRates.click_through_rate?.toFixed(1) || 0}%
                                    </p>
                                    <p className="text-sm text-gray-600">Click-Through Rate</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {clickThroughRates.avg_click_position?.toFixed(1) || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Avg. Click Position</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Search Insights */}
                <Card>
                    <CardHeader>
                        <CardTitle>Search Insights & Recommendations</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 className="font-medium text-gray-900 mb-3">Optimization Opportunities:</h4>
                                <ul className="space-y-2 text-sm text-gray-600">
                                    <li className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                        Review queries with no results for content gaps
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                                        Optimize popular search terms for better ranking
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-purple-500 rounded-full" />
                                        Improve product descriptions for better search matching
                                    </li>
                                    <li className="flex items-center gap-2">
                                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                                        Consider adding synonyms for common search terms
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium text-gray-900 mb-3">Performance Indicators:</h4>
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Search Success Rate</span>
                                        <Badge variant={metrics?.success_rate > 80 ? "default" : "destructive"}>
                                            {metrics?.success_rate > 80 ? "Good" : "Needs Improvement"}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Click-Through Rate</span>
                                        <Badge variant={clickThroughRates?.click_through_rate > 20 ? "default" : "secondary"}>
                                            {clickThroughRates?.click_through_rate > 20 ? "Good" : "Average"}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm text-gray-600">Average Results per Query</span>
                                        <Badge variant="outline">
                                            {metrics?.avg_result_count?.toFixed(1) || 0} results
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SuperAdminLayout>
    );
}
