import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
    Download, 
    TrendingUp, 
    Users,
    FileText,
    AlertTriangle,
    Calendar,
    RefreshCw,
    HardDrive,
    CheckCircle,
    XCircle
} from 'lucide-react';

interface DownloadAnalyticsProps {
    overview: any;
    dailyTrends: any[];
    mostDownloaded: any[];
    fileTypes: any[];
    topUsers: any[];
    failureAnalysis: any;
    bandwidthUsage: any;
    days: number;
}

export default function DownloadAnalytics({
    overview,
    dailyTrends,
    mostDownloaded,
    fileTypes,
    topUsers,
    failureAnalysis,
    bandwidthUsage,
    days
}: DownloadAnalyticsProps) {
    const [selectedPeriod, setSelectedPeriod] = useState(days.toString());
    const [isLoading, setIsLoading] = useState(false);

    const handlePeriodChange = (newPeriod: string) => {
        setSelectedPeriod(newPeriod);
        setIsLoading(true);
        
        window.location.href = route('super-admin.analytics.downloads', { days: newPeriod });
    };

    const handleRefresh = () => {
        setIsLoading(true);
        window.location.reload();
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <SuperAdminLayout>
            <Head title="Download Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Download Analytics</h1>
                        <p className="text-gray-600 mt-2">
                            Analyze download patterns, success rates, and bandwidth usage
                        </p>
                    </div>
                    <div className="flex items-center gap-3">
                        <Select value={selectedPeriod} onValueChange={handlePeriodChange}>
                            <SelectTrigger className="w-40">
                                <Calendar className="h-4 w-4 mr-2" />
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="7">Last 7 days</SelectItem>
                                <SelectItem value="30">Last 30 days</SelectItem>
                                <SelectItem value="90">Last 90 days</SelectItem>
                                <SelectItem value="365">Last year</SelectItem>
                            </SelectContent>
                        </Select>
                        
                        <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
                            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                            Refresh
                        </Button>
                    </div>
                </div>

                {/* Overview Cards */}
                {overview && (
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <Download className="h-5 w-5 text-blue-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Total Downloads</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.total_downloads?.toLocaleString() || 0}
                                        </p>
                                        {overview.growth?.total_downloads && (
                                            <p className={`text-xs ${overview.growth.total_downloads >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                {overview.growth.total_downloads >= 0 ? '+' : ''}{overview.growth.total_downloads.toFixed(1)}%
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-green-100 rounded-lg">
                                        <CheckCircle className="h-5 w-5 text-green-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Success Rate</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.success_rate?.toFixed(1) || 0}%
                                        </p>
                                        {overview.growth?.success_rate && (
                                            <p className={`text-xs ${overview.growth.success_rate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                {overview.growth.success_rate >= 0 ? '+' : ''}{overview.growth.success_rate.toFixed(1)}%
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-purple-100 rounded-lg">
                                        <Users className="h-5 w-5 text-purple-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Unique Users</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.unique_users?.toLocaleString() || 0}
                                        </p>
                                        {overview.growth?.unique_users && (
                                            <p className={`text-xs ${overview.growth.unique_users >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                {overview.growth.unique_users >= 0 ? '+' : ''}{overview.growth.unique_users.toFixed(1)}%
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center gap-3">
                                    <div className="p-2 bg-orange-100 rounded-lg">
                                        <HardDrive className="h-5 w-5 text-orange-600" />
                                    </div>
                                    <div>
                                        <p className="text-sm text-gray-600">Bandwidth Used</p>
                                        <p className="text-xl font-bold text-gray-900">
                                            {overview.current?.total_bandwidth_mb?.toFixed(1) || 0} MB
                                        </p>
                                        {overview.growth?.total_bandwidth_mb && (
                                            <p className={`text-xs ${overview.growth.total_bandwidth_mb >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                {overview.growth.total_bandwidth_mb >= 0 ? '+' : ''}{overview.growth.total_bandwidth_mb.toFixed(1)}%
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Most Downloaded Files */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <TrendingUp className="h-5 w-5" />
                            Most Downloaded Files
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {mostDownloaded && mostDownloaded.length > 0 ? (
                            <div className="space-y-4">
                                {mostDownloaded.slice(0, 10).map((item, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                        <div className="flex items-center gap-4">
                                            <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center text-sm font-bold">
                                                {index + 1}
                                            </div>
                                            <div>
                                                <h3 className="font-medium text-gray-900">
                                                    {item.digital_asset?.filename || 'Unknown File'}
                                                </h3>
                                                <p className="text-sm text-gray-600">
                                                    {item.product?.name || 'Unknown Product'}
                                                </p>
                                                <div className="flex items-center gap-2 mt-1">
                                                    <Badge variant="secondary" className="text-xs">
                                                        {item.digital_asset?.file_type || 'Unknown'}
                                                    </Badge>
                                                    {item.digital_asset?.file_size && (
                                                        <Badge variant="outline" className="text-xs">
                                                            {formatFileSize(item.digital_asset.file_size)}
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-lg font-bold text-gray-900">
                                                {item.download_count?.toLocaleString() || 0}
                                            </p>
                                            <p className="text-sm text-gray-600">downloads</p>
                                            <p className="text-xs text-gray-500">
                                                {item.success_rate?.toFixed(1) || 0}% success rate
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8 text-gray-500">
                                No download data available for this period
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* File Types and Top Users */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Downloads by File Type */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Downloads by File Type
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {fileTypes && fileTypes.length > 0 ? (
                                <div className="space-y-4">
                                    {fileTypes.slice(0, 8).map((type, index) => (
                                        <div key={index} className="space-y-2">
                                            <div className="flex items-center justify-between">
                                                <span className="text-sm font-medium text-gray-900">
                                                    {type.file_type?.toUpperCase() || 'Unknown'}
                                                </span>
                                                <span className="text-sm text-gray-600">
                                                    {type.download_count?.toLocaleString() || 0}
                                                </span>
                                            </div>
                                            <Progress 
                                                value={type.success_rate || 0} 
                                                className="h-2"
                                            />
                                            <div className="flex items-center justify-between text-xs text-gray-500">
                                                <span>{type.success_rate?.toFixed(1) || 0}% success rate</span>
                                                <span>{type.total_size_mb?.toFixed(1) || 0} MB total</span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No file type data available
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Top Downloading Users */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5" />
                                Top Downloading Users
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {topUsers && topUsers.length > 0 ? (
                                <div className="space-y-4">
                                    {topUsers.slice(0, 8).map((user, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">
                                                    {user.user?.name?.charAt(0) || '?'}
                                                </div>
                                                <div>
                                                    <p className="font-medium text-gray-900">
                                                        {user.user?.name || 'Unknown User'}
                                                    </p>
                                                    <p className="text-sm text-gray-600">
                                                        {user.unique_files || 0} unique files
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium text-gray-900">
                                                    {user.download_count?.toLocaleString() || 0}
                                                </p>
                                                <p className="text-sm text-gray-600">
                                                    {user.success_rate?.toFixed(1) || 0}% success
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No user data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Bandwidth Usage */}
                {bandwidthUsage && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <HardDrive className="h-5 w-5" />
                                Bandwidth Usage
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {bandwidthUsage.total_gb?.toFixed(2) || 0} GB
                                    </p>
                                    <p className="text-sm text-gray-600">Total Bandwidth</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {bandwidthUsage.avg_daily_mb?.toFixed(1) || 0} MB
                                    </p>
                                    <p className="text-sm text-gray-600">Daily Average</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {bandwidthUsage.total_mb?.toFixed(1) || 0} MB
                                    </p>
                                    <p className="text-sm text-gray-600">Total MB</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-gray-900">
                                        {bandwidthUsage.daily_usage?.length || 0}
                                    </p>
                                    <p className="text-sm text-gray-600">Active Days</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Failure Analysis */}
                {failureAnalysis && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="h-5 w-5 text-red-500" />
                                Download Failures
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {failureAnalysis.failure_reasons && failureAnalysis.failure_reasons.length > 0 ? (
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900">Common Failure Reasons:</h4>
                                    {failureAnalysis.failure_reasons.slice(0, 5).map((reason, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <span className="text-gray-900">
                                                {reason.failure_reason || 'Unknown Error'}
                                            </span>
                                            <Badge variant="destructive">
                                                {reason.count?.toLocaleString() || 0}
                                            </Badge>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8 text-gray-500">
                                    No failure data available
                                </div>
                            )}
                        </CardContent>
                    </Card>
                )}
            </div>
        </SuperAdminLayout>
    );
}
