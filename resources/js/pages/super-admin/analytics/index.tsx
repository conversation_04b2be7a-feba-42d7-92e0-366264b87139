import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
    BarChart3, 
    TrendingUp, 
    Package, 
    Download, 
    Users, 
    Search,
    ShoppingCart,
    ArrowRight
} from 'lucide-react';

export default function AnalyticsIndex() {
    const analyticsModules = [
        {
            title: 'Sales Analytics',
            description: 'Track revenue, orders, and sales performance with detailed insights and forecasting.',
            icon: ShoppingCart,
            href: route('super-admin.analytics.sales'),
            color: 'bg-blue-500',
            features: [
                'Revenue tracking and trends',
                'Order analysis and forecasting',
                'Customer acquisition metrics',
                'Payment method analytics'
            ]
        },
        {
            title: 'Product Performance',
            description: 'Monitor product views, conversions, and performance metrics across your catalog.',
            icon: Package,
            href: route('super-admin.analytics.products'),
            color: 'bg-green-500',
            features: [
                'Product views and conversion rates',
                'Top performing products',
                'Category performance analysis',
                'Conversion funnel tracking'
            ]
        },
        {
            title: 'Download Statistics',
            description: 'Analyze download patterns, success rates, and bandwidth usage for digital assets.',
            icon: Download,
            href: route('super-admin.analytics.downloads'),
            color: 'bg-purple-500',
            features: [
                'Download success rates',
                'Bandwidth usage tracking',
                'File performance metrics',
                'User download patterns'
            ]
        },
        {
            title: 'Customer Analytics',
            description: 'Understand customer behavior, segmentation, and lifetime value analysis.',
            icon: Users,
            href: route('super-admin.analytics.customers'),
            color: 'bg-orange-500',
            features: [
                'Customer segmentation',
                'Lifetime value analysis',
                'Retention and churn metrics',
                'Behavior pattern analysis'
            ]
        },
        {
            title: 'Search Analytics',
            description: 'Monitor search queries, popular terms, and search performance optimization.',
            icon: Search,
            href: route('super-admin.analytics.search'),
            color: 'bg-indigo-500',
            features: [
                'Search query analysis',
                'Popular searches and trends',
                'Search performance metrics',
                'No-result query tracking'
            ]
        }
    ];

    return (
        <SuperAdminLayout>
            <Head title="Analytics Dashboard" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
                        <p className="text-gray-600 mt-2">
                            Comprehensive insights and analytics for your digital download center
                        </p>
                    </div>
                    <div className="flex items-center gap-2">
                        <BarChart3 className="h-8 w-8 text-blue-600" />
                    </div>
                </div>

                {/* Analytics Modules Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {analyticsModules.map((module, index) => {
                        const Icon = module.icon;
                        
                        return (
                            <Card key={index} className="group hover:shadow-lg transition-shadow duration-200">
                                <CardHeader className="pb-4">
                                    <div className="flex items-center gap-3">
                                        <div className={`p-3 rounded-lg ${module.color} text-white`}>
                                            <Icon className="h-6 w-6" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{module.title}</CardTitle>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <p className="text-gray-600 text-sm">
                                        {module.description}
                                    </p>
                                    
                                    {/* Features List */}
                                    <div className="space-y-2">
                                        <h4 className="text-sm font-medium text-gray-700">Key Features:</h4>
                                        <ul className="space-y-1">
                                            {module.features.map((feature, featureIndex) => (
                                                <li key={featureIndex} className="text-xs text-gray-600 flex items-center gap-2">
                                                    <div className="w-1 h-1 bg-gray-400 rounded-full" />
                                                    {feature}
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                    
                                    {/* Action Button */}
                                    <Link href={module.href}>
                                        <Button className="w-full group-hover:bg-primary/90 transition-colors">
                                            View Analytics
                                            <ArrowRight className="h-4 w-4 ml-2" />
                                        </Button>
                                    </Link>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Quick Stats Overview */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-blue-100 rounded-lg">
                                    <TrendingUp className="h-5 w-5 text-blue-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Total Revenue</p>
                                    <p className="text-xl font-bold text-gray-900">$0.00</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-green-100 rounded-lg">
                                    <Package className="h-5 w-5 text-green-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Products Sold</p>
                                    <p className="text-xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <Download className="h-5 w-5 text-purple-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Total Downloads</p>
                                    <p className="text-xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardContent className="p-6">
                            <div className="flex items-center gap-3">
                                <div className="p-2 bg-orange-100 rounded-lg">
                                    <Users className="h-5 w-5 text-orange-600" />
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Active Customers</p>
                                    <p className="text-xl font-bold text-gray-900">0</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Getting Started */}
                <Card>
                    <CardHeader>
                        <CardTitle>Getting Started with Analytics</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 className="font-medium text-gray-900 mb-2">What you can track:</h4>
                                <ul className="space-y-1 text-sm text-gray-600">
                                    <li>• Revenue and sales performance</li>
                                    <li>• Product popularity and conversion rates</li>
                                    <li>• Download patterns and success rates</li>
                                    <li>• Customer behavior and segmentation</li>
                                    <li>• Search queries and optimization opportunities</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium text-gray-900 mb-2">Key benefits:</h4>
                                <ul className="space-y-1 text-sm text-gray-600">
                                    <li>• Make data-driven business decisions</li>
                                    <li>• Identify top-performing products</li>
                                    <li>• Optimize customer experience</li>
                                    <li>• Track growth and performance trends</li>
                                    <li>• Improve conversion rates</li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SuperAdminLayout>
    );
}
