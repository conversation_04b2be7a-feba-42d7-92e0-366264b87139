import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ArrowLeft, 
    Edit, 
    FileText, 
    Package, 
    Star, 
    Download,
    Calendar,
    User,
    DollarSign,
    Tag,
    Eye,
    ShoppingCart
} from 'lucide-react';

interface Product {
    id: number;
    uuid: string;
    name: string;
    slug: string;
    description?: string;
    short_description?: string;
    price: number;
    sale_price?: number;
    sku: string;
    status: 'active' | 'inactive' | 'draft';
    featured: boolean;
    digital: boolean;
    downloadable: boolean;
    download_limit?: number;
    download_expiry_days?: number;
    image?: string;
    gallery?: string[];
    meta_title?: string;
    meta_description?: string;
    tags?: string[];
    attributes?: Record<string, unknown>;
    category?: {
        id: number;
        name: string;
        slug: string;
    };
    creator?: {
        id: number;
        name: string;
        email: string;
    };
    files?: Array<{
        id: number;
        filename: string;
        original_filename: string;
        file_size: number;
        mime_type: string;
        is_active: boolean;
        download_count: number;
        created_at: string;
    }>;
    order_items?: Array<{
        id: number;
        quantity: number;
        price: number;
        order: {
            id: number;
            order_number: string;
            status: string;
            created_at: string;
        };
    }>;
    created_at: string;
    updated_at: string;
}

interface Props {
    product: Product;
}

export default function ShowProduct({ product }: Props) {
    const getStatusBadge = (status: string) => {
        const variants = {
            active: 'bg-green-100 text-green-800',
            inactive: 'bg-red-100 text-red-800',
            draft: 'bg-gray-100 text-gray-800',
        };
        return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
    };

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    return (
        <SuperAdminLayout>
            <Head title={`Product: ${product.name}`} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('super-admin.products.index')}>
                            <Button variant="outline" size="sm">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Products
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight">{product.name}</h1>
                            <p className="text-muted-foreground">SKU: {product.sku}</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Link href={route('super-admin.products.edit', product.id)}>
                            <Button>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Product
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {/* Product Overview */}
                    <Card className="lg:col-span-2">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Product Details
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {product.image && (
                                <div>
                                    <img 
                                        src={product.image} 
                                        alt={product.name}
                                        className="w-full h-48 object-cover rounded-lg"
                                    />
                                </div>
                            )}
                            
                            <div className="grid gap-4 md:grid-cols-2">
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Name</label>
                                    <p className="font-medium">{product.name}</p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                                    <div className="flex items-center gap-2">
                                        {getStatusBadge(product.status)}
                                        {product.featured && (
                                            <Badge className="bg-yellow-100 text-yellow-800">
                                                <Star className="mr-1 h-3 w-3" />
                                                Featured
                                            </Badge>
                                        )}
                                        {product.digital && (
                                            <Badge className="bg-blue-100 text-blue-800">
                                                <Download className="mr-1 h-3 w-3" />
                                                Digital
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Price</label>
                                    <div>
                                        <p className="font-medium">{formatPrice(product.price)}</p>
                                        {product.sale_price && (
                                            <p className="text-sm text-green-600">
                                                Sale Price: {formatPrice(product.sale_price)}
                                            </p>
                                        )}
                                    </div>
                                </div>
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Category</label>
                                    <p>{product.category?.name || 'No category'}</p>
                                </div>
                            </div>

                            {product.short_description && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Short Description</label>
                                    <p className="text-sm">{product.short_description}</p>
                                </div>
                            )}

                            {product.description && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                                    <div className="prose prose-sm max-w-none">
                                        <p>{product.description}</p>
                                    </div>
                                </div>
                            )}

                            {product.tags && product.tags.length > 0 && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Tags</label>
                                    <div className="flex flex-wrap gap-2 mt-1">
                                        {product.tags.map((tag, index) => (
                                            <Badge key={index} variant="outline">
                                                <Tag className="mr-1 h-3 w-3" />
                                                {tag}
                                            </Badge>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Product Info */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Eye className="h-5 w-5" />
                                Product Info
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-center gap-2">
                                    <User className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Creator</p>
                                        <p className="text-sm text-muted-foreground">
                                            {product.creator?.name || 'Unknown'}
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Created</p>
                                        <p className="text-sm text-muted-foreground">
                                            {new Date(product.created_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-center gap-2">
                                    <FileText className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Files</p>
                                        <p className="text-sm text-muted-foreground">
                                            {product.files?.length || 0} files
                                        </p>
                                    </div>
                                </div>

                                <div className="flex items-center gap-2">
                                    <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                                    <div>
                                        <p className="text-sm font-medium">Orders</p>
                                        <p className="text-sm text-muted-foreground">
                                            {product.order_items?.length || 0} orders
                                        </p>
                                    </div>
                                </div>

                                {product.downloadable && (
                                    <>
                                        {product.download_limit && (
                                            <div className="flex items-center gap-2">
                                                <Download className="h-4 w-4 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Download Limit</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {product.download_limit} downloads
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                        
                                        {product.download_expiry_days && (
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                                <div>
                                                    <p className="text-sm font-medium">Download Expiry</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {product.download_expiry_days} days
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Files Section */}
                {product.files && product.files.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="h-5 w-5" />
                                Digital Files ({product.files.length})
                            </CardTitle>
                            <CardDescription>
                                Files associated with this product
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {product.files.map((file) => (
                                    <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <FileText className="h-8 w-8 text-muted-foreground" />
                                            <div>
                                                <p className="font-medium">{file.original_filename}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {formatFileSize(file.file_size)} • {file.mime_type}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Badge variant={file.is_active ? "default" : "secondary"}>
                                                {file.is_active ? 'Active' : 'Inactive'}
                                            </Badge>
                                            <Badge variant="outline">
                                                {file.download_count} downloads
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Recent Orders */}
                {product.order_items && product.order_items.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <ShoppingCart className="h-5 w-5" />
                                Recent Orders ({product.order_items.length})
                            </CardTitle>
                            <CardDescription>
                                Recent orders containing this product
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {product.order_items.slice(0, 5).map((item) => (
                                    <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div>
                                            <p className="font-medium">Order #{item.order.order_number}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {new Date(item.order.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">{formatPrice(item.price)}</p>
                                            <p className="text-sm text-muted-foreground">
                                                Qty: {item.quantity}
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </SuperAdminLayout>
    );
}
