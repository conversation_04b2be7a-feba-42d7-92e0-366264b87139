import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Head, Link, router } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Package,
    Plus,
    Search,
    MoreHorizontal,
    Edit,
    Eye,
    Trash2,
    Star,
    StarOff,
    Download,
    Users
} from 'lucide-react';
import { useState } from 'react';

interface Product {
    id: number;
    name: string;
    slug: string;
    description?: string;
    price: number;
    sale_price?: number;
    sku: string;
    status: 'active' | 'inactive' | 'draft';
    featured: boolean;
    digital: boolean;
    downloadable: boolean;
    image?: string;
    category?: {
        id: number;
        name: string;
    };
    creator?: {
        id: number;
        name: string;
    };
    files_count: number;
    order_items_count: number;
    created_at: string;
    updated_at: string;
}

interface Category {
    id: number;
    name: string;
}

interface Creator {
    id: number;
    name: string;
}

interface Props {
    products: {
        data: Product[];
        links?: any[];
        meta?: any;
        current_page?: number;
        total?: number;
    };
    categories: Category[];
    creators: Creator[];
    filters: {
        search?: string;
        category?: string;
        status?: string;
        featured?: string;
        creator?: string;
        min_price?: string;
        max_price?: string;
    };
    sort: {
        by: string;
        order: string;
    };
}

export default function ProductsIndex({ products, categories, creators, filters, sort }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [selectedCategory, setSelectedCategory] = useState(filters.category || 'all');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || 'all');
    const [selectedCreator, setSelectedCreator] = useState(filters.creator || 'all');

    const handleSearch = () => {
        router.get(route('super-admin.products.index'), {
            search,
            category: selectedCategory === 'all' ? '' : selectedCategory,
            status: selectedStatus === 'all' ? '' : selectedStatus,
            creator: selectedCreator === 'all' ? '' : selectedCreator,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleClearFilters = () => {
        setSearch('');
        setSelectedCategory('all');
        setSelectedStatus('all');
        setSelectedCreator('all');
        router.get(route('super-admin.products.index'));
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge className="bg-green-100 text-green-800">Active</Badge>;
            case 'inactive':
                return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
            case 'draft':
                return <Badge className="bg-gray-100 text-gray-800">Draft</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const handleDelete = (productId: number) => {
        if (confirm('Are you sure you want to delete this product?')) {
            router.delete(route('super-admin.products.destroy', productId));
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    return (
        <SuperAdminLayout>
            <Head title="Products Management" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">Products Management</h1>
                        <p className="text-muted-foreground">
                            Manage all products and digital downloads in the system
                        </p>
                    </div>
                    <Link href={route('super-admin.products.create')}>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Product
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Search className="h-5 w-5" />
                            Search & Filter
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5 items-end">
                            <div>
                                <label className="text-sm font-medium">Search</label>
                                <Input
                                    placeholder="Search products..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium">Category</label>
                                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All categories" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All categories</SelectItem>
                                        {categories.map((category) => (
                                            <SelectItem key={category.id} value={category.id.toString()}>
                                                {category.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <label className="text-sm font-medium">Status</label>
                                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All statuses" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All statuses</SelectItem>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                        <SelectItem value="draft">Draft</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <label className="text-sm font-medium">Creator</label>
                                <Select value={selectedCreator} onValueChange={setSelectedCreator}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="All creators" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All creators</SelectItem>
                                        {creators.map((creator) => (
                                            <SelectItem key={creator.id} value={creator.id.toString()}>
                                                {creator.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex gap-2">
                                <Button onClick={handleSearch}>Search</Button>
                                <Button variant="outline" onClick={handleClearFilters}>Clear</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Products Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Package className="h-5 w-5" />
                            Products ({products.meta?.total || products.total || products.data?.length || 0})
                        </CardTitle>
                        <CardDescription>
                            Manage product catalog and digital downloads
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Product</TableHead>
                                    <TableHead>Category</TableHead>
                                    <TableHead>Price</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead>Creator</TableHead>
                                    <TableHead>Files</TableHead>
                                    <TableHead>Orders</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {products.data && products.data.length > 0 ? products.data.map((product) => (
                                    <TableRow key={product.id}>
                                        <TableCell>
                                            <div className="flex items-center gap-3">
                                                {product.image && (
                                                    <img
                                                        src={product.image}
                                                        alt={product.name}
                                                        className="w-10 h-10 rounded object-cover"
                                                    />
                                                )}
                                                <div>
                                                    <div className="font-medium flex items-center gap-2">
                                                        {product.name}
                                                        {product.featured && (
                                                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                                        )}
                                                        {product.digital && (
                                                            <Download className="h-4 w-4 text-blue-500" />
                                                        )}
                                                    </div>
                                                    <div className="text-sm text-muted-foreground">{product.sku}</div>
                                                </div>
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {product.category ? (
                                                <Badge variant="outline">{product.category.name}</Badge>
                                            ) : (
                                                <span className="text-muted-foreground">No category</span>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <div>
                                                <div className="font-medium">{formatPrice(product.price)}</div>
                                                {product.sale_price && (
                                                    <div className="text-sm text-green-600">
                                                        Sale: {formatPrice(product.sale_price)}
                                                    </div>
                                                )}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            {getStatusBadge(product.status)}
                                        </TableCell>
                                        <TableCell>
                                            {product.creator ? (
                                                <div className="flex items-center gap-2">
                                                    <Users className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm">{product.creator.name}</span>
                                                </div>
                                            ) : (
                                                <span className="text-muted-foreground">Unknown</span>
                                            )}
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">
                                                {product.files_count} files
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline">
                                                {product.order_items_count} orders
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <span className="text-sm">
                                                {new Date(product.created_at).toLocaleDateString()}
                                            </span>
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <DropdownMenu>
                                                <DropdownMenuTrigger asChild>
                                                    <Button variant="ghost" className="h-8 w-8 p-0">
                                                        <MoreHorizontal className="h-4 w-4" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end">
                                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('super-admin.products.show', product.id)}>
                                                            <Eye className="mr-2 h-4 w-4" />
                                                            View
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem asChild>
                                                        <Link href={route('super-admin.products.edit', product.id)}>
                                                            <Edit className="mr-2 h-4 w-4" />
                                                            Edit
                                                        </Link>
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem
                                                        onClick={() => handleDelete(product.id)}
                                                        className="text-red-600"
                                                    >
                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                        Delete
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </TableCell>
                                    </TableRow>
                                )) : (
                                    <TableRow>
                                        <TableCell colSpan={9} className="text-center py-8">
                                            <div className="text-muted-foreground">
                                                No products found. {products.data?.length === 0 ? 'Try adjusting your filters.' : 'Create your first product to get started.'}
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>
        </SuperAdminLayout>
    );
}
