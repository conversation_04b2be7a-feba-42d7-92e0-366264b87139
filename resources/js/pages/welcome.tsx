import { type SharedData } from '@/types';
import { Head, Link, usePage } from '@inertiajs/react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
    ShoppingBag,
    Download,
    Shield,
    Clock,
    Star,
    ArrowRight,
    CheckCircle,
    Zap,
    Globe,
    Users
} from 'lucide-react';

export default function Welcome() {
    const { auth } = usePage<SharedData>().props;

    return (
        <>
            <Head title="Digital Download Center - Premium Digital Products">
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
                {/* Navigation */}
                <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="flex justify-between items-center h-16">
                            <div className="flex items-center gap-2">
                                <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                                    <Download className="h-5 w-5 text-white" />
                                </div>
                                <span className="text-xl font-bold text-gray-900">Digital Download Center</span>
                            </div>

                            <div className="flex items-center gap-4">
                                <Link href={route('shop.index')} className="text-gray-600 hover:text-gray-900">
                                    Browse Shop
                                </Link>
                                {auth.user ? (
                                    <Button asChild>
                                        <Link href={route('dashboard')}>
                                            Dashboard
                                        </Link>
                                    </Button>
                                ) : (
                                    <div className="flex items-center gap-3">
                                        <Link
                                            href={route('login')}
                                            className="text-gray-600 hover:text-gray-900"
                                        >
                                            Log in
                                        </Link>
                                        <Button asChild>
                                            <Link href={route('register')}>
                                                Get Started
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </nav>

                {/* Hero Section */}
                <section className="relative py-20 lg:py-32">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center">
                            <Badge className="mb-6 bg-blue-100 text-blue-800 hover:bg-blue-100">
                                <Zap className="h-3 w-3 mr-1" />
                                Premium Digital Downloads
                            </Badge>
                            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                                Discover Amazing
                                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> Digital Products</span>
                            </h1>
                            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
                                Access thousands of high-quality digital downloads including software, templates,
                                graphics, and more. Secure, instant downloads with lifetime access.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <Button size="lg" asChild>
                                    <Link href={route('shop.index')}>
                                        <ShoppingBag className="h-5 w-5 mr-2" />
                                        Browse Products
                                    </Link>
                                </Button>
                                {!auth.user && (
                                    <Button size="lg" variant="outline" asChild>
                                        <Link href={route('register')}>
                                            Create Free Account
                                            <ArrowRight className="h-5 w-5 ml-2" />
                                        </Link>
                                    </Button>
                                )}
                            </div>
                        </div>
                    </div>
                </section>
                {/* Features Section */}
                <section className="py-20 bg-white">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                Why Choose Our Platform?
                            </h2>
                            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                                We provide the best digital download experience with security, reliability, and ease of use.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                            <Card className="text-center border-0 shadow-lg">
                                <CardHeader>
                                    <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Shield className="h-6 w-6 text-blue-600" />
                                    </div>
                                    <CardTitle className="text-lg">Secure Downloads</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Advanced security with IP binding and time-limited download links for maximum protection.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="text-center border-0 shadow-lg">
                                <CardHeader>
                                    <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Zap className="h-6 w-6 text-green-600" />
                                    </div>
                                    <CardTitle className="text-lg">Instant Access</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Get immediate access to your purchases with instant download links delivered to your account.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="text-center border-0 shadow-lg">
                                <CardHeader>
                                    <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Globe className="h-6 w-6 text-purple-600" />
                                    </div>
                                    <CardTitle className="text-lg">Global CDN</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Fast downloads worldwide with our global content delivery network and cloud storage.
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="text-center border-0 shadow-lg">
                                <CardHeader>
                                    <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                        <Users className="h-6 w-6 text-orange-600" />
                                    </div>
                                    <CardTitle className="text-lg">24/7 Support</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <p className="text-gray-600">
                                        Dedicated customer support team ready to help you with any questions or issues.
                                    </p>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </section>
                {/* How It Works Section */}
                <section className="py-20 bg-gray-50">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="text-center mb-16">
                            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                                How It Works
                            </h2>
                            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                                Get started with digital downloads in just a few simple steps.
                            </p>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div className="text-center">
                                <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <span className="text-2xl font-bold text-white">1</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-4">Browse & Choose</h3>
                                <p className="text-gray-600">
                                    Explore our extensive catalog of digital products and find exactly what you need.
                                </p>
                            </div>

                            <div className="text-center">
                                <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <span className="text-2xl font-bold text-white">2</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-4">Secure Purchase</h3>
                                <p className="text-gray-600">
                                    Complete your purchase with our secure payment system and create your account.
                                </p>
                            </div>

                            <div className="text-center">
                                <div className="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <span className="text-2xl font-bold text-white">3</span>
                                </div>
                                <h3 className="text-xl font-semibold text-gray-900 mb-4">Download & Enjoy</h3>
                                <p className="text-gray-600">
                                    Access your downloads immediately from your account dashboard with secure links.
                                </p>
                            </div>
                        </div>
                    </div>
                </section>

                {/* CTA Section */}
                <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
                    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                            Ready to Get Started?
                        </h2>
                        <p className="text-xl text-blue-100 mb-8">
                            Join thousands of satisfied customers and discover amazing digital products today.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                            <Button size="lg" variant="secondary" asChild>
                                <Link href={route('shop.index')}>
                                    <ShoppingBag className="h-5 w-5 mr-2" />
                                    Browse Products
                                </Link>
                            </Button>
                            {!auth.user && (
                                <Button size="lg" variant="outline" className="bg-white text-blue-600 hover:bg-gray-50" asChild>
                                    <Link href={route('register')}>
                                        Create Free Account
                                    </Link>
                                </Button>
                            )}
                        </div>
                    </div>
                </section>

                {/* Footer */}
                <footer className="bg-gray-900 text-white py-12">
                    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
                            <div>
                                <div className="flex items-center gap-2 mb-4">
                                    <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                                        <Download className="h-5 w-5 text-white" />
                                    </div>
                                    <span className="text-xl font-bold">Digital Download Center</span>
                                </div>
                                <p className="text-gray-400">
                                    Your trusted source for premium digital downloads with secure, instant access.
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Quick Links</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><Link href={route('shop.index')} className="hover:text-white">Browse Products</Link></li>
                                    <li><Link href="#" className="hover:text-white">Categories</Link></li>
                                    <li><Link href="#" className="hover:text-white">Featured</Link></li>
                                    <li><Link href="#" className="hover:text-white">New Arrivals</Link></li>
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Account</h3>
                                <ul className="space-y-2 text-gray-400">
                                    {auth.user ? (
                                        <>
                                            <li><Link href={route('dashboard')} className="hover:text-white">Dashboard</Link></li>
                                            <li><Link href={route('customer.orders')} className="hover:text-white">My Orders</Link></li>
                                            <li><Link href={route('customer.downloads')} className="hover:text-white">My Downloads</Link></li>
                                        </>
                                    ) : (
                                        <>
                                            <li><Link href={route('login')} className="hover:text-white">Login</Link></li>
                                            <li><Link href={route('register')} className="hover:text-white">Register</Link></li>
                                        </>
                                    )}
                                </ul>
                            </div>

                            <div>
                                <h3 className="font-semibold mb-4">Support</h3>
                                <ul className="space-y-2 text-gray-400">
                                    <li><Link href="#" className="hover:text-white">Help Center</Link></li>
                                    <li><Link href="#" className="hover:text-white">Contact Us</Link></li>
                                    <li><Link href="#" className="hover:text-white">Terms of Service</Link></li>
                                    <li><Link href="#" className="hover:text-white">Privacy Policy</Link></li>
                                </ul>
                            </div>
                        </div>

                        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                            <p>&copy; 2024 Digital Download Center. All rights reserved.</p>
                        </div>
                    </div>
                </footer>
            </div>
        </>
    );
}