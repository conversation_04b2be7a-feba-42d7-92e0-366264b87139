import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Mail, ArrowLeft, Shield, Info } from 'lucide-react';
import { FormEventHandler } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type ForgotPasswordForm = {
    email: string;
};

interface ForgotPasswordProps {
    status?: string;
}

export default function ForgotPassword({ status }: ForgotPasswordProps) {
    const { data, setData, post, processing, errors } = useForm<Required<ForgotPasswordForm>>({
        email: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.email'));
    };

    return (
        <AuthLayout 
            title="Reset your password" 
            description="Enter your email address and we'll send you a secure link to reset your password"
        >
            <Head title="Forgot Password" />

            {status && (
                <div className="mb-6 rounded-xl bg-green-50 border border-green-200 p-4 shadow-sm">
                    <div className="flex items-center justify-center space-x-2">
                        <Shield className="h-5 w-5 text-green-600" />
                        <p className="text-sm font-medium text-green-800 text-center">{status}</p>
                    </div>
                </div>
            )}

            <form className="space-y-6" onSubmit={submit}>
                {/* Email Field */}
                <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                        Email address
                    </Label>
                    <div className="relative group">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <Mail className="h-5 w-5 text-slate-400 group-focus-within:text-blue-500 transition-colors" />
                        </div>
                        <Input
                            id="email"
                            type="email"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="Enter your email address"
                            className="pl-12 h-12 text-base border-slate-200 bg-slate-50/50 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:bg-slate-800/50 dark:focus:bg-slate-800 dark:focus:border-blue-400 transition-all duration-200 rounded-xl"
                        />
                    </div>
                    <InputError message={errors.email} />
                </div>

                {/* Submit Button */}
                <Button 
                    type="submit" 
                    className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:from-blue-700 focus:to-blue-800 text-white font-semibold rounded-xl shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40 focus:shadow-xl focus:shadow-blue-500/40 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none transform hover:scale-[1.02] active:scale-[0.98]" 
                    tabIndex={2} 
                    disabled={processing}
                >
                    {processing ? (
                        <>
                            <LoaderCircle className="h-5 w-5 animate-spin mr-2" />
                            Sending reset link...
                        </>
                    ) : (
                        <>
                            <Mail className="h-5 w-5 mr-2" />
                            Send reset link
                        </>
                    )}
                </Button>

                {/* Back to Login */}
                <div className="text-center pt-4 border-t border-slate-200 dark:border-slate-700">
                    <TextLink 
                        href={route('login')} 
                        className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                        tabIndex={3}
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to sign in
                    </TextLink>
                </div>
            </form>

            {/* Help Section */}
            <div className="mt-8 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                <div className="flex items-start space-x-3">
                    <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <div>
                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">
                            Need help?
                        </h3>
                        <p className="text-xs text-slate-600 dark:text-slate-400 leading-relaxed">
                            If you don't receive an email within a few minutes, check your spam folder. 
                            Still having trouble? {' '}
                            <TextLink href="/contact" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                                Contact our support team
                            </TextLink>
                            {' '}for assistance.
                        </p>
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}
