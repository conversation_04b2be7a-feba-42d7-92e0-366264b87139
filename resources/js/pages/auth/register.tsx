import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Eye, EyeOff, Mail, Lock, User, Shield, Check } from 'lucide-react';
import { FormEventHand<PERSON>, useState, useEffect } from 'react';

import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import AuthLayout from '@/layouts/auth-layout';

type RegisterForm = {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    terms: boolean;
};

export default function Register() {
    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);
    const [passwordStrength, setPasswordStrength] = useState(0);
    
    const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
        terms: false,
    });

    // Password strength checker
    useEffect(() => {
        const password = data.password;
        let strength = 0;
        
        if (password.length >= 8) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        setPasswordStrength(strength);
    }, [data.password]);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    const getPasswordStrengthColor = () => {
        if (passwordStrength <= 2) return 'bg-red-500';
        if (passwordStrength <= 3) return 'bg-yellow-500';
        if (passwordStrength <= 4) return 'bg-blue-500';
        return 'bg-green-500';
    };

    const getPasswordStrengthText = () => {
        if (passwordStrength <= 2) return 'Weak';
        if (passwordStrength <= 3) return 'Fair';
        if (passwordStrength <= 4) return 'Good';
        return 'Strong';
    };

    return (
        <AuthLayout 
            title="Create your account" 
            description="Join thousands of users and start your journey today"
        >
            <Head title="Sign Up" />

            <form className="space-y-6" onSubmit={submit}>
                {/* Name Field */}
                <div className="space-y-2">
                    <Label htmlFor="name" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                        Full name
                    </Label>
                    <div className="relative group">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <User className="h-5 w-5 text-slate-400 group-focus-within:text-blue-500 transition-colors" />
                        </div>
                        <Input
                            id="name"
                            type="text"
                            required
                            autoFocus
                            tabIndex={1}
                            autoComplete="name"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            placeholder="Enter your full name"
                            className="pl-12 h-12 text-base border-slate-200 bg-slate-50/50 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:bg-slate-800/50 dark:focus:bg-slate-800 dark:focus:border-blue-400 transition-all duration-200 rounded-xl"
                        />
                    </div>
                    <InputError message={errors.name} />
                </div>

                {/* Email Field */}
                <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                        Email address
                    </Label>
                    <div className="relative group">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <Mail className="h-5 w-5 text-slate-400 group-focus-within:text-blue-500 transition-colors" />
                        </div>
                        <Input
                            id="email"
                            type="email"
                            required
                            tabIndex={2}
                            autoComplete="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            placeholder="Enter your email address"
                            className="pl-12 h-12 text-base border-slate-200 bg-slate-50/50 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:bg-slate-800/50 dark:focus:bg-slate-800 dark:focus:border-blue-400 transition-all duration-200 rounded-xl"
                        />
                    </div>
                    <InputError message={errors.email} />
                </div>

                {/* Password Field */}
                <div className="space-y-2">
                    <Label htmlFor="password" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                        Password
                    </Label>
                    <div className="relative group">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <Lock className="h-5 w-5 text-slate-400 group-focus-within:text-blue-500 transition-colors" />
                        </div>
                        <Input
                            id="password"
                            type={showPassword ? 'text' : 'password'}
                            required
                            tabIndex={3}
                            autoComplete="new-password"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            placeholder="Create a strong password"
                            className="pl-12 pr-12 h-12 text-base border-slate-200 bg-slate-50/50 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:bg-slate-800/50 dark:focus:bg-slate-800 dark:focus:border-blue-400 transition-all duration-200 rounded-xl"
                        />
                        <button
                            type="button"
                            className="absolute inset-y-0 right-0 flex items-center pr-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                            onClick={() => setShowPassword(!showPassword)}
                            tabIndex={-1}
                            aria-label={showPassword ? 'Hide password' : 'Show password'}
                        >
                            {showPassword ? (
                                <EyeOff className="h-5 w-5" />
                            ) : (
                                <Eye className="h-5 w-5" />
                            )}
                        </button>
                    </div>
                    
                    {/* Password Strength Indicator */}
                    {data.password && (
                        <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                                <div className="flex-1 bg-slate-200 rounded-full h-2">
                                    <div 
                                        className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                                        style={{ width: `${(passwordStrength / 5) * 100}%` }}
                                    ></div>
                                </div>
                                <span className="text-xs font-medium text-slate-600 dark:text-slate-400">
                                    {getPasswordStrengthText()}
                                </span>
                            </div>
                            <div className="text-xs text-slate-500 dark:text-slate-400 space-y-1">
                                <div className="flex items-center space-x-2">
                                    <Check className={`h-3 w-3 ${data.password.length >= 8 ? 'text-green-500' : 'text-slate-300'}`} />
                                    <span>At least 8 characters</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Check className={`h-3 w-3 ${/[A-Z]/.test(data.password) ? 'text-green-500' : 'text-slate-300'}`} />
                                    <span>One uppercase letter</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Check className={`h-3 w-3 ${/[0-9]/.test(data.password) ? 'text-green-500' : 'text-slate-300'}`} />
                                    <span>One number</span>
                                </div>
                            </div>
                        </div>
                    )}
                    
                    <InputError message={errors.password} />
                </div>

                {/* Password Confirmation Field */}
                <div className="space-y-2">
                    <Label htmlFor="password_confirmation" className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                        Confirm password
                    </Label>
                    <div className="relative group">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <Lock className="h-5 w-5 text-slate-400 group-focus-within:text-blue-500 transition-colors" />
                        </div>
                        <Input
                            id="password_confirmation"
                            type={showPasswordConfirmation ? 'text' : 'password'}
                            required
                            tabIndex={4}
                            autoComplete="new-password"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            placeholder="Confirm your password"
                            className="pl-12 pr-12 h-12 text-base border-slate-200 bg-slate-50/50 focus:bg-white focus:border-blue-500 focus:ring-blue-500/20 dark:border-slate-600 dark:bg-slate-800/50 dark:focus:bg-slate-800 dark:focus:border-blue-400 transition-all duration-200 rounded-xl"
                        />
                        <button
                            type="button"
                            className="absolute inset-y-0 right-0 flex items-center pr-4 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                            onClick={() => setShowPasswordConfirmation(!showPasswordConfirmation)}
                            tabIndex={-1}
                            aria-label={showPasswordConfirmation ? 'Hide password' : 'Show password'}
                        >
                            {showPasswordConfirmation ? (
                                <EyeOff className="h-5 w-5" />
                            ) : (
                                <Eye className="h-5 w-5" />
                            )}
                        </button>
                    </div>
                    <InputError message={errors.password_confirmation} />
                </div>

                {/* Terms and Conditions */}
                <div className="space-y-3">
                    <div className="flex items-start space-x-3 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                        <Checkbox
                            id="terms"
                            name="terms"
                            checked={data.terms}
                            onClick={() => setData('terms', !data.terms)}
                            tabIndex={5}
                            className="mt-0.5 border-slate-300 text-blue-600 focus:ring-blue-500 rounded-md"
                        />
                        <Label htmlFor="terms" className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed cursor-pointer">
                            I agree to the{' '}
                            <TextLink href="/terms" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                                Terms of Service
                            </TextLink>
                            {' '}and{' '}
                            <TextLink href="/privacy" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
                                Privacy Policy
                            </TextLink>
                        </Label>
                    </div>
                    <InputError message={errors.terms} />
                </div>

                {/* Submit Button */}
                <Button 
                    type="submit" 
                    className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:from-blue-700 focus:to-blue-800 text-white font-semibold rounded-xl shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40 focus:shadow-xl focus:shadow-blue-500/40 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none transform hover:scale-[1.02] active:scale-[0.98]" 
                    tabIndex={6} 
                    disabled={processing || !data.terms}
                >
                    {processing ? (
                        <>
                            <LoaderCircle className="h-5 w-5 animate-spin mr-2" />
                            Creating account...
                        </>
                    ) : (
                        <>
                            <Shield className="h-5 w-5 mr-2" />
                            Create secure account
                        </>
                    )}
                </Button>

                {/* Divider */}
                <div className="relative my-8">
                    <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-slate-200 dark:border-slate-700"></div>
                    </div>
                    <div className="relative flex justify-center text-sm">
                        <span className="px-4 bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-400 font-medium">
                            Already have an account?
                        </span>
                    </div>
                </div>

                {/* Sign In Link */}
                <div className="text-center">
                    <TextLink 
                        href={route('login')} 
                        className="inline-flex items-center justify-center w-full h-12 px-6 font-semibold text-blue-600 bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 dark:text-blue-400 border border-blue-200 dark:border-blue-800 rounded-xl transition-all duration-200 hover:scale-[1.02] active:scale-[0.98]"
                        tabIndex={7}
                    >
                        Sign in to your account
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
