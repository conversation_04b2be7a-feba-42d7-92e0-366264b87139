import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Mail, CheckCircle, AlertCircle, RefreshCw, LogOut } from 'lucide-react';
import { FormEventHandler } from 'react';

import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import AuthLayout from '@/layouts/auth-layout';

export default function VerifyEmail({ status }: { status?: string }) {
    const { post, processing } = useForm({});

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('verification.send'));
    };

    return (
        <AuthLayout 
            title="Verify your email" 
            description="We've sent a verification link to your email address. Please check your inbox and click the link to verify your account."
        >
            <Head title="Email Verification" />

            {/* Success Message */}
            {status === 'verification-link-sent' && (
                <div className="mb-6 rounded-xl bg-green-50 border border-green-200 p-4 shadow-sm">
                    <div className="flex items-center justify-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <p className="text-sm font-medium text-green-800 text-center">
                            A new verification link has been sent to your email address.
                        </p>
                    </div>
                </div>
            )}

            {/* Main Content */}
            <div className="space-y-6">
                {/* Email Icon and Instructions */}
                <div className="text-center space-y-4">
                    <div className="flex justify-center">
                        <div className="rounded-full bg-blue-100 dark:bg-blue-900/20 p-4">
                            <Mail className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                        </div>
                    </div>
                    
                    <div className="space-y-2">
                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                            Before continuing, please check your email for a verification link. 
                            If you didn't receive the email, we can send you another one.
                        </p>
                    </div>
                </div>

                {/* Action Buttons */}
                <form onSubmit={submit} className="space-y-4">
                    <Button 
                        type="submit"
                        disabled={processing} 
                        className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:from-blue-700 focus:to-blue-800 text-white font-semibold rounded-xl shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/40 focus:shadow-xl focus:shadow-blue-500/40 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none transform hover:scale-[1.02] active:scale-[0.98]"
                    >
                        {processing ? (
                            <>
                                <LoaderCircle className="h-5 w-5 animate-spin mr-2" />
                                Sending verification email...
                            </>
                        ) : (
                            <>
                                <RefreshCw className="h-5 w-5 mr-2" />
                                Resend verification email
                            </>
                        )}
                    </Button>

                    <div className="text-center pt-4 border-t border-slate-200 dark:border-slate-700">
                        <TextLink 
                            href={route('logout')} 
                            method="post" 
                            className="inline-flex items-center text-sm font-medium text-slate-600 hover:text-slate-800 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
                        >
                            <LogOut className="h-4 w-4 mr-2" />
                            Sign out
                        </TextLink>
                    </div>
                </form>

                {/* Help Section */}
                <div className="mt-8 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-xl border border-slate-200 dark:border-slate-700">
                    <div className="flex items-start space-x-3">
                        <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0" />
                        <div>
                            <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-2">
                                Didn't receive the email?
                            </h3>
                            <div className="text-xs text-slate-600 dark:text-slate-400 space-y-1">
                                <p>• Check your spam or junk folder</p>
                                <p>• Make sure you entered the correct email address</p>
                                <p>• Wait a few minutes for the email to arrive</p>
                                <p>• Click "Resend verification email" to try again</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AuthLayout>
    );
}
