import { Head, usePage } from '@inertiajs/react';
import { Users, Shield, Settings, BarChart3, FileText, Activity } from 'lucide-react';

import TextLink from '@/components/text-link';
import { DashboardStats } from '@/components/dashboard/dashboard-stats';
import { RecentActivity } from '@/components/dashboard/recent-activity';
import { DefaultTransactionsSummaries } from '@/components/dashboard/transactions-summary';
import SmartLayout from '@/layouts/smart-layout';
import { AdminDashboardProps, SystemHealthItem } from '@/types/dashboard';

export default function AdminDashboard() {
    const { dashboardData, systemHealth } = usePage<AdminDashboardProps>().props;

    const stats = [
        { name: 'Total Users', value: dashboardData?.stats?.users?.total?.toLocaleString() || '1,234', icon: Users, color: 'blue' },
        { name: 'Active Sessions', value: '89', icon: Activity, color: 'green' },
        { name: 'Security Events', value: '12', icon: Shield, color: 'yellow' },
        { name: 'System Health', value: '98%', icon: BarChart3, color: 'emerald' },
    ];

    const quickActions = [
        { name: 'Manage Users', href: '/admin/users', icon: Users, description: 'Add, edit, and manage user accounts' },
        { name: 'System Settings', href: '/admin/settings', icon: Settings, description: 'Configure system preferences' },
        { name: 'View Reports', href: '/admin/analytics', icon: BarChart3, description: 'Access analytics and reports' },
        { name: 'Audit Logs', href: '/admin/audit-logs', icon: FileText, description: 'Review system audit logs' },
    ];

    // Sample admin activity data
    const adminActivities = [
        {
            id: '1',
            type: 'user' as const,
            title: 'User role updated',
            description: '<EMAIL> assigned admin role',
            status: 'complete' as const,
            time: '5 minutes ago',
        },
        {
            id: '2',
            type: 'user' as const,
            title: 'Failed login attempt',
            description: 'Multiple failed attempts from IP *************',
            status: 'pending' as const,
            time: '10 minutes ago',
        },
        {
            id: '3',
            type: 'transaction' as const,
            title: 'System backup completed',
            description: 'Daily backup process finished successfully',
            status: 'complete' as const,
            time: '1 hour ago',
        },
    ];

    return (
        <SmartLayout>
            <Head title="Admin Dashboard" />

            <div className="space-y-8">
                {/* Header */}
                <div className="border-b border-slate-200 dark:border-slate-700 pb-6">
                    <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
                        Admin Dashboard
                    </h1>
                    <p className="mt-2 text-slate-600 dark:text-slate-400">
                        Manage your platform and monitor system performance
                    </p>
                </div>

                {/* Dashboard Statistics */}
                <DashboardStats data={dashboardData?.stats} />

                {/* Admin Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {stats.map((stat) => (
                        <div
                            key={stat.name}
                            className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm hover:shadow-md transition-shadow"
                        >
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                                        {stat.name}
                                    </p>
                                    <p className="text-2xl font-bold text-slate-900 dark:text-white mt-1">
                                        {stat.value}
                                    </p>
                                </div>
                                <div className={`p-3 rounded-lg bg-${stat.color}-100 dark:bg-${stat.color}-900/20`}>
                                    <stat.icon className={`h-6 w-6 text-${stat.color}-600 dark:text-${stat.color}-400`} />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Recent Admin Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <RecentActivity
                        title="Recent Admin Activity"
                        items={dashboardData?.activities?.users || adminActivities}
                    />

                    <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm">
                        <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                            System Health
                        </h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            {systemHealth && Object.entries(systemHealth).map(([key, health]: [string, SystemHealthItem]) => {
                                const serviceName = key.charAt(0).toUpperCase() + key.slice(1);
                                const isOperational = health.status === 'operational';
                                return (
                                    <div key={key} className="flex items-center justify-between p-4 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                                        <span className="text-sm font-medium text-slate-900 dark:text-white">
                                            {serviceName}
                                        </span>
                                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            isOperational
                                                ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                                                : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                                        }`}>
                                            {isOperational ? 'Operational' : 'Error'}
                                        </span>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm">
                    <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-6">
                        Quick Actions
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {quickActions.map((action) => (
                            <TextLink
                                key={action.name}
                                href={action.href}
                                className="group p-4 rounded-lg border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-all duration-200"
                            >
                                <div className="flex items-start space-x-3">
                                    <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/30 transition-colors">
                                        <action.icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white group-hover:text-blue-900 dark:group-hover:text-blue-100 transition-colors">
                                            {action.name}
                                        </h3>
                                        <p className="text-xs text-slate-600 dark:text-slate-400 mt-1 leading-relaxed">
                                            {action.description}
                                        </p>
                                    </div>
                                </div>
                            </TextLink>
                        ))}
                    </div>
                </div>

                {/* Transaction Summaries */}
                <DefaultTransactionsSummaries />
            </div>
        </SmartLayout>
    );
}
