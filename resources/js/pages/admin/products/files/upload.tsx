import React, { useState, useCallback } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import InputError from '@/components/input-error';
import { ArrowLeft, Upload, X, File, AlertCircle, CheckCircle } from 'lucide-react';

interface Product {
  id: number;
  name: string;
}

interface Props {
  product: Product;
  maxFileSize: number;
  allowedExtensions: string[];
}

interface FileWithPreview extends File {
  id: string;
  preview?: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}

export default function UploadFiles({ product, maxFileSize, allowedExtensions }: Props) {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const { data, setData, post, processing, errors } = useForm({
    files: [] as File[],
    description: '',
    version: '',
    is_active: true,
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = useCallback((file: File): string | null => {
    // Check file size
    if (file.size > maxFileSize) {
      return `File size exceeds maximum allowed size of ${formatFileSize(maxFileSize)}`;
    }

    // Check file extension
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      return `File type '${extension}' is not allowed. Allowed types: ${allowedExtensions.join(', ')}`;
    }

    return null;
  }, [maxFileSize, allowedExtensions]);

  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles);
    const validFiles: FileWithPreview[] = [];

    fileArray.forEach((file) => {
      const error = validateFile(file);
      const fileWithPreview: FileWithPreview = Object.assign(file, {
        id: Math.random().toString(36).substr(2, 9),
        status: error ? 'error' : 'pending',
        progress: 0,
        error,
      });

      validFiles.push(fileWithPreview);
    });

    setFiles(prev => [...prev, ...validFiles]);

    // Update form data with valid files only
    const validFileObjects = validFiles.filter(f => f.status !== 'error');
    setData('files', [...data.files, ...validFileObjects]);
  }, [data.files, setData, validateFile]);

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
    setData('files', data.files.filter(f => (f as FileWithPreview).id !== fileId));
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      addFiles(e.dataTransfer.files);
    }
  }, [addFiles]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      addFiles(e.target.files);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (files.length === 0) {
      return;
    }

    // Update file statuses to uploading
    setFiles(prev => prev.map(f => ({ ...f, status: 'uploading', progress: 0 })));

    post(route('admin.products.files.store', product.id), {
      onSuccess: () => {
        setFiles(prev => prev.map(f => ({ ...f, status: 'success', progress: 100 })));
      },
      onError: () => {
        setFiles(prev => prev.map(f => ({ ...f, status: 'error', progress: 0 })));
      },
    });
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    // Return different icons based on file extension
    switch (extension) {
      case 'pdf':
        return <File className="h-8 w-8 text-red-500" />;
      case 'zip':
      case 'rar':
      case '7z':
        return <File className="h-8 w-8 text-yellow-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <File className="h-8 w-8 text-green-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <File className="h-8 w-8 text-purple-500" />;
      default:
        return <File className="h-8 w-8 text-blue-500" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <AppLayout>
      <Head title={`Upload Files - ${product.name}`} />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={route('admin.products.files.index', product.id)}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Files
              </Button>
            </Link>
            <Heading title="Upload Files" description={`Upload digital assets for ${product.name}`} />
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* File Upload Area */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>File Upload</CardTitle>
                </CardHeader>
                <CardContent>
                  <div
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      dragActive
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                  >
                    <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <div className="space-y-2">
                      <p className="text-lg font-medium">
                        Drag and drop files here, or{' '}
                        <label className="text-blue-600 hover:text-blue-500 cursor-pointer">
                          browse
                          <input
                            type="file"
                            multiple
                            className="hidden"
                            onChange={handleFileInput}
                            accept={allowedExtensions.map(ext => `.${ext}`).join(',')}
                          />
                        </label>
                      </p>
                      <p className="text-sm text-gray-500">
                        Maximum file size: {formatFileSize(maxFileSize)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Allowed types: {allowedExtensions.join(', ')}
                      </p>
                    </div>
                  </div>
                  <InputError message={errors.files} />
                </CardContent>
              </Card>

              {/* File List */}
              {files.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Selected Files ({files.length})</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {files.map((file) => (
                        <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                          <div className="flex-shrink-0">
                            {getFileIcon(file.name)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium truncate">{file.name}</p>
                              <div className="flex items-center space-x-2">
                                {getStatusIcon(file.status)}
                                <button
                                  type="button"
                                  onClick={() => removeFile(file.id)}
                                  className="text-gray-400 hover:text-red-500"
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                            <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                            {file.error && (
                              <p className="text-sm text-red-600 mt-1">{file.error}</p>
                            )}
                            {file.status === 'uploading' && (
                              <Progress value={file.progress} className="mt-2" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Upload Settings */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Upload Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Optional description for these files"
                      rows={3}
                    />
                    <InputError message={errors.description} />
                  </div>

                  <div>
                    <Label htmlFor="version">Version</Label>
                    <Input
                      id="version"
                      value={data.version}
                      onChange={(e) => setData('version', e.target.value)}
                      placeholder="e.g., v1.0, 2024.1"
                    />
                    <InputError message={errors.version} />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_active"
                      checked={data.is_active}
                      onCheckedChange={(checked) => setData('is_active', checked)}
                    />
                    <Label htmlFor="is_active">Active (available for download)</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Upload Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Max file size:</span>
                    <span>{formatFileSize(maxFileSize)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Files selected:</span>
                    <span>{files.filter(f => f.status !== 'error').length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Total size:</span>
                    <span>
                      {formatFileSize(
                        files
                          .filter(f => f.status !== 'error')
                          .reduce((total, file) => total + file.size, 0)
                      )}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-4">
            <Link href={route('admin.products.files.index', product.id)}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button 
              type="submit" 
              disabled={processing || files.filter(f => f.status !== 'error').length === 0}
            >
              {processing ? 'Uploading...' : `Upload ${files.filter(f => f.status !== 'error').length} File(s)`}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
