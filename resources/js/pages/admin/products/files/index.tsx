import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import { ArrowLeft, Upload, Download, MoreHorizontal, File, Eye, Trash2, HardDrive } from 'lucide-react';

interface Product {
  id: number;
  name: string;
}

interface DigitalAsset {
  id: number;
  original_name: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  download_count: number;
  is_active: boolean;
  created_at: string;
  metadata?: {
    description?: string;
    version?: string;
  };
}

interface StorageStats {
  total_assets: number;
  active_assets: number;
  total_size: number;
  total_size_formatted: string;
}

interface Props {
  product: Product;
  assets: {
    data: DigitalAsset[];
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    meta: {
      current_page: number;
      from: number;
      last_page: number;
      per_page: number;
      to: number;
      total: number;
    };
  };
  storageStats: StorageStats;
}

export default function FilesIndex({ product, assets, storageStats }: Props) {
  const [selectedAssets, setSelectedAssets] = useState<number[]>([]);
  const [bulkAction, setBulkAction] = useState('');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (mimeType: string) => {
    // Return different icons based on mime type
    if (mimeType.startsWith('image/')) {
      return <File className="h-5 w-5 text-green-500" />;
    } else if (mimeType.startsWith('video/')) {
      return <File className="h-5 w-5 text-purple-500" />;
    } else if (mimeType === 'application/pdf') {
      return <File className="h-5 w-5 text-red-500" />;
    } else if (mimeType.includes('zip') || mimeType.includes('archive')) {
      return <File className="h-5 w-5 text-yellow-500" />;
    } else {
      return <File className="h-5 w-5 text-blue-500" />;
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedAssets(assets.data.map(asset => asset.id));
    } else {
      setSelectedAssets([]);
    }
  };

  const handleSelectAsset = (assetId: number, checked: boolean) => {
    if (checked) {
      setSelectedAssets([...selectedAssets, assetId]);
    } else {
      setSelectedAssets(selectedAssets.filter(id => id !== assetId));
    }
  };

  const handleBulkAction = () => {
    if (!bulkAction || selectedAssets.length === 0) return;

    router.post(route('admin.products.files.bulk-action', product.id), {
      action: bulkAction,
      asset_ids: selectedAssets,
    }, {
      onSuccess: () => {
        setSelectedAssets([]);
        setBulkAction('');
      },
    });
  };

  const handleDownload = (asset: DigitalAsset) => {
    router.get(route('admin.products.files.download', [product.id, asset.id]), {}, {
      onSuccess: (page) => {
        const response = page.props as { download_url?: string };
        if (response.download_url) {
          window.open(response.download_url, '_blank');
        }
      },
    });
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge className={isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  return (
    <AppLayout>
      <Head title={`Files - ${product.name}`} />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={route('admin.products.show', product.id)}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Product
              </Button>
            </Link>
            <Heading title="Digital Assets" description={`Manage files for ${product.name}`} />
          </div>
          <Link href={route('admin.products.files.create', product.id)}>
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Files
            </Button>
          </Link>
        </div>

        {/* Storage Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <HardDrive className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Total Files</p>
                  <p className="text-2xl font-bold">{storageStats.total_assets}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <File className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Active Files</p>
                  <p className="text-2xl font-bold">{storageStats.active_assets}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Download className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Total Size</p>
                  <p className="text-2xl font-bold">{storageStats.total_size_formatted}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Upload className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-sm font-medium leading-none">Product Files</p>
                  <p className="text-2xl font-bold">{assets.data.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Bulk Actions */}
        {selectedAssets.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  {selectedAssets.length} file(s) selected
                </span>
                <Select value={bulkAction} onValueChange={setBulkAction}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Bulk Actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="activate">Activate</SelectItem>
                    <SelectItem value="deactivate">Deactivate</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={handleBulkAction} disabled={!bulkAction}>
                  Apply
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Files Table */}
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="p-4 text-left">
                      <Checkbox
                        checked={selectedAssets.length === assets.data.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="p-4 text-left">File</th>
                    <th className="p-4 text-left">Size</th>
                    <th className="p-4 text-left">Type</th>
                    <th className="p-4 text-left">Downloads</th>
                    <th className="p-4 text-left">Status</th>
                    <th className="p-4 text-left">Uploaded</th>
                    <th className="p-4 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {assets.data.map(asset => (
                    <tr key={asset.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedAssets.includes(asset.id)}
                          onCheckedChange={(checked) => handleSelectAsset(asset.id, checked as boolean)}
                        />
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          {getFileIcon(asset.mime_type)}
                          <div>
                            <div className="font-medium">{asset.original_name}</div>
                            {asset.metadata?.description && (
                              <div className="text-sm text-gray-500">{asset.metadata.description}</div>
                            )}
                            {asset.metadata?.version && (
                              <Badge variant="outline" className="mt-1">v{asset.metadata.version}</Badge>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <span className="text-sm">{formatFileSize(asset.file_size)}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm font-mono">{asset.mime_type}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm">{asset.download_count}</span>
                      </td>
                      <td className="p-4">{getStatusBadge(asset.is_active)}</td>
                      <td className="p-4">
                        <span className="text-sm">{new Date(asset.created_at).toLocaleDateString()}</span>
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('admin.products.files.show', [product.id, asset.id])}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDownload(asset)}>
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this file?')) {
                                  router.delete(route('admin.products.files.destroy', [product.id, asset.id]));
                                }
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {assets.data.length === 0 && (
          <Card>
            <CardContent className="py-12 text-center">
              <File className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No files uploaded</h3>
              <p className="text-gray-500 mb-4">Get started by uploading your first digital asset.</p>
              <Link href={route('admin.products.files.create', product.id)}>
                <Button>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Files
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Pagination would go here */}
      </div>
    </AppLayout>
  );
}
