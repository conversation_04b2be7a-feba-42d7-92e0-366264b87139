import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import { formatCurrency } from '@/lib/utils';
import { Plus, ChevronsUpDown, MoreHorizontal } from 'lucide-react';

interface Product {
  id: number;
  name: string;
  slug: string;
  price: number;
  sale_price?: number;
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  sku: string;
  category?: {
    id: number;
    name: string;
  };
  creator?: {
    id: number;
    name: string;
  };
  files_count: number;
  order_items_count: number;
  created_at: string;
  updated_at: string;
}

interface Category {
  id: number;
  name: string;
}

interface Props {
  products: {
    data: Product[];
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    meta: {
      current_page: number;
      from: number;
      last_page: number;
      per_page: number;
      to: number;
      total: number;
    };
  };
  categories: Category[];
  filters: {
    search?: string;
    category?: string;
    status?: string;
    featured?: boolean;
    min_price?: number;
    max_price?: number;
  };
  sort: {
    by: string;
    order: string;
  };
}

export default function ProductsIndex({ products, categories, filters, sort }: Props) {
  const [selectedProducts, setSelectedProducts] = useState<number[]>([]);
  const [bulkAction, setBulkAction] = useState('');

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const searchParams = Object.fromEntries(formData.entries());

    // Convert "all" values back to empty strings for the backend
    if (searchParams.category === 'all') searchParams.category = '';
    if (searchParams.status === 'all') searchParams.status = '';

    router.get(route('admin.products.index'), searchParams, { preserveState: true });
  };

  const handleSort = (column: string) => {
    const newOrder = sort.by === column && sort.order === 'asc' ? 'desc' : 'asc';
    router.get(route('admin.products.index'), {
      ...filters,
      sort_by: column,
      sort_order: newOrder,
    }, { preserveState: true });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.data.map(product => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  const handleSelectProduct = (productId: number, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  const handleBulkAction = () => {
    if (!bulkAction || selectedProducts.length === 0) return;

    router.post(route('admin.products.bulk-action'), {
      action: bulkAction,
      product_ids: selectedProducts,
    }, {
      onSuccess: () => {
        setSelectedProducts([]);
        setBulkAction('');
      },
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      draft: 'bg-gray-100 text-gray-800',
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  return (
    <AppLayout>
      <Head title="Products" />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Heading title="Products" description="Manage your digital products" />
          <Link href={route('admin.products.create')}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Filter Products</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Input
                name="search"
                placeholder="Search products..."
                defaultValue={filters.search}
              />
              
              <Select name="category" defaultValue={filters.category || "all"}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select name="status" defaultValue={filters.status || "all"}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                </SelectContent>
              </Select>

              <Button type="submit">Filter</Button>
            </form>
          </CardContent>
        </Card>

        {selectedProducts.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  {selectedProducts.length} product(s) selected
                </span>
                <Select value={bulkAction} onValueChange={setBulkAction}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Bulk Actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="activate">Activate</SelectItem>
                    <SelectItem value="deactivate">Deactivate</SelectItem>
                    <SelectItem value="feature">Feature</SelectItem>
                    <SelectItem value="unfeature">Unfeature</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={handleBulkAction} disabled={!bulkAction}>
                  Apply
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="p-4 text-left">
                      <Checkbox
                        checked={selectedProducts.length === products.data.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="p-4 text-left">
                      <button
                        onClick={() => handleSort('name')}
                        className="flex items-center gap-2 hover:text-blue-600"
                      >
                        Name
                        <ChevronsUpDown className="h-4 w-4" />
                      </button>
                    </th>
                    <th className="p-4 text-left">Category</th>
                    <th className="p-4 text-left">
                      <button
                        onClick={() => handleSort('price')}
                        className="flex items-center gap-2 hover:text-blue-600"
                      >
                        Price
                        <ChevronsUpDown className="h-4 w-4" />
                      </button>
                    </th>
                    <th className="p-4 text-left">Status</th>
                    <th className="p-4 text-left">Files</th>
                    <th className="p-4 text-left">Sales</th>
                    <th className="p-4 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {products.data.map(product => (
                    <tr key={product.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedProducts.includes(product.id)}
                          onCheckedChange={(checked) => handleSelectProduct(product.id, checked as boolean)}
                        />
                      </td>
                      <td className="p-4">
                        <div>
                          <Link
                            href={route('admin.products.show', product.id)}
                            className="font-medium text-blue-600 hover:text-blue-800"
                          >
                            {product.name}
                          </Link>
                          <div className="text-sm text-gray-500">SKU: {product.sku}</div>
                          {product.featured && (
                            <Badge className="mt-1 bg-yellow-100 text-yellow-800">Featured</Badge>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        {product.category ? (
                          <span className="text-sm">{product.category.name}</span>
                        ) : (
                          <span className="text-sm text-gray-400">No category</span>
                        )}
                      </td>
                      <td className="p-4">
                        <div>
                          {product.sale_price ? (
                            <>
                              <span className="font-medium">{formatCurrency(product.sale_price)}</span>
                              <span className="ml-2 text-sm text-gray-500 line-through">
                                {formatCurrency(product.price)}
                              </span>
                            </>
                          ) : (
                            <span className="font-medium">{formatCurrency(product.price)}</span>
                          )}
                        </div>
                      </td>
                      <td className="p-4">{getStatusBadge(product.status)}</td>
                      <td className="p-4">
                        <span className="text-sm">{product.files_count}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-sm">{product.order_items_count}</span>
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('admin.products.show', product.id)}>
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={route('admin.products.edit', product.id)}>
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this product?')) {
                                  router.delete(route('admin.products.destroy', product.id));
                                }
                              }}
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Pagination would go here */}
      </div>
    </AppLayout>
  );
}
