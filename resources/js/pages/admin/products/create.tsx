import React from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import InputError from '@/components/input-error';
import { ArrowLeft } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  parent_id?: number;
}

interface Props {
  categories: Category[];
}

export default function CreateProduct({ categories }: Props) {
  const { data, setData, post, processing, errors } = useForm({
    name: '',
    category_id: '',
    description: '',
    short_description: '',
    price: '',
    sale_price: '',
    sku: '',
    status: 'draft',
    featured: false,
    digital: true,
    downloadable: true,
    download_limit: '',
    download_expiry_days: '',
    image: '',
    gallery: [],
    meta_title: '',
    meta_description: '',
    tags: [],
    attributes: {},
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    post(route('admin.products.store'));
  };

  const addTag = (tag: string) => {
    if (tag && !data.tags.includes(tag)) {
      setData('tags', [...data.tags, tag]);
    }
  };

  const removeTag = (index: number) => {
    setData('tags', data.tags.filter((_, i) => i !== index));
  };

  return (
    <AppLayout>
      <Head title="Create Product" />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Heading title="Create Product" description="Add a new digital product to your catalog" />
          <Link href={route('admin.products.index')}>
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Button>
          </Link>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Product Name *</Label>
                    <Input
                      id="name"
                      value={data.name}
                      onChange={(e) => setData('name', e.target.value)}
                      placeholder="Enter product name"
                    />
                    <InputError message={errors.name} />
                  </div>

                  <div>
                    <Label htmlFor="short_description">Short Description</Label>
                    <Textarea
                      id="short_description"
                      value={data.short_description}
                      onChange={(e) => setData('short_description', e.target.value)}
                      placeholder="Brief product description"
                      rows={3}
                    />
                    <InputError message={errors.short_description} />
                  </div>

                  <div>
                    <Label htmlFor="description">Full Description</Label>
                    <Textarea
                      id="description"
                      value={data.description}
                      onChange={(e) => setData('description', e.target.value)}
                      placeholder="Detailed product description"
                      rows={6}
                    />
                    <InputError message={errors.description} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Pricing</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="price">Regular Price *</Label>
                      <Input
                        id="price"
                        type="number"
                        step="0.01"
                        min="0"
                        value={data.price}
                        onChange={(e) => setData('price', e.target.value)}
                        placeholder="0.00"
                      />
                      <InputError message={errors.price} />
                    </div>

                    <div>
                      <Label htmlFor="sale_price">Sale Price</Label>
                      <Input
                        id="sale_price"
                        type="number"
                        step="0.01"
                        min="0"
                        value={data.sale_price}
                        onChange={(e) => setData('sale_price', e.target.value)}
                        placeholder="0.00"
                      />
                      <InputError message={errors.sale_price} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Download Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="downloadable"
                      checked={data.downloadable}
                      onCheckedChange={(checked) => setData('downloadable', checked)}
                    />
                    <Label htmlFor="downloadable">Downloadable Product</Label>
                  </div>

                  {data.downloadable && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="download_limit">Download Limit</Label>
                        <Input
                          id="download_limit"
                          type="number"
                          min="1"
                          value={data.download_limit}
                          onChange={(e) => setData('download_limit', e.target.value)}
                          placeholder="Unlimited"
                        />
                        <InputError message={errors.download_limit} />
                      </div>

                      <div>
                        <Label htmlFor="download_expiry_days">Download Expiry (Days)</Label>
                        <Input
                          id="download_expiry_days"
                          type="number"
                          min="1"
                          value={data.download_expiry_days}
                          onChange={(e) => setData('download_expiry_days', e.target.value)}
                          placeholder="Never expires"
                        />
                        <InputError message={errors.download_expiry_days} />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>SEO Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="meta_title">Meta Title</Label>
                    <Input
                      id="meta_title"
                      value={data.meta_title}
                      onChange={(e) => setData('meta_title', e.target.value)}
                      placeholder="SEO title"
                    />
                    <InputError message={errors.meta_title} />
                  </div>

                  <div>
                    <Label htmlFor="meta_description">Meta Description</Label>
                    <Textarea
                      id="meta_description"
                      value={data.meta_description}
                      onChange={(e) => setData('meta_description', e.target.value)}
                      placeholder="SEO description"
                      rows={3}
                    />
                    <InputError message={errors.meta_description} />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Publish</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={data.status} onValueChange={(value) => setData('status', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <InputError message={errors.status} />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="featured"
                      checked={data.featured}
                      onCheckedChange={(checked) => setData('featured', checked)}
                    />
                    <Label htmlFor="featured">Featured Product</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="digital"
                      checked={data.digital}
                      onCheckedChange={(checked) => setData('digital', checked)}
                    />
                    <Label htmlFor="digital">Digital Product</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Product Data</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="sku">SKU</Label>
                    <Input
                      id="sku"
                      value={data.sku}
                      onChange={(e) => setData('sku', e.target.value)}
                      placeholder="Auto-generated if empty"
                    />
                    <InputError message={errors.sku} />
                  </div>

                  <div>
                    <Label htmlFor="category_id">Category</Label>
                    <Select value={data.category_id || "none"} onValueChange={(value) => setData('category_id', value === "none" ? "" : value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Category</SelectItem>
                        {categories.map(category => (
                          <SelectItem key={category.id} value={category.id.toString()}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <InputError message={errors.category_id} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Product Tags</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {data.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => removeTag(index)}
                            className="ml-1 text-blue-600 hover:text-blue-800"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                    <Input
                      placeholder="Add tag and press Enter"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag(e.currentTarget.value);
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="flex items-center justify-end space-x-4">
            <Link href={route('admin.products.index')}>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </Link>
            <Button type="submit" disabled={processing}>
              {processing ? 'Creating...' : 'Create Product'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
}
