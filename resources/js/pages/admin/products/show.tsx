import React from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import { formatCurrency } from '@/lib/utils';
import { ProductRatingDisplay } from '@/components/products/product-rating-display';
import { ArrowLeft, Edit, Package, FileText, Download, Users, Star } from 'lucide-react';

interface Product {
  id: number;
  uuid: string;
  name: string;
  slug: string;
  description?: string;
  short_description?: string;
  price: number;
  sale_price?: number;
  sku: string;
  status: 'active' | 'inactive' | 'draft';
  featured: boolean;
  digital: boolean;
  downloadable: boolean;
  download_limit?: number;
  download_expiry_days?: number;
  average_rating: number;
  total_reviews: number;
  total_ratings: number;
  rating_breakdown: Record<number, number>;
  reviews_enabled: boolean;
  image?: string;
  gallery?: string[];
  meta_title?: string;
  meta_description?: string;
  tags?: string[];
  attributes?: Record<string, unknown>;
  category?: {
    id: number;
    name: string;
    slug: string;
  };
  creator?: {
    id: number;
    name: string;
    email: string;
  };
  files?: Array<{
    id: number;
    original_name: string;
    file_name: string;
    file_size: number;
    mime_type: string;
    download_count: number;
    is_active: boolean;
    created_at: string;
  }>;
  order_items?: Array<{
    id: number;
    quantity: number;
    unit_price: number;
    total_price: number;
    order: {
      id: number;
      order_number: string;
      status: string;
      created_at: string;
    };
  }>;
  created_at: string;
  updated_at: string;
}

interface Props {
  product: Product;
}

export default function ShowProduct({ product }: Props) {
  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-red-100 text-red-800',
      draft: 'bg-gray-100 text-gray-800',
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <AppLayout>
      <Head title={`Product: ${product.name}`} />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={route('admin.products.index')}>
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Products
              </Button>
            </Link>
            <Heading title={product.name} description={`SKU: ${product.sku}`} />
          </div>
          <div className="flex items-center space-x-2">
            <Link href={route('admin.products.files.index', product.id)}>
              <Button variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                Manage Files
              </Button>
            </Link>
            <Link href={route('admin.products.edit', product.id)}>
              <Button>
                <Edit className="mr-2 h-4 w-4" />
                Edit Product
              </Button>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="mr-2 h-5 w-5" />
                  Product Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-500">Status</label>
                    <div className="mt-1">{getStatusBadge(product.status)}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Category</label>
                    <div className="mt-1">
                      {product.category ? (
                        <span className="text-sm">{product.category.name}</span>
                      ) : (
                        <span className="text-sm text-gray-400">No category</span>
                      )}
                    </div>
                  </div>
                </div>

                {product.short_description && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Short Description</label>
                    <p className="mt-1 text-sm">{product.short_description}</p>
                  </div>
                )}

                {product.description && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Description</label>
                    <div className="mt-1 text-sm whitespace-pre-wrap">{product.description}</div>
                  </div>
                )}

                {product.tags && product.tags.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Tags</label>
                    <div className="mt-1 flex flex-wrap gap-2">
                      {product.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {product.files && product.files.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5" />
                    Product Files ({product.files.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {product.files.map((file) => (
                      <div key={file.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium">{file.original_name}</div>
                          <div className="text-sm text-gray-500">
                            {formatFileSize(file.file_size)} • {file.mime_type}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-sm text-gray-500">
                            <Download className="inline h-4 w-4 mr-1" />
                            {file.download_count} downloads
                          </div>
                          <Badge variant={file.is_active ? "default" : "secondary"}>
                            {file.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Product Reviews & Ratings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="mr-2 h-5 w-5" />
                  Reviews & Ratings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ProductRatingDisplay
                  stats={{
                    average_rating: product.average_rating,
                    total_reviews: product.total_reviews,
                    total_ratings: product.total_ratings,
                    rating_breakdown: product.rating_breakdown || {},
                    rating_breakdown_percentages: {},
                    reviews_enabled: product.reviews_enabled
                  }}
                  showBreakdown={true}
                  size="md"
                />

                {product.total_reviews > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <Link href={route('admin.reviews.index', { product_id: product.id })}>
                      <Button variant="outline" size="sm">
                        Manage Reviews
                      </Button>
                    </Link>
                  </div>
                )}
              </CardContent>
            </Card>

            {product.order_items && product.order_items.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="mr-2 h-5 w-5" />
                    Recent Orders ({product.order_items.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {product.order_items.slice(0, 5).map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">Order #{item.order.order_number}</div>
                          <div className="text-sm text-gray-500">
                            {new Date(item.order.created_at).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(item.total_price)}</div>
                          <div className="text-sm text-gray-500">Qty: {item.quantity}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Pricing</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Regular Price</label>
                  <div className="mt-1 text-2xl font-bold">{formatCurrency(product.price)}</div>
                </div>
                
                {product.sale_price && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Sale Price</label>
                    <div className="mt-1 text-2xl font-bold text-green-600">
                      {formatCurrency(product.sale_price)}
                    </div>
                    <div className="text-sm text-gray-500">
                      Save {formatCurrency(product.price - product.sale_price)} 
                      ({Math.round(((product.price - product.sale_price) / product.price) * 100)}% off)
                    </div>
                  </div>
                )}

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Featured</span>
                    <Badge variant={product.featured ? "default" : "secondary"}>
                      {product.featured ? "Yes" : "No"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Digital</span>
                    <Badge variant={product.digital ? "default" : "secondary"}>
                      {product.digital ? "Yes" : "No"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Downloadable</span>
                    <Badge variant={product.downloadable ? "default" : "secondary"}>
                      {product.downloadable ? "Yes" : "No"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {product.downloadable && (
              <Card>
                <CardHeader>
                  <CardTitle>Download Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Download Limit</span>
                    <span className="text-sm font-medium">
                      {product.download_limit || "Unlimited"}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">Expiry Days</span>
                    <span className="text-sm font-medium">
                      {product.download_expiry_days || "Never expires"}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Metadata</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-500">Created By</label>
                  <div className="mt-1 text-sm">
                    {product.creator ? product.creator.name : "Unknown"}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Created At</label>
                  <div className="mt-1 text-sm">
                    {new Date(product.created_at).toLocaleString()}
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Last Updated</label>
                  <div className="mt-1 text-sm">
                    {new Date(product.updated_at).toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
