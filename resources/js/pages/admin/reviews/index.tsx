import React from 'react';
import { <PERSON>, <PERSON>, router, usePage } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { StarRating } from '@/components/reviews/star-rating';
import { 
    Search, 
    Filter, 
    CheckCircle, 
    XCircle, 
    Star,
    Shield,
    Eye,
    MoreHorizontal,
    ThumbsUp,
    ThumbsDown
} from 'lucide-react';
import { useState } from 'react';

interface Review {
    id: number;
    uuid: string;
    title: string;
    content: string;
    rating: number;
    status: 'pending' | 'approved' | 'rejected';
    is_verified_purchase: boolean;
    is_featured: boolean;
    created_at: string;
    product: {
        id: number;
        name: string;
        slug: string;
    };
    user: {
        id: number;
        name: string;
        email: string;
    };
    helpful_votes_count: number;
}

interface Product {
    id: number;
    name: string;
}

interface Props {
    reviews: {
        data: Review[];
        links: any[];
        meta: any;
    };
    products: Product[];
    filters: {
        search?: string;
        status?: string;
        rating?: string;
        verified_only?: boolean;
        product_id?: string;
    };
    sort: {
        by: string;
        order: string;
    };
}

export default function ReviewsIndex({ reviews, products, filters, sort }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [selectedStatus, setSelectedStatus] = useState(filters.status || 'all');
    const [selectedRating, setSelectedRating] = useState(filters.rating || 'all');
    const [selectedProduct, setSelectedProduct] = useState(filters.product_id || 'all');
    const [verifiedOnly, setVerifiedOnly] = useState(filters.verified_only || false);

    const handleSearch = () => {
        router.get(route('admin.reviews.index'), {
            search,
            status: selectedStatus === 'all' ? '' : selectedStatus,
            rating: selectedRating === 'all' ? '' : selectedRating,
            product_id: selectedProduct === 'all' ? '' : selectedProduct,
            verified_only: verifiedOnly,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleApprove = (reviewId: number) => {
        router.post(route('admin.reviews.approve', reviewId), {}, {
            preserveScroll: true,
        });
    };

    const handleReject = (reviewId: number) => {
        router.post(route('admin.reviews.reject', reviewId), {
            admin_notes: 'Rejected by admin'
        }, {
            preserveScroll: true,
        });
    };

    const getStatusBadge = (status: string) => {
        const variants = {
            pending: 'bg-yellow-100 text-yellow-800',
            approved: 'bg-green-100 text-green-800',
            rejected: 'bg-red-100 text-red-800',
        };
        return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
    };

    const truncateContent = (content: string, maxLength: number = 100) => {
        return content.length > maxLength ? content.substring(0, maxLength) + '...' : content;
    };

    return (
        <AppLayout>
            <Head title="Product Reviews" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Product Reviews</h1>
                        <p className="text-gray-600">Manage customer product reviews and ratings</p>
                    </div>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-5 w-5" />
                            Filters
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div>
                                <Input
                                    placeholder="Search reviews..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </div>
                            
                            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="approved">Approved</SelectItem>
                                    <SelectItem value="rejected">Rejected</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={selectedRating} onValueChange={setSelectedRating}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Rating" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Ratings</SelectItem>
                                    <SelectItem value="5">5 Stars</SelectItem>
                                    <SelectItem value="4">4 Stars</SelectItem>
                                    <SelectItem value="3">3 Stars</SelectItem>
                                    <SelectItem value="2">2 Stars</SelectItem>
                                    <SelectItem value="1">1 Star</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={selectedProduct} onValueChange={setSelectedProduct}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Product" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Products</SelectItem>
                                    {products.map((product) => (
                                        <SelectItem key={product.id} value={product.id.toString()}>
                                            {product.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Button onClick={handleSearch} className="w-full">
                                <Search className="h-4 w-4 mr-2" />
                                Search
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Reviews List */}
                <Card>
                    <CardHeader>
                        <CardTitle>Reviews ({reviews.meta.total})</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {reviews.data.map((review) => (
                                <div key={review.id} className="border rounded-lg p-4 space-y-3">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center gap-2 mb-2">
                                                <StarRating rating={review.rating} size="sm" />
                                                {getStatusBadge(review.status)}
                                                {review.is_verified_purchase && (
                                                    <Badge variant="secondary" className="text-xs">
                                                        <Shield className="h-3 w-3 mr-1" />
                                                        Verified
                                                    </Badge>
                                                )}
                                                {review.is_featured && (
                                                    <Badge variant="default" className="text-xs">
                                                        <Star className="h-3 w-3 mr-1" />
                                                        Featured
                                                    </Badge>
                                                )}
                                            </div>
                                            
                                            <h4 className="font-semibold text-gray-900 mb-1">
                                                {review.title}
                                            </h4>
                                            
                                            <p className="text-sm text-gray-600 mb-2">
                                                {truncateContent(review.content)}
                                            </p>
                                            
                                            <div className="flex items-center gap-4 text-xs text-gray-500">
                                                <span>by {review.user.name}</span>
                                                <span>for {review.product.name}</span>
                                                <span>{new Date(review.created_at).toLocaleDateString()}</span>
                                                <span className="flex items-center gap-1">
                                                    <ThumbsUp className="h-3 w-3" />
                                                    {review.helpful_votes_count}
                                                </span>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            {review.status === 'pending' && (
                                                <>
                                                    <Button
                                                        size="sm"
                                                        onClick={() => handleApprove(review.id)}
                                                        className="bg-green-600 hover:bg-green-700"
                                                    >
                                                        <CheckCircle className="h-4 w-4 mr-1" />
                                                        Approve
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        variant="destructive"
                                                        onClick={() => handleReject(review.id)}
                                                    >
                                                        <XCircle className="h-4 w-4 mr-1" />
                                                        Reject
                                                    </Button>
                                                </>
                                            )}
                                            
                                            <Link href={route('admin.reviews.show', review.id)}>
                                                <Button size="sm" variant="outline">
                                                    <Eye className="h-4 w-4 mr-1" />
                                                    View
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>

                        {reviews.data.length === 0 && (
                            <div className="text-center py-8">
                                <p className="text-gray-500">No reviews found matching your criteria.</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
