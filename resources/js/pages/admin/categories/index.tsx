import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppLayout from '@/layouts/app-layout';
import Heading from '@/components/heading';
import { Plus, ChevronsUpDown, MoreHorizontal, FolderTree } from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  parent_id?: number;
  sort_order: number;
  is_active: boolean;
  products_count: number;
  parent?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

interface Props {
  categories: {
    data: Category[];
    links: Array<{
      url: string | null;
      label: string;
      active: boolean;
    }>;
    meta: {
      current_page: number;
      from: number;
      last_page: number;
      per_page: number;
      to: number;
      total: number;
    };
  };
  parentCategories: Array<{
    id: number;
    name: string;
  }>;
  filters: {
    search?: string;
    parent?: string;
    status?: boolean;
  };
  sort: {
    by: string;
    order: string;
  };
}

export default function CategoriesIndex({ categories, parentCategories, filters, sort }: Props) {
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [bulkAction, setBulkAction] = useState('');

  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const searchParams = Object.fromEntries(formData.entries());

    // Convert "all" values back to empty strings for the backend
    if (searchParams.parent === 'all') searchParams.parent = '';
    if (searchParams.status === 'all') searchParams.status = '';

    router.get(route('admin.categories.index'), searchParams, { preserveState: true });
  };

  const handleSort = (column: string) => {
    const newOrder = sort.by === column && sort.order === 'asc' ? 'desc' : 'asc';
    router.get(route('admin.categories.index'), {
      ...filters,
      sort_by: column,
      sort_order: newOrder,
    }, { preserveState: true });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCategories(categories.data.map(category => category.id));
    } else {
      setSelectedCategories([]);
    }
  };

  const handleSelectCategory = (categoryId: number, checked: boolean) => {
    if (checked) {
      setSelectedCategories([...selectedCategories, categoryId]);
    } else {
      setSelectedCategories(selectedCategories.filter(id => id !== categoryId));
    }
  };

  const handleBulkAction = () => {
    if (!bulkAction || selectedCategories.length === 0) return;

    router.post(route('admin.categories.bulk-action'), {
      action: bulkAction,
      category_ids: selectedCategories,
    }, {
      onSuccess: () => {
        setSelectedCategories([]);
        setBulkAction('');
      },
    });
  };

  const getStatusBadge = (isActive: boolean) => {
    return (
      <Badge className={isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
        {isActive ? 'Active' : 'Inactive'}
      </Badge>
    );
  };

  return (
    <AppLayout>
      <Head title="Categories" />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Heading title="Categories" description="Manage product categories and hierarchy" />
          <Link href={route('admin.categories.create')}>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </Link>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Filter Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Input
                name="search"
                placeholder="Search categories..."
                defaultValue={filters.search}
              />
              
              <Select name="parent" defaultValue={filters.parent || "all"}>
                <SelectTrigger>
                  <SelectValue placeholder="All Categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="root">Root Categories</SelectItem>
                  {parentCategories.map(category => (
                    <SelectItem key={category.id} value={category.id.toString()}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select name="status" defaultValue={filters.status?.toString() || "all"}>
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="true">Active</SelectItem>
                  <SelectItem value="false">Inactive</SelectItem>
                </SelectContent>
              </Select>

              <Button type="submit">Filter</Button>
            </form>
          </CardContent>
        </Card>

        {selectedCategories.length > 0 && (
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-4">
                <span className="text-sm text-gray-600">
                  {selectedCategories.length} category(ies) selected
                </span>
                <Select value={bulkAction} onValueChange={setBulkAction}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Bulk Actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="activate">Activate</SelectItem>
                    <SelectItem value="deactivate">Deactivate</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={handleBulkAction} disabled={!bulkAction}>
                  Apply
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr>
                    <th className="p-4 text-left">
                      <Checkbox
                        checked={selectedCategories.length === categories.data.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="p-4 text-left">
                      <button
                        onClick={() => handleSort('name')}
                        className="flex items-center gap-2 hover:text-blue-600"
                      >
                        Name
                        <ChevronsUpDown className="h-4 w-4" />
                      </button>
                    </th>
                    <th className="p-4 text-left">Parent</th>
                    <th className="p-4 text-left">
                      <button
                        onClick={() => handleSort('sort_order')}
                        className="flex items-center gap-2 hover:text-blue-600"
                      >
                        Sort Order
                        <ChevronsUpDown className="h-4 w-4" />
                      </button>
                    </th>
                    <th className="p-4 text-left">Status</th>
                    <th className="p-4 text-left">Products</th>
                    <th className="p-4 text-left">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {categories.data.map(category => (
                    <tr key={category.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedCategories.includes(category.id)}
                          onCheckedChange={(checked) => handleSelectCategory(category.id, checked as boolean)}
                        />
                      </td>
                      <td className="p-4">
                        <div className="flex items-center">
                          {category.parent_id && (
                            <div className="mr-2 text-gray-400">
                              <FolderTree className="h-4 w-4" />
                            </div>
                          )}
                          <div>
                            <Link
                              href={route('admin.categories.show', category.id)}
                              className="font-medium text-blue-600 hover:text-blue-800"
                            >
                              {category.name}
                            </Link>
                            {category.description && (
                              <div className="text-sm text-gray-500 mt-1">
                                {category.description.substring(0, 60)}
                                {category.description.length > 60 && '...'}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        {category.parent ? (
                          <span className="text-sm">{category.parent.name}</span>
                        ) : (
                          <span className="text-sm text-gray-400">Root Category</span>
                        )}
                      </td>
                      <td className="p-4">
                        <span className="text-sm font-mono">{category.sort_order}</span>
                      </td>
                      <td className="p-4">{getStatusBadge(category.is_active)}</td>
                      <td className="p-4">
                        <span className="text-sm">{category.products_count}</span>
                      </td>
                      <td className="p-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={route('admin.categories.show', category.id)}>
                                View
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={route('admin.categories.edit', category.id)}>
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => {
                                if (confirm('Are you sure you want to delete this category?')) {
                                  router.delete(route('admin.categories.destroy', category.id));
                                }
                              }}
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Pagination would go here */}
      </div>
    </AppLayout>
  );
}
