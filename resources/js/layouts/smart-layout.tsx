import { usePage } from '@inertiajs/react';
import { type BreadcrumbItem, type SharedData, type User } from '@/types';
import { type ReactNode } from 'react';

import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import AdminLayout from '@/layouts/role-based/admin-layout';
import UserLayout from '@/layouts/role-based/user-layout';
import GuestLayout from '@/layouts/role-based/guest-layout';

interface Role {
    id: number;
    name: string;
    display_name?: string;
}

interface UserWithRoles extends User {
    roles?: Role[];
}

interface SmartLayoutProps {
    children: ReactNode;
    breadcrumbs?: BreadcrumbItem[];
}

export default function SmartLayout({ children, breadcrumbs, ...props }: SmartLayoutProps) {
    const { auth } = usePage<SharedData>().props;
    const user = auth?.user as UserWithRoles;

    // Determine user role
    const getUserRole = () => {
        if (!user) return 'guest';
        
        const roles = user.roles || [];
        
        if (roles.some((role: Role) => role.name === 'super_admin')) {
            return 'super_admin';
        }

        if (roles.some((role: Role) => role.name === 'admin')) {
            return 'admin';
        }

        if (roles.some((role: Role) => role.name === 'manager')) {
            return 'admin'; // Managers use admin layout
        }
        
        return 'user';
    };

    const userRole = getUserRole();

    // Select appropriate layout based on role
    switch (userRole) {
        case 'super_admin':
            return (
                <SuperAdminLayout breadcrumbs={breadcrumbs} {...props}>
                    {children}
                </SuperAdminLayout>
            );
        
        case 'admin':
            return (
                <AdminLayout breadcrumbs={breadcrumbs} {...props}>
                    {children}
                </AdminLayout>
            );
        
        case 'user':
            return (
                <UserLayout breadcrumbs={breadcrumbs} {...props}>
                    {children}
                </UserLayout>
            );
        
        case 'guest':
        default:
            return (
                <GuestLayout breadcrumbs={breadcrumbs} {...props}>
                    {children}
                </GuestLayout>
            );
    }
}
