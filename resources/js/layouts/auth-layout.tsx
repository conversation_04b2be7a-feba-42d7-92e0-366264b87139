import AppLogoIcon from '@/components/app-logo-icon';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

interface AuthLayoutProps {
    name?: string;
    title?: string;
    description?: string;
}

export default function AuthLayout({ children, title, description }: PropsWithChildren<AuthLayoutProps>) {
    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-40">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-50/20 to-purple-50/20 dark:from-blue-900/10 dark:to-purple-900/10"></div>
                <div 
                    className="absolute inset-0" 
                    style={{
                        backgroundImage: 'radial-gradient(circle at 1px 1px, rgba(148, 163, 184, 0.3) 1px, transparent 0)',
                        backgroundSize: '20px 20px'
                    }}
                ></div>
            </div>
            
            <div className="relative flex min-h-screen items-center justify-center p-4 sm:p-6 lg:p-8">
                <div className="w-full max-w-md">
                    {/* Main Card */}
                    <div className="relative overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm shadow-xl shadow-slate-200/50 ring-1 ring-slate-200/50 dark:bg-slate-800/80 dark:shadow-slate-900/50 dark:ring-slate-700/50">
                        {/* Header Section */}
                        <div className="px-8 pt-8 pb-6">
                            <div className="flex flex-col items-center space-y-6">
                                {/* Logo */}
                                <Link 
                                    href={route('home')} 
                                    className="group flex items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 p-3 shadow-lg shadow-blue-500/25 transition-all duration-200 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105"
                                >
                                    <AppLogoIcon className="h-8 w-8 text-white" />
                                </Link>

                                {/* Title and Description */}
                                <div className="space-y-2 text-center">
                                    <h1 className="text-2xl font-bold tracking-tight text-slate-900 dark:text-white">
                                        {title}
                                    </h1>
                                    <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                        {description}
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* Form Section */}
                        <div className="px-8 pb-8">
                            {children}
                        </div>

                        {/* Decorative Elements */}
                        <div className="absolute top-0 left-0 h-1 w-full bg-gradient-to-r from-blue-500 via-purple-500 to-blue-600"></div>
                    </div>

                    {/* Footer */}
                    <div className="mt-8 text-center">
                        <p className="text-xs text-slate-500 dark:text-slate-400">
                            Protected by industry-standard security measures
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
