import SuperAdminLayout from '@/layouts/role-based/super-admin-layout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    Construction, 
    ArrowLeft, 
    Settings, 
    Database,
    FileText,
    Users,
    Package,
    BarChart3
} from 'lucide-react';

interface PlaceholderPageProps {
    title: string;
    description?: string;
    icon?: 'construction' | 'settings' | 'database' | 'filetext' | 'users' | 'package' | 'barchart';
    features?: string[];
    backUrl?: string;
    backLabel?: string;
}

export default function PlaceholderPage({ 
    title, 
    description, 
    icon = 'construction',
    features = [],
    backUrl,
    backLabel = 'Back'
}: PlaceholderPageProps) {
    
    const getIcon = () => {
        const iconClass = "h-12 w-12 text-muted-foreground";
        switch (icon) {
            case 'settings':
                return <Settings className={iconClass} />;
            case 'database':
                return <Database className={iconClass} />;
            case 'filetext':
                return <FileText className={iconClass} />;
            case 'users':
                return <Users className={iconClass} />;
            case 'package':
                return <Package className={iconClass} />;
            case 'barchart':
                return <BarChart3 className={iconClass} />;
            default:
                return <Construction className={iconClass} />;
        }
    };

    return (
        <SuperAdminLayout>
            <Head title={title} />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
                        {description && (
                            <p className="text-muted-foreground">{description}</p>
                        )}
                    </div>
                    {backUrl && (
                        <Button variant="outline" onClick={() => window.history.back()}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            {backLabel}
                        </Button>
                    )}
                </div>

                {/* Main Content */}
                <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
                    {/* Status Card */}
                    <Card className="lg:col-span-2">
                        <CardHeader className="text-center">
                            <div className="flex justify-center mb-4">
                                {getIcon()}
                            </div>
                            <CardTitle className="text-2xl">Page Under Development</CardTitle>
                            <CardDescription className="text-base">
                                This {title.toLowerCase()} page is currently being developed and will be available soon.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="text-center space-y-4">
                            <Badge variant="outline" className="text-sm px-3 py-1">
                                Coming Soon
                            </Badge>
                            <p className="text-sm text-muted-foreground max-w-md mx-auto">
                                We're working hard to bring you this feature. Check back soon for updates, 
                                or contact your system administrator for more information.
                            </p>
                        </CardContent>
                    </Card>

                    {/* Features Card */}
                    {features.length > 0 && (
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Planned Features</CardTitle>
                                <CardDescription>
                                    What to expect when this page is ready
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <ul className="space-y-2">
                                    {features.map((feature, index) => (
                                        <li key={index} className="flex items-start gap-2 text-sm">
                                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                                            <span>{feature}</span>
                                        </li>
                                    ))}
                                </ul>
                            </CardContent>
                        </Card>
                    )}
                </div>

                {/* Additional Info */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg">Development Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-3">
                            <div className="text-center p-4 border rounded-lg">
                                <div className="text-2xl font-bold text-blue-600">Planning</div>
                                <div className="text-sm text-muted-foreground">Requirements gathering</div>
                            </div>
                            <div className="text-center p-4 border rounded-lg bg-blue-50">
                                <div className="text-2xl font-bold text-blue-600">In Progress</div>
                                <div className="text-sm text-muted-foreground">Currently developing</div>
                            </div>
                            <div className="text-center p-4 border rounded-lg">
                                <div className="text-2xl font-bold text-gray-400">Testing</div>
                                <div className="text-sm text-muted-foreground">Quality assurance</div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </SuperAdminLayout>
    );
}
