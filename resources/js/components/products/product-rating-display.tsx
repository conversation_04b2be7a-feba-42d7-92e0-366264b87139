import React from 'react';
import { Star, TrendingUp, Users } from 'lucide-react';
import { StarRating, RatingBreakdown, RatingSummary } from '@/components/reviews/star-rating';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface ProductRatingStats {
    average_rating: number;
    total_reviews: number;
    total_ratings: number;
    rating_breakdown: Record<number, number>;
    rating_breakdown_percentages: Record<number, number>;
    reviews_enabled: boolean;
}

interface ProductRatingDisplayProps {
    stats: ProductRatingStats;
    showBreakdown?: boolean;
    showSummary?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export function ProductRatingDisplay({
    stats,
    showBreakdown = false,
    showSummary = false,
    size = 'md',
    className
}: ProductRatingDisplayProps) {
    if (!stats.reviews_enabled || stats.total_reviews === 0) {
        return (
            <div className={cn('text-gray-500 text-sm', className)}>
                No reviews yet
            </div>
        );
    }

    const getRatingDescription = (rating: number) => {
        if (rating >= 4.5) return 'Excellent';
        if (rating >= 4.0) return 'Very Good';
        if (rating >= 3.5) return 'Good';
        if (rating >= 3.0) return 'Average';
        if (rating >= 2.0) return 'Below Average';
        return 'Poor';
    };

    const getRatingColor = (rating: number) => {
        if (rating >= 4.0) return 'text-green-600';
        if (rating >= 3.0) return 'text-yellow-600';
        return 'text-red-600';
    };

    if (showSummary) {
        return (
            <RatingSummary
                averageRating={stats.average_rating}
                totalReviews={stats.total_reviews}
                ratingBreakdown={stats.rating_breakdown}
                className={className}
            />
        );
    }

    return (
        <div className={cn('flex items-center gap-2', className)}>
            <StarRating 
                rating={stats.average_rating} 
                size={size}
                showValue={false}
            />
            
            <div className="flex items-center gap-2 text-sm">
                <span className={cn('font-medium', getRatingColor(stats.average_rating))}>
                    {stats.average_rating.toFixed(1)}
                </span>
                
                <span className="text-gray-500">
                    ({stats.total_reviews} {stats.total_reviews === 1 ? 'review' : 'reviews'})
                </span>
                
                {stats.average_rating >= 4.5 && (
                    <Badge variant="secondary" className="text-xs">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Top Rated
                    </Badge>
                )}
            </div>

            {showBreakdown && (
                <div className="ml-4">
                    <RatingBreakdown
                        ratingBreakdown={stats.rating_breakdown}
                        totalReviews={stats.total_reviews}
                    />
                </div>
            )}
        </div>
    );
}

interface ProductRatingCardProps {
    stats: ProductRatingStats;
    productName?: string;
    className?: string;
}

export function ProductRatingCard({ stats, productName, className }: ProductRatingCardProps) {
    if (!stats.reviews_enabled) {
        return (
            <Card className={cn('', className)}>
                <CardContent className="pt-6">
                    <div className="text-center text-gray-500">
                        Reviews are disabled for this product
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (stats.total_reviews === 0) {
        return (
            <Card className={cn('', className)}>
                <CardContent className="pt-6">
                    <div className="text-center space-y-2">
                        <div className="text-gray-500">No reviews yet</div>
                        <div className="text-sm text-gray-400">
                            Be the first to review {productName && `"${productName}"`}
                        </div>
                    </div>
                </CardContent>
            </Card>
        );
    }

    const getRatingDescription = (rating: number) => {
        if (rating >= 4.5) return 'Excellent';
        if (rating >= 4.0) return 'Very Good';
        if (rating >= 3.5) return 'Good';
        if (rating >= 3.0) return 'Average';
        if (rating >= 2.0) return 'Below Average';
        return 'Poor';
    };

    return (
        <Card className={cn('', className)}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    Customer Reviews
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
                {/* Overall Rating */}
                <div className="flex items-center gap-6">
                    <div className="text-center">
                        <div className="text-4xl font-bold text-gray-900 mb-1">
                            {stats.average_rating.toFixed(1)}
                        </div>
                        <StarRating rating={stats.average_rating} size="lg" className="mb-2" />
                        <div className="text-sm text-gray-600">
                            {getRatingDescription(stats.average_rating)}
                        </div>
                    </div>
                    
                    <div className="flex-1">
                        <div className="text-sm text-gray-600 mb-3">
                            Based on {stats.total_reviews} {stats.total_reviews === 1 ? 'review' : 'reviews'}
                        </div>
                        <RatingBreakdown 
                            ratingBreakdown={stats.rating_breakdown}
                            totalReviews={stats.total_reviews}
                        />
                    </div>
                </div>

                {/* Rating Statistics */}
                <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">
                            {Math.round(stats.rating_breakdown_percentages[5] + stats.rating_breakdown_percentages[4])}%
                        </div>
                        <div className="text-xs text-gray-500">Positive</div>
                    </div>
                    
                    <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">
                            {Math.round(stats.rating_breakdown_percentages[3])}%
                        </div>
                        <div className="text-xs text-gray-500">Neutral</div>
                    </div>
                    
                    <div className="text-center">
                        <div className="text-2xl font-bold text-red-600">
                            {Math.round(stats.rating_breakdown_percentages[2] + stats.rating_breakdown_percentages[1])}%
                        </div>
                        <div className="text-xs text-gray-500">Negative</div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

interface CompactRatingDisplayProps {
    stats: ProductRatingStats;
    showCount?: boolean;
    className?: string;
}

export function CompactRatingDisplay({ 
    stats, 
    showCount = true, 
    className 
}: CompactRatingDisplayProps) {
    if (!stats.reviews_enabled || stats.total_reviews === 0) {
        return (
            <div className={cn('text-gray-400 text-sm', className)}>
                No reviews
            </div>
        );
    }

    return (
        <div className={cn('flex items-center gap-1', className)}>
            <StarRating rating={stats.average_rating} size="sm" />
            <span className="text-sm font-medium text-gray-700">
                {stats.average_rating.toFixed(1)}
            </span>
            {showCount && (
                <span className="text-xs text-gray-500">
                    ({stats.total_reviews})
                </span>
            )}
        </div>
    );
}

interface RatingTrendProps {
    trends: Array<{
        date: string;
        rating: number;
        average_so_far: number;
    }>;
    className?: string;
}

export function RatingTrend({ trends, className }: RatingTrendProps) {
    if (trends.length === 0) {
        return (
            <div className={cn('text-gray-500 text-sm', className)}>
                No rating trends available
            </div>
        );
    }

    const latestAverage = trends[trends.length - 1]?.average_so_far || 0;
    const firstAverage = trends[0]?.average_so_far || 0;
    const trend = latestAverage - firstAverage;

    return (
        <Card className={cn('', className)}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Rating Trend
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex items-center gap-4 mb-4">
                    <div className="text-2xl font-bold">
                        {latestAverage.toFixed(1)}
                    </div>
                    <div className={cn(
                        'flex items-center gap-1 text-sm',
                        trend > 0 ? 'text-green-600' : trend < 0 ? 'text-red-600' : 'text-gray-600'
                    )}>
                        <TrendingUp className={cn(
                            'h-4 w-4',
                            trend < 0 && 'rotate-180'
                        )} />
                        {trend > 0 ? '+' : ''}{trend.toFixed(2)} over {trends.length} reviews
                    </div>
                </div>
                
                {/* Simple trend visualization */}
                <div className="space-y-2">
                    {trends.slice(-5).map((point, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                            <span className="text-gray-500 w-20">
                                {new Date(point.date).toLocaleDateString()}
                            </span>
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-yellow-400 h-2 rounded-full"
                                    style={{ width: `${(point.average_so_far / 5) * 100}%` }}
                                />
                            </div>
                            <span className="w-8 text-right">
                                {point.average_so_far.toFixed(1)}
                            </span>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
}
