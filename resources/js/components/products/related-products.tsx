import React from 'react';
import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CompactRatingDisplay } from '@/components/products/product-rating-display';
import { ShoppingCart, Eye, Heart, ArrowRight, BarChart3 } from 'lucide-react';
import { cn, formatCurrency } from '@/lib/utils';

interface Product {
    id: number;
    name: string;
    slug: string;
    description?: string;
    short_description?: string;
    price: number;
    sale_price?: number;
    image?: string;
    average_rating: number;
    total_reviews: number;
    total_ratings: number;
    rating_breakdown: Record<number, number>;
    reviews_enabled: boolean;
    featured: boolean;
    status: string;
}

interface RelatedProductsProps {
    products: Product[];
    title?: string;
    subtitle?: string;
    showAddToCart?: boolean;
    showViewButton?: boolean;
    showWishlist?: boolean;
    layout?: 'grid' | 'carousel';
    columns?: 2 | 3 | 4 | 6;
    className?: string;
}

export function RelatedProducts({
    products,
    title = "Related Products",
    subtitle = "You might also like these products",
    showAddToCart = true,
    showViewButton = true,
    showWishlist = false,
    layout = 'grid',
    columns = 4,
    className
}: RelatedProductsProps) {
    if (!products || products.length === 0) {
        return null;
    }

    const gridCols = {
        2: 'grid-cols-1 md:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
        6: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6'
    };

    return (
        <div className={cn('space-y-6', className)}>
            {/* Header */}
            <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
                {subtitle && (
                    <p className="text-gray-600">{subtitle}</p>
                )}
            </div>

            {/* Products Grid */}
            <div className={cn(
                'grid gap-6',
                layout === 'grid' ? gridCols[columns] : 'grid-cols-1'
            )}>
                {products.map((product) => (
                    <ProductCard
                        key={product.id}
                        product={product}
                        showAddToCart={showAddToCart}
                        showViewButton={showViewButton}
                        showWishlist={showWishlist}
                    />
                ))}
            </div>
        </div>
    );
}

interface ProductCardProps {
    product: Product;
    showAddToCart?: boolean;
    showViewButton?: boolean;
    showWishlist?: boolean;
    className?: string;
}

function ProductCard({
    product,
    showAddToCart = true,
    showViewButton = true,
    showWishlist = false,
    className
}: ProductCardProps) {
    const effectivePrice = product.sale_price || product.price;
    const isOnSale = product.sale_price && product.sale_price < product.price;
    const discountPercentage = isOnSale 
        ? Math.round(((product.price - product.sale_price!) / product.price) * 100)
        : 0;

    return (
        <Card className={cn('group hover:shadow-lg transition-shadow duration-200', className)}>
            {/* Product Image */}
            <div className="relative aspect-square overflow-hidden rounded-t-lg">
                {product.image ? (
                    <img
                        src={`/storage/${product.image}`}
                        alt={product.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400">No Image</span>
                    </div>
                )}
                
                {/* Badges */}
                <div className="absolute top-2 left-2 space-y-1">
                    {product.featured && (
                        <Badge variant="default" className="text-xs">
                            Featured
                        </Badge>
                    )}
                    {isOnSale && (
                        <Badge variant="destructive" className="text-xs">
                            -{discountPercentage}%
                        </Badge>
                    )}
                </div>

                {/* Wishlist Button */}
                {showWishlist && (
                    <Button
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 h-8 w-8 p-0 bg-white/80 hover:bg-white"
                    >
                        <Heart className="h-4 w-4" />
                    </Button>
                )}
            </div>

            <CardContent className="p-4 space-y-3">
                {/* Product Name */}
                <div>
                    <Link href={route('products.show', product.slug)}>
                        <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2">
                            {product.name}
                        </h3>
                    </Link>
                </div>

                {/* Description */}
                {product.short_description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                        {product.short_description}
                    </p>
                )}

                {/* Rating */}
                <CompactRatingDisplay
                    stats={{
                        average_rating: product.average_rating,
                        total_reviews: product.total_reviews,
                        total_ratings: product.total_ratings,
                        rating_breakdown: product.rating_breakdown || {},
                        rating_breakdown_percentages: {},
                        reviews_enabled: product.reviews_enabled
                    }}
                />

                {/* Price */}
                <div className="flex items-center gap-2">
                    <span className="text-lg font-bold text-gray-900">
                        {formatCurrency(effectivePrice)}
                    </span>
                    {isOnSale && (
                        <span className="text-sm text-gray-500 line-through">
                            {formatCurrency(product.price)}
                        </span>
                    )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                    {showViewButton && (
                        <Link href={route('products.show', product.slug)} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                                <Eye className="h-4 w-4 mr-2" />
                                View
                            </Button>
                        </Link>
                    )}
                    
                    {showAddToCart && (
                        <Button size="sm" className="flex-1">
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Add to Cart
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

interface PersonalizedRecommendationsProps {
    products: Product[];
    userId?: number;
    className?: string;
}

export function PersonalizedRecommendations({
    products,
    userId,
    className
}: PersonalizedRecommendationsProps) {
    if (!products || products.length === 0) {
        return null;
    }

    return (
        <RelatedProducts
            products={products}
            title="Recommended for You"
            subtitle="Based on your purchase history and preferences"
            columns={4}
            className={className}
        />
    );
}

interface TrendingProductsProps {
    products: Product[];
    className?: string;
}

export function TrendingProducts({ products, className }: TrendingProductsProps) {
    if (!products || products.length === 0) {
        return null;
    }

    return (
        <RelatedProducts
            products={products}
            title="Trending Now"
            subtitle="Popular products this month"
            columns={6}
            className={className}
        />
    );
}

interface SimilarProductsProps {
    products: Product[];
    baseProduct?: Product;
    className?: string;
}

export function SimilarProducts({ products, baseProduct, className }: SimilarProductsProps) {
    if (!products || products.length === 0) {
        return null;
    }

    const title = baseProduct 
        ? `More like "${baseProduct.name}"`
        : "Similar Products";

    return (
        <RelatedProducts
            products={products}
            title={title}
            subtitle="Products you might also like"
            columns={4}
            className={className}
        />
    );
}

interface FrequentlyBoughtTogetherProps {
    products: Product[];
    baseProduct?: Product;
    className?: string;
}

export function FrequentlyBoughtTogether({ 
    products, 
    baseProduct, 
    className 
}: FrequentlyBoughtTogetherProps) {
    if (!products || products.length === 0) {
        return null;
    }

    const totalPrice = products.reduce((sum, product) => {
        return sum + (product.sale_price || product.price);
    }, baseProduct ? (baseProduct.sale_price || baseProduct.price) : 0);

    return (
        <Card className={cn('', className)}>
            <CardHeader>
                <CardTitle>Frequently Bought Together</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="flex items-center gap-4 overflow-x-auto pb-2">
                    {baseProduct && (
                        <>
                            <div className="flex-shrink-0 text-center">
                                <div className="w-20 h-20 bg-gray-100 rounded-lg mb-2 overflow-hidden">
                                    {baseProduct.image ? (
                                        <img
                                            src={`/storage/${baseProduct.image}`}
                                            alt={baseProduct.name}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                            <span className="text-xs text-gray-400">No Image</span>
                                        </div>
                                    )}
                                </div>
                                <p className="text-xs font-medium line-clamp-2">{baseProduct.name}</p>
                                <p className="text-xs text-gray-600">
                                    {formatCurrency(baseProduct.sale_price || baseProduct.price)}
                                </p>
                            </div>
                            <div className="text-gray-400">+</div>
                        </>
                    )}
                    
                    {products.map((product, index) => (
                        <React.Fragment key={product.id}>
                            <div className="flex-shrink-0 text-center">
                                <div className="w-20 h-20 bg-gray-100 rounded-lg mb-2 overflow-hidden">
                                    {product.image ? (
                                        <img
                                            src={`/storage/${product.image}`}
                                            alt={product.name}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                            <span className="text-xs text-gray-400">No Image</span>
                                        </div>
                                    )}
                                </div>
                                <p className="text-xs font-medium line-clamp-2">{product.name}</p>
                                <p className="text-xs text-gray-600">
                                    {formatCurrency(product.sale_price || product.price)}
                                </p>
                            </div>
                            {index < products.length - 1 && (
                                <div className="text-gray-400">+</div>
                            )}
                        </React.Fragment>
                    ))}
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                    <div>
                        <p className="text-sm text-gray-600">Total price:</p>
                        <p className="text-lg font-bold">{formatCurrency(totalPrice)}</p>
                    </div>
                    
                    <Button>
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add All to Cart
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
