import React from 'react';
import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
    ShoppingCart, 
    Eye, 
    Package, 
    TrendingDown,
    Star,
    Users
} from 'lucide-react';
import { cn, formatCurrency } from '@/lib/utils';

interface Product {
    id: number;
    name: string;
    slug: string;
    price: number;
    sale_price?: number;
    image?: string;
    average_rating: number;
    total_reviews: number;
    quantity?: number;
    is_required?: boolean;
    is_optional?: boolean;
}

interface Bundle {
    id: number;
    name: string;
    slug: string;
    short_description?: string;
    original_price: number;
    bundle_price: number;
    discount_percentage: number;
    savings_amount: number;
    image?: string;
    featured: boolean;
    products_count: number;
    products?: Product[];
}

interface BundleCardProps {
    bundle: Bundle;
    showProducts?: boolean;
    showAddToCart?: boolean;
    showViewButton?: boolean;
    onAddToCart?: (bundleId: number) => void;
    className?: string;
}

export function BundleCard({
    bundle,
    showProducts = false,
    showAddToCart = true,
    showViewButton = true,
    onAddToCart,
    className
}: BundleCardProps) {
    const handleAddToCart = () => {
        if (onAddToCart) {
            onAddToCart(bundle.id);
        }
    };

    return (
        <Card className={cn('group hover:shadow-lg transition-shadow duration-200', className)}>
            {/* Bundle Image */}
            <div className="relative aspect-video overflow-hidden rounded-t-lg">
                {bundle.image ? (
                    <img
                        src={`/storage/${bundle.image}`}
                        alt={bundle.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <Package className="h-12 w-12 text-white" />
                    </div>
                )}
                
                {/* Badges */}
                <div className="absolute top-2 left-2 space-y-1">
                    {bundle.featured && (
                        <Badge variant="default" className="text-xs">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                        </Badge>
                    )}
                    {bundle.discount_percentage > 0 && (
                        <Badge variant="destructive" className="text-xs">
                            <TrendingDown className="h-3 w-3 mr-1" />
                            {Math.round(bundle.discount_percentage)}% OFF
                        </Badge>
                    )}
                </div>

                {/* Products Count */}
                <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="text-xs">
                        <Package className="h-3 w-3 mr-1" />
                        {bundle.products_count} items
                    </Badge>
                </div>
            </div>

            <CardContent className="p-4 space-y-3">
                {/* Bundle Name */}
                <div>
                    <Link href={route('bundles.show', bundle.slug)}>
                        <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2">
                            {bundle.name}
                        </h3>
                    </Link>
                </div>

                {/* Description */}
                {bundle.short_description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                        {bundle.short_description}
                    </p>
                )}

                {/* Pricing */}
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-gray-900">
                            {formatCurrency(bundle.bundle_price)}
                        </span>
                        {bundle.original_price > bundle.bundle_price && (
                            <span className="text-sm text-gray-500 line-through">
                                {formatCurrency(bundle.original_price)}
                            </span>
                        )}
                    </div>

                    {bundle.savings_amount > 0 && (
                        <div className="flex items-center gap-1 text-sm text-green-600">
                            <TrendingDown className="h-3 w-3" />
                            <span>Save {formatCurrency(bundle.savings_amount)}</span>
                        </div>
                    )}
                </div>

                {/* Products Preview */}
                {showProducts && bundle.products && bundle.products.length > 0 && (
                    <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700">Includes:</h4>
                        <div className="space-y-1">
                            {bundle.products.slice(0, 3).map((product) => (
                                <div key={product.id} className="flex items-center gap-2 text-xs text-gray-600">
                                    <div className="w-6 h-6 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                                        {product.image ? (
                                            <img
                                                src={`/storage/${product.image}`}
                                                alt={product.name}
                                                className="w-full h-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                <Package className="h-3 w-3 text-gray-400" />
                                            </div>
                                        )}
                                    </div>
                                    <span className="line-clamp-1">{product.name}</span>
                                    {product.quantity && product.quantity > 1 && (
                                        <span className="text-gray-500">×{product.quantity}</span>
                                    )}
                                </div>
                            ))}
                            {bundle.products.length > 3 && (
                                <div className="text-xs text-gray-500">
                                    +{bundle.products.length - 3} more items
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                    {showViewButton && (
                        <Link href={route('bundles.show', bundle.slug)} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                                <Eye className="h-4 w-4 mr-2" />
                                View Bundle
                            </Button>
                        </Link>
                    )}
                    
                    {showAddToCart && (
                        <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={handleAddToCart}
                        >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Add to Cart
                        </Button>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

interface BundleListProps {
    bundles: Bundle[];
    showProducts?: boolean;
    showAddToCart?: boolean;
    showViewButton?: boolean;
    onAddToCart?: (bundleId: number) => void;
    columns?: 2 | 3 | 4;
    className?: string;
}

export function BundleList({
    bundles,
    showProducts = false,
    showAddToCart = true,
    showViewButton = true,
    onAddToCart,
    columns = 3,
    className
}: BundleListProps) {
    if (!bundles || bundles.length === 0) {
        return (
            <div className={cn('text-center py-8', className)}>
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    No Bundles Available
                </h3>
                <p className="text-gray-600">
                    Check back later for exciting product bundles with great savings!
                </p>
            </div>
        );
    }

    const gridCols = {
        2: 'grid-cols-1 md:grid-cols-2',
        3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
    };

    return (
        <div className={cn('grid gap-6', gridCols[columns], className)}>
            {bundles.map((bundle) => (
                <BundleCard
                    key={bundle.id}
                    bundle={bundle}
                    showProducts={showProducts}
                    showAddToCart={showAddToCart}
                    showViewButton={showViewButton}
                    onAddToCart={onAddToCart}
                />
            ))}
        </div>
    );
}

interface FeaturedBundlesProps {
    bundles: Bundle[];
    title?: string;
    subtitle?: string;
    onAddToCart?: (bundleId: number) => void;
    className?: string;
}

export function FeaturedBundles({
    bundles,
    title = "Featured Bundles",
    subtitle = "Save more with our curated product bundles",
    onAddToCart,
    className
}: FeaturedBundlesProps) {
    if (!bundles || bundles.length === 0) {
        return null;
    }

    return (
        <div className={cn('space-y-6', className)}>
            {/* Header */}
            <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
                {subtitle && (
                    <p className="text-gray-600">{subtitle}</p>
                )}
            </div>

            {/* Bundles */}
            <BundleList
                bundles={bundles}
                showProducts={true}
                onAddToCart={onAddToCart}
                columns={3}
            />
        </div>
    );
}
