import { DollarSign, TrendingUp, TrendingDown, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TransactionSummaryProps {
    title: string;
    timeframe: string;
    amount: string;
    change?: {
        value: string;
        type: 'increase' | 'decrease';
        period: string;
    };
    stats: Array<{
        label: string;
        value: string;
        subtext?: string;
    }>;
    className?: string;
}

export function TransactionsSummary({ 
    title, 
    timeframe, 
    amount, 
    change, 
    stats, 
    className 
}: TransactionSummaryProps) {
    return (
        <div className={cn(
            'bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm',
            className
        )}>
            <div className="flex items-center justify-between mb-6">
                <div>
                    <h2 className="text-xl font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                        <DollarSign className="h-5 w-5 text-green-600" />
                        {title}
                    </h2>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                        {timeframe}
                    </p>
                </div>
                <div className="text-right">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">
                        {amount}
                    </div>
                    {change && (
                        <div className={cn(
                            'flex items-center text-sm font-medium mt-1',
                            change.type === 'increase' ? 'text-green-600' : 'text-red-600'
                        )}>
                            {change.type === 'increase' ? (
                                <TrendingUp className="h-4 w-4 mr-1" />
                            ) : (
                                <TrendingDown className="h-4 w-4 mr-1" />
                            )}
                            {change.value} {change.period}
                        </div>
                    )}
                </div>
            </div>

            <div className="space-y-4">
                {stats.map((stat, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                            <div>
                                <span className="text-sm font-medium text-slate-900 dark:text-white">
                                    {stat.label}
                                </span>
                                {stat.subtext && (
                                    <p className="text-xs text-slate-600 dark:text-slate-400">
                                        {stat.subtext}
                                    </p>
                                )}
                            </div>
                        </div>
                        <span className="text-sm font-semibold text-slate-900 dark:text-white">
                            {stat.value}
                        </span>
                    </div>
                ))}
            </div>

            <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                    View detailed report
                </button>
            </div>
        </div>
    );
}

// Default transaction summaries matching the original design
export function DefaultTransactionsSummaries() {
    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TransactionsSummary
                title="Transactions"
                timeframe="All Time"
                amount="0.00BDT"
                stats={[
                    { label: 'Past 24 Hours', value: '0.00BDT' },
                    { label: 'Past 7 Days', value: '0.00BDT' },
                    { label: 'Past 30 Days', value: '0.00BDT' },
                ]}
            />

            <div className="bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm">
                <h2 className="text-xl font-semibold text-slate-900 dark:text-white mb-6 flex items-center gap-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    Recent Activity Summary
                </h2>
                
                <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full bg-green-500"></div>
                            <span className="text-sm font-medium text-slate-900 dark:text-white">
                                Users
                            </span>
                        </div>
                        <div className="text-right">
                            <div className="text-sm font-semibold text-slate-900 dark:text-white">1</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400">Online</div>
                        </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                            <span className="text-sm font-medium text-slate-900 dark:text-white">
                                Orders
                            </span>
                        </div>
                        <div className="text-right">
                            <div className="text-sm font-semibold text-slate-900 dark:text-white">0</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400">Pending</div>
                        </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                            <span className="text-sm font-medium text-slate-900 dark:text-white">
                                Invoices
                            </span>
                        </div>
                        <div className="text-right">
                            <div className="text-sm font-semibold text-slate-900 dark:text-white">0</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400">Unpaid</div>
                        </div>
                    </div>

                    <div className="flex items-center justify-between p-3 bg-slate-50 dark:bg-slate-700/50 rounded-lg">
                        <div className="flex items-center space-x-3">
                            <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                            <span className="text-sm font-medium text-slate-900 dark:text-white">
                                Transactions
                            </span>
                        </div>
                        <div className="text-right">
                            <div className="text-sm font-semibold text-slate-900 dark:text-white">0</div>
                            <div className="text-xs text-slate-600 dark:text-slate-400">Complete</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
