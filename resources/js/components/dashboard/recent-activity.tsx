import { Clock, DollarSign, Users, FileText, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActivityItem {
    id: string;
    type: 'user' | 'order' | 'invoice' | 'transaction';
    title: string;
    description: string;
    amount?: string;
    status: 'pending' | 'complete' | 'inactive';
    time: string;
}

interface RecentActivityProps {
    title: string;
    items: ActivityItem[];
    className?: string;
}

const activityIcons = {
    user: Users,
    order: ShoppingCart,
    invoice: FileText,
    transaction: DollarSign,
};

const statusColors = {
    pending: 'text-orange-600 bg-orange-100',
    complete: 'text-green-600 bg-green-100',
    inactive: 'text-gray-600 bg-gray-100',
};

export function RecentActivity({ title, items, className }: RecentActivityProps) {
    return (
        <div className={cn('bg-white dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700 p-6 shadow-sm', className)}>
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-slate-900 dark:text-white flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    {title}
                </h2>
                <span className="text-sm text-slate-500 dark:text-slate-400">
                    {items.length} items
                </span>
            </div>

            <div className="space-y-4">
                {items.length === 0 ? (
                    <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                        <Clock className="h-12 w-12 mx-auto mb-3 opacity-50" />
                        <p>No recent activity</p>
                    </div>
                ) : (
                    items.map((item) => {
                        const Icon = activityIcons[item.type];
                        return (
                            <div key={item.id} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors">
                                <div className="flex-shrink-0">
                                    <div className="w-10 h-10 rounded-full bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                                        <Icon className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                                    </div>
                                </div>
                                
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-center justify-between">
                                        <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                                            {item.title}
                                        </p>
                                        <div className="flex items-center space-x-2">
                                            {item.amount && (
                                                <span className="text-sm font-semibold text-slate-900 dark:text-white">
                                                    {item.amount}
                                                </span>
                                            )}
                                            <span className={cn(
                                                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                                                statusColors[item.status]
                                            )}>
                                                {item.status}
                                            </span>
                                        </div>
                                    </div>
                                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                                        {item.description}
                                    </p>
                                    <p className="text-xs text-slate-500 dark:text-slate-500 mt-2">
                                        {item.time}
                                    </p>
                                </div>
                            </div>
                        );
                    })
                )}
            </div>

            {items.length > 0 && (
                <div className="mt-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <button className="w-full text-center text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">
                        View all activity
                    </button>
                </div>
            )}
        </div>
    );
}
