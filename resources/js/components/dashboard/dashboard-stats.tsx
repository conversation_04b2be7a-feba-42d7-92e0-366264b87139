import { MetricCard } from './metric-card';
import { 
    Package, 
    Users, 
    Ticket, 
    Megaphone, 
    Gift, 
    GraduationCap,
    Building,
    FileText,
    UserCheck,
    Globe
} from 'lucide-react';

interface DashboardStatsProps {
    data?: {
        products?: { total: number; categories: number; subcategories: number };
        users?: { total: number; listingAgents: number; franchises: number };
        tickets?: { total: number; pending: number; resolved: number };
        advertisements?: { total: number; published: number; pending: number };
        packages?: { total: number; active: number; inactive: number };
        courses?: { total: number; active: number; inactive: number };
        companies?: { total: number; pending: number };
        invoices?: { total: number; paid: number; unpaid: number };
        participants?: { total: number; active: number; pending: number };
        onlineUsers?: { total: number; system: number; customers: number };
    };
}

export function DashboardStats({ data }: DashboardStatsProps) {
    // Default data matching the original design
    const defaultData = {
        products: { total: 5, categories: 2, subcategories: 4 },
        users: { total: 103767, listingAgents: 10, franchises: 17 },
        tickets: { total: 0, pending: 1, resolved: 0 },
        advertisements: { total: 0, published: 0, pending: 0 },
        packages: { total: 6, active: 0, inactive: 0 },
        courses: { total: 0, active: 2, inactive: 3 },
        companies: { total: 0, pending: 0 },
        invoices: { total: 0, paid: 0, unpaid: 0 },
        participants: { total: 0, active: 0, pending: 0 },
        onlineUsers: { total: 1, system: 1, customers: 0 },
    };

    const stats = { ...defaultData, ...data };

    return (
        <div className="space-y-6">
            {/* Top Row - Main Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
                <MetricCard
                    title="Products"
                    value={stats.products.total}
                    color="green"
                    icon={Package}
                    stats={[
                        { label: 'Categories', value: stats.products.categories },
                        { label: 'Subcategories', value: stats.products.subcategories },
                    ]}
                />

                <MetricCard
                    title="Users"
                    value={stats.users.total.toLocaleString()}
                    color="blue"
                    icon={Users}
                    stats={[
                        { label: 'LISTING AGENT', value: stats.users.listingAgents },
                        { label: 'Franchise', value: stats.users.franchises },
                    ]}
                />

                <MetricCard
                    title="Tickets"
                    value={stats.tickets.total}
                    color="orange"
                    icon={Ticket}
                    stats={[
                        { label: 'Pending', value: stats.tickets.pending },
                        { label: 'Resolved', value: stats.tickets.resolved },
                    ]}
                />

                <MetricCard
                    title="Advertisements"
                    value={stats.advertisements.total}
                    color="teal"
                    icon={Megaphone}
                    stats={[
                        { label: 'Published', value: stats.advertisements.published },
                        { label: 'Pending', value: stats.advertisements.pending },
                    ]}
                />

                <MetricCard
                    title="Packages"
                    value={stats.packages.total}
                    color="pink"
                    icon={Gift}
                    stats={[
                        { label: 'ACTIVE', value: stats.packages.active },
                        { label: 'Inactive', value: stats.packages.inactive },
                    ]}
                />

                <MetricCard
                    title="Courses"
                    value={stats.courses.total}
                    color="cyan"
                    icon={GraduationCap}
                    stats={[
                        { label: 'ACTIVE', value: stats.courses.active },
                        { label: 'Inactive', value: stats.courses.inactive },
                    ]}
                />
            </div>

            {/* Bottom Row - Secondary Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                <MetricCard
                    title="Companies"
                    value={stats.companies.total}
                    color="blue"
                    icon={Building}
                    stats={[
                        { label: 'Pending', value: stats.companies.pending },
                    ]}
                />

                <MetricCard
                    title="Invoices"
                    value={stats.invoices.total}
                    color="black"
                    icon={FileText}
                    stats={[
                        { label: 'Paid', value: stats.invoices.paid },
                        { label: 'Unpaid', value: stats.invoices.unpaid },
                    ]}
                />

                <MetricCard
                    title="Participants"
                    value={stats.participants.total}
                    color="orange"
                    icon={UserCheck}
                    stats={[
                        { label: 'Active', value: stats.participants.active },
                        { label: 'Pending', value: stats.participants.pending },
                    ]}
                />

                <MetricCard
                    title="Users"
                    value={3}
                    color="red"
                    icon={Users}
                    stats={[
                        { label: 'Active', value: 1 },
                        { label: 'Inactive', value: 1 },
                    ]}
                />

                <MetricCard
                    title="Online Users"
                    value={stats.onlineUsers.total}
                    color="emerald"
                    icon={Globe}
                    stats={[
                        { label: 'System', value: stats.onlineUsers.system },
                        { label: 'Customers', value: stats.onlineUsers.customers },
                    ]}
                />
            </div>
        </div>
    );
}
