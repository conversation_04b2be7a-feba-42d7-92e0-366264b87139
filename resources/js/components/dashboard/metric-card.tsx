import { type LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MetricCardProps {
    title: string;
    value: string | number;
    subtitle?: string;
    icon?: LucideIcon;
    color: 'green' | 'blue' | 'orange' | 'teal' | 'pink' | 'cyan' | 'black' | 'red' | 'emerald';
    stats?: Array<{
        label: string;
        value: string | number;
        color?: string;
    }>;
    className?: string;
}

const colorVariants = {
    green: {
        bg: 'bg-gradient-to-br from-green-500 to-green-600',
        text: 'text-white',
        icon: 'text-green-100',
        stat: 'text-green-100',
    },
    blue: {
        bg: 'bg-gradient-to-br from-blue-500 to-blue-600',
        text: 'text-white',
        icon: 'text-blue-100',
        stat: 'text-blue-100',
    },
    orange: {
        bg: 'bg-gradient-to-br from-orange-500 to-orange-600',
        text: 'text-white',
        icon: 'text-orange-100',
        stat: 'text-orange-100',
    },
    teal: {
        bg: 'bg-gradient-to-br from-teal-500 to-teal-600',
        text: 'text-white',
        icon: 'text-teal-100',
        stat: 'text-teal-100',
    },
    pink: {
        bg: 'bg-gradient-to-br from-pink-500 to-pink-600',
        text: 'text-white',
        icon: 'text-pink-100',
        stat: 'text-pink-100',
    },
    cyan: {
        bg: 'bg-gradient-to-br from-cyan-500 to-cyan-600',
        text: 'text-white',
        icon: 'text-cyan-100',
        stat: 'text-cyan-100',
    },
    black: {
        bg: 'bg-gradient-to-br from-slate-800 to-slate-900',
        text: 'text-white',
        icon: 'text-slate-100',
        stat: 'text-slate-100',
    },
    red: {
        bg: 'bg-gradient-to-br from-red-500 to-red-600',
        text: 'text-white',
        icon: 'text-red-100',
        stat: 'text-red-100',
    },
    emerald: {
        bg: 'bg-gradient-to-br from-emerald-500 to-emerald-600',
        text: 'text-white',
        icon: 'text-emerald-100',
        stat: 'text-emerald-100',
    },
};

export function MetricCard({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    color, 
    stats = [], 
    className 
}: MetricCardProps) {
    const variant = colorVariants[color];

    return (
        <div className={cn(
            'rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1',
            variant.bg,
            className
        )}>
            <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                    <h3 className={cn('text-sm font-medium mb-1', variant.text)}>
                        {title}
                    </h3>
                    <div className={cn('text-3xl font-bold', variant.text)}>
                        {value}
                    </div>
                    {subtitle && (
                        <p className={cn('text-sm mt-1', variant.stat)}>
                            {subtitle}
                        </p>
                    )}
                </div>
                {Icon && (
                    <div className="ml-4">
                        <Icon className={cn('h-8 w-8', variant.icon)} />
                    </div>
                )}
            </div>

            {stats.length > 0 && (
                <div className="space-y-2">
                    {stats.map((stat, index) => (
                        <div key={index} className="flex justify-between items-center">
                            <span className={cn('text-sm', variant.stat)}>
                                {stat.label}
                            </span>
                            <span className={cn('text-sm font-semibold', variant.text)}>
                                {stat.value}
                            </span>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}
