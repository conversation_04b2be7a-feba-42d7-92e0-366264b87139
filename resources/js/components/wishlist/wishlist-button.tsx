import React, { useState, useEffect } from 'react';
import { Heart, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { router } from '@inertiajs/react';

interface WishlistButtonProps {
    productId: number;
    initialInWishlist?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'outline' | 'ghost';
    showText?: boolean;
    className?: string;
    onToggle?: (inWishlist: boolean) => void;
}

export function WishlistButton({
    productId,
    initialInWishlist = false,
    size = 'md',
    variant = 'outline',
    showText = true,
    className,
    onToggle
}: WishlistButtonProps) {
    const [inWishlist, setInWishlist] = useState(initialInWishlist);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        // Check if product is in wishlist on mount
        checkWishlistStatus();
    }, [productId]);

    const checkWishlistStatus = async () => {
        try {
            const response = await fetch(route('wishlists.check-product'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ product_id: productId }),
            });

            if (response.ok) {
                const data = await response.json();
                setInWishlist(data.in_wishlist);
            }
        } catch (error) {
            console.error('Error checking wishlist status:', error);
        }
    };

    const toggleWishlist = async () => {
        setLoading(true);

        try {
            const endpoint = inWishlist ? 'wishlists.quick-remove' : 'wishlists.quick-add';
            const response = await fetch(route(endpoint), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ product_id: productId }),
            });

            if (response.ok) {
                const newStatus = !inWishlist;
                setInWishlist(newStatus);
                onToggle?.(newStatus);
            } else {
                const errorData = await response.json();
                console.error('Wishlist toggle error:', errorData.message);
            }
        } catch (error) {
            console.error('Error toggling wishlist:', error);
        } finally {
            setLoading(false);
        }
    };

    const sizeClasses = {
        sm: 'h-8 px-2',
        md: 'h-9 px-3',
        lg: 'h-10 px-4'
    };

    const iconSizes = {
        sm: 'h-3 w-3',
        md: 'h-4 w-4',
        lg: 'h-5 w-5'
    };

    return (
        <Button
            variant={variant}
            size={size}
            onClick={toggleWishlist}
            disabled={loading}
            className={cn(
                'transition-colors duration-200',
                inWishlist && 'text-red-600 hover:text-red-700',
                className
            )}
        >
            {loading ? (
                <Loader2 className={cn(iconSizes[size], 'animate-spin')} />
            ) : (
                <Heart 
                    className={cn(
                        iconSizes[size],
                        inWishlist && 'fill-current'
                    )} 
                />
            )}
            
            {showText && (
                <span className="ml-2">
                    {inWishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
                </span>
            )}
        </Button>
    );
}

interface WishlistIconButtonProps {
    productId: number;
    initialInWishlist?: boolean;
    className?: string;
    onToggle?: (inWishlist: boolean) => void;
}

export function WishlistIconButton({
    productId,
    initialInWishlist = false,
    className,
    onToggle
}: WishlistIconButtonProps) {
    const [inWishlist, setInWishlist] = useState(initialInWishlist);
    const [loading, setLoading] = useState(false);

    const toggleWishlist = async () => {
        setLoading(true);

        try {
            const endpoint = inWishlist ? 'wishlists.quick-remove' : 'wishlists.quick-add';
            const response = await fetch(route(endpoint), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ product_id: productId }),
            });

            if (response.ok) {
                const newStatus = !inWishlist;
                setInWishlist(newStatus);
                onToggle?.(newStatus);
            }
        } catch (error) {
            console.error('Error toggling wishlist:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <button
            onClick={toggleWishlist}
            disabled={loading}
            className={cn(
                'p-2 rounded-full bg-white/80 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md',
                inWishlist && 'text-red-600',
                className
            )}
        >
            {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
                <Heart 
                    className={cn(
                        'h-4 w-4',
                        inWishlist && 'fill-current'
                    )} 
                />
            )}
        </button>
    );
}

interface WishlistCounterProps {
    userId?: number;
    className?: string;
}

export function WishlistCounter({ userId, className }: WishlistCounterProps) {
    const [count, setCount] = useState(0);

    useEffect(() => {
        if (userId) {
            fetchWishlistCount();
        }
    }, [userId]);

    const fetchWishlistCount = async () => {
        try {
            const response = await fetch(route('wishlists.index'));
            if (response.ok) {
                const data = await response.json();
                const totalItems = data.wishlists.reduce((sum: number, wishlist: any) => 
                    sum + wishlist.items_count, 0
                );
                setCount(totalItems);
            }
        } catch (error) {
            console.error('Error fetching wishlist count:', error);
        }
    };

    if (count === 0) {
        return null;
    }

    return (
        <span className={cn(
            'inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-600 rounded-full',
            className
        )}>
            {count}
        </span>
    );
}
