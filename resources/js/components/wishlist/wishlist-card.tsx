import React from 'react';
import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CompactRatingDisplay } from '@/components/products/product-rating-display';
import { WishlistIconButton } from './wishlist-button';
import { 
    Heart, 
    ShoppingCart, 
    Eye, 
    TrendingDown, 
    TrendingUp,
    Calendar,
    DollarSign,
    Package
} from 'lucide-react';
import { cn, formatCurrency } from '@/lib/utils';

interface WishlistItem {
    id: number;
    price_when_added: number;
    notes?: string;
    priority: number;
    notify_on_sale: boolean;
    notify_on_restock: boolean;
    created_at: string;
    product: {
        id: number;
        name: string;
        slug: string;
        description?: string;
        short_description?: string;
        price: number;
        sale_price?: number;
        image?: string;
        average_rating: number;
        total_reviews: number;
        reviews_enabled: boolean;
    };
}

interface WishlistItemCardProps {
    item: WishlistItem;
    onRemove?: (itemId: number) => void;
    onAddToCart?: (productId: number) => void;
    showActions?: boolean;
    className?: string;
}

export function WishlistItemCard({
    item,
    onRemove,
    onAddToCart,
    showActions = true,
    className
}: WishlistItemCardProps) {
    const { product } = item;
    const currentPrice = product.sale_price || product.price;
    const priceWhenAdded = item.price_when_added;
    const priceDifference = priceWhenAdded - currentPrice;
    const hasPriceDrop = priceDifference > 0;
    const isOnSale = product.sale_price && product.sale_price < product.price;

    const handleRemove = () => {
        if (onRemove) {
            onRemove(item.id);
        }
    };

    const handleAddToCart = () => {
        if (onAddToCart) {
            onAddToCart(product.id);
        }
    };

    return (
        <Card className={cn('group hover:shadow-lg transition-shadow duration-200', className)}>
            <div className="relative">
                {/* Product Image */}
                <div className="aspect-square overflow-hidden rounded-t-lg">
                    {product.image ? (
                        <img
                            src={`/storage/${product.image}`}
                            alt={product.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                    ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                            <Package className="h-12 w-12 text-gray-400" />
                        </div>
                    )}
                </div>

                {/* Badges */}
                <div className="absolute top-2 left-2 space-y-1">
                    {isOnSale && (
                        <Badge variant="destructive" className="text-xs">
                            Sale
                        </Badge>
                    )}
                    {hasPriceDrop && (
                        <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                            <TrendingDown className="h-3 w-3 mr-1" />
                            Price Drop
                        </Badge>
                    )}
                </div>

                {/* Wishlist Button */}
                {showActions && (
                    <div className="absolute top-2 right-2">
                        <WishlistIconButton
                            productId={product.id}
                            initialInWishlist={true}
                            onToggle={(inWishlist) => {
                                if (!inWishlist) {
                                    handleRemove();
                                }
                            }}
                        />
                    </div>
                )}
            </div>

            <CardContent className="p-4 space-y-3">
                {/* Product Name */}
                <div>
                    <Link href={route('products.show', product.slug)}>
                        <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors line-clamp-2">
                            {product.name}
                        </h3>
                    </Link>
                </div>

                {/* Description */}
                {product.short_description && (
                    <p className="text-sm text-gray-600 line-clamp-2">
                        {product.short_description}
                    </p>
                )}

                {/* Rating */}
                <CompactRatingDisplay
                    stats={{
                        average_rating: product.average_rating,
                        total_reviews: product.total_reviews,
                        total_ratings: product.total_reviews,
                        rating_breakdown: {},
                        rating_breakdown_percentages: {},
                        reviews_enabled: product.reviews_enabled
                    }}
                />

                {/* Price Information */}
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-gray-900">
                            {formatCurrency(currentPrice)}
                        </span>
                        {isOnSale && (
                            <span className="text-sm text-gray-500 line-through">
                                {formatCurrency(product.price)}
                            </span>
                        )}
                    </div>

                    {/* Price Change */}
                    {priceWhenAdded && priceWhenAdded !== currentPrice && (
                        <div className="flex items-center gap-1 text-sm">
                            <span className="text-gray-500">
                                Added at: {formatCurrency(priceWhenAdded)}
                            </span>
                            {hasPriceDrop ? (
                                <div className="flex items-center text-green-600">
                                    <TrendingDown className="h-3 w-3 mr-1" />
                                    <span>-{formatCurrency(priceDifference)}</span>
                                </div>
                            ) : (
                                <div className="flex items-center text-red-600">
                                    <TrendingUp className="h-3 w-3 mr-1" />
                                    <span>+{formatCurrency(-priceDifference)}</span>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* Notes */}
                {item.notes && (
                    <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        <span className="font-medium">Note: </span>
                        {item.notes}
                    </div>
                )}

                {/* Added Date */}
                <div className="flex items-center text-xs text-gray-500">
                    <Calendar className="h-3 w-3 mr-1" />
                    Added {new Date(item.created_at).toLocaleDateString()}
                </div>

                {/* Actions */}
                {showActions && (
                    <div className="flex items-center gap-2 pt-2">
                        <Link href={route('products.show', product.slug)} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                                <Eye className="h-4 w-4 mr-2" />
                                View
                            </Button>
                        </Link>
                        
                        <Button 
                            size="sm" 
                            className="flex-1"
                            onClick={handleAddToCart}
                        >
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Add to Cart
                        </Button>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}

interface WishlistSummaryCardProps {
    wishlist: {
        id: number;
        name: string;
        description?: string;
        is_public: boolean;
        is_default: boolean;
        items_count: number;
        created_at: string;
    };
    stats?: {
        total_items: number;
        total_value: number;
        total_savings: number;
        items_on_sale: number;
        items_with_price_drops: number;
    };
    onEdit?: () => void;
    onDelete?: () => void;
    className?: string;
}

export function WishlistSummaryCard({
    wishlist,
    stats,
    onEdit,
    onDelete,
    className
}: WishlistSummaryCardProps) {
    return (
        <Card className={cn('hover:shadow-lg transition-shadow duration-200', className)}>
            <CardHeader>
                <div className="flex items-start justify-between">
                    <div className="space-y-1">
                        <CardTitle className="flex items-center gap-2">
                            <Heart className="h-5 w-5 text-red-500" />
                            {wishlist.name}
                            {wishlist.is_default && (
                                <Badge variant="secondary" className="text-xs">
                                    Default
                                </Badge>
                            )}
                            {wishlist.is_public && (
                                <Badge variant="outline" className="text-xs">
                                    Public
                                </Badge>
                            )}
                        </CardTitle>
                        {wishlist.description && (
                            <p className="text-sm text-gray-600">{wishlist.description}</p>
                        )}
                    </div>
                    
                    {(onEdit || onDelete) && (
                        <div className="flex items-center gap-2">
                            {onEdit && (
                                <Button variant="ghost" size="sm" onClick={onEdit}>
                                    Edit
                                </Button>
                            )}
                            {onDelete && !wishlist.is_default && (
                                <Button variant="ghost" size="sm" onClick={onDelete}>
                                    Delete
                                </Button>
                            )}
                        </div>
                    )}
                </div>
            </CardHeader>

            <CardContent className="space-y-4">
                {/* Stats */}
                {stats && (
                    <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                                {stats.total_items}
                            </div>
                            <div className="text-sm text-gray-600">Items</div>
                        </div>
                        
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-900">
                                {formatCurrency(stats.total_value)}
                            </div>
                            <div className="text-sm text-gray-600">Total Value</div>
                        </div>
                        
                        {stats.total_savings > 0 && (
                            <>
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-green-600">
                                        {formatCurrency(stats.total_savings)}
                                    </div>
                                    <div className="text-sm text-gray-600">Savings</div>
                                </div>
                                
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-blue-600">
                                        {stats.items_on_sale}
                                    </div>
                                    <div className="text-sm text-gray-600">On Sale</div>
                                </div>
                            </>
                        )}
                    </div>
                )}

                {/* Actions */}
                <div className="flex items-center gap-2">
                    <Link href={route('wishlists.show', wishlist.id)} className="flex-1">
                        <Button variant="outline" className="w-full">
                            View Wishlist
                        </Button>
                    </Link>
                    
                    {wishlist.items_count > 0 && (
                        <Button className="flex-1">
                            <ShoppingCart className="h-4 w-4 mr-2" />
                            Add All to Cart
                        </Button>
                    )}
                </div>

                {/* Created Date */}
                <div className="text-xs text-gray-500 text-center">
                    Created {new Date(wishlist.created_at).toLocaleDateString()}
                </div>
            </CardContent>
        </Card>
    );
}
