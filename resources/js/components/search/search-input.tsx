import React, { useState, useEffect, useRef } from 'react';
import { router } from '@inertiajs/react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    Search, 
    X, 
    Clock, 
    TrendingUp,
    Package,
    Tag,
    Folder
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Suggestion {
    type: 'product' | 'category' | 'tag';
    text: string;
    category: string;
}

interface QuickResult {
    id: number;
    name: string;
    slug: string;
    price: number;
    sale_price?: number;
    image?: string;
}

interface SearchInputProps {
    initialValue?: string;
    placeholder?: string;
    showSuggestions?: boolean;
    showQuickResults?: boolean;
    onSearch?: (query: string) => void;
    className?: string;
}

export function SearchInput({
    initialValue = '',
    placeholder = 'Search products...',
    showSuggestions = true,
    showQuickResults = true,
    onSearch,
    className
}: SearchInputProps) {
    const [query, setQuery] = useState(initialValue);
    const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
    const [quickResults, setQuickResults] = useState<QuickResult[]>([]);
    const [showDropdown, setShowDropdown] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(-1);
    
    const inputRef = useRef<HTMLInputElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const debounceRef = useRef<NodeJS.Timeout>();

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setShowDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    useEffect(() => {
        if (query.length >= 2) {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
            
            debounceRef.current = setTimeout(() => {
                fetchSuggestions(query);
            }, 300);
        } else {
            setSuggestions([]);
            setQuickResults([]);
            setShowDropdown(false);
        }

        return () => {
            if (debounceRef.current) {
                clearTimeout(debounceRef.current);
            }
        };
    }, [query]);

    const fetchSuggestions = async (searchQuery: string) => {
        setLoading(true);
        
        try {
            if (showQuickResults) {
                const response = await fetch(route('search.quick', { q: searchQuery }));
                if (response.ok) {
                    const data = await response.json();
                    setQuickResults(data.products || []);
                    setSuggestions(data.suggestions || []);
                    setShowDropdown(true);
                }
            } else if (showSuggestions) {
                const response = await fetch(route('search.suggestions', { q: searchQuery }));
                if (response.ok) {
                    const data = await response.json();
                    setSuggestions(data.suggestions || []);
                    setShowDropdown(true);
                }
            }
        } catch (error) {
            console.error('Error fetching suggestions:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = (searchQuery?: string) => {
        const finalQuery = searchQuery || query;
        
        if (finalQuery.trim()) {
            if (onSearch) {
                onSearch(finalQuery);
            } else {
                router.get(route('search.index'), { q: finalQuery });
            }
        }
        
        setShowDropdown(false);
        inputRef.current?.blur();
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        const totalItems = quickResults.length + suggestions.length;
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex(prev => (prev + 1) % totalItems);
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex(prev => prev <= 0 ? totalItems - 1 : prev - 1);
                break;
            case 'Enter':
                e.preventDefault();
                if (selectedIndex >= 0) {
                    if (selectedIndex < quickResults.length) {
                        const product = quickResults[selectedIndex];
                        router.get(route('products.show', product.slug));
                    } else {
                        const suggestion = suggestions[selectedIndex - quickResults.length];
                        setQuery(suggestion.text);
                        handleSearch(suggestion.text);
                    }
                } else {
                    handleSearch();
                }
                break;
            case 'Escape':
                setShowDropdown(false);
                setSelectedIndex(-1);
                break;
        }
    };

    const clearSearch = () => {
        setQuery('');
        setSuggestions([]);
        setQuickResults([]);
        setShowDropdown(false);
        inputRef.current?.focus();
    };

    const getSuggestionIcon = (type: string) => {
        switch (type) {
            case 'product':
                return <Package className="h-4 w-4" />;
            case 'category':
                return <Folder className="h-4 w-4" />;
            case 'tag':
                return <Tag className="h-4 w-4" />;
            default:
                return <Search className="h-4 w-4" />;
        }
    };

    return (
        <div className={cn('relative', className)} ref={dropdownRef}>
            <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                    ref={inputRef}
                    type="text"
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    onKeyDown={handleKeyDown}
                    onFocus={() => query.length >= 2 && setShowDropdown(true)}
                    placeholder={placeholder}
                    className="pl-10 pr-20"
                />
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
                    {query && (
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={clearSearch}
                            className="h-6 w-6 p-0"
                        >
                            <X className="h-3 w-3" />
                        </Button>
                    )}
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSearch()}
                        className="h-6 w-6 p-0"
                    >
                        <Search className="h-3 w-3" />
                    </Button>
                </div>
            </div>

            {/* Dropdown */}
            {showDropdown && (quickResults.length > 0 || suggestions.length > 0) && (
                <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-96 overflow-y-auto">
                    <CardContent className="p-0">
                        {/* Quick Results */}
                        {quickResults.length > 0 && (
                            <div className="border-b">
                                <div className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50">
                                    Products
                                </div>
                                {quickResults.map((product, index) => (
                                    <div
                                        key={product.id}
                                        className={cn(
                                            'flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer',
                                            selectedIndex === index && 'bg-blue-50'
                                        )}
                                        onClick={() => router.get(route('products.show', product.slug))}
                                    >
                                        <div className="w-10 h-10 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                                            {product.image ? (
                                                <img
                                                    src={`/storage/${product.image}`}
                                                    alt={product.name}
                                                    className="w-full h-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                                    <Package className="h-4 w-4 text-gray-400" />
                                                </div>
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="font-medium text-gray-900 truncate">
                                                {product.name}
                                            </div>
                                            <div className="text-sm text-gray-600">
                                                ${product.sale_price || product.price}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* Suggestions */}
                        {suggestions.length > 0 && (
                            <div>
                                <div className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50">
                                    Suggestions
                                </div>
                                {suggestions.map((suggestion, index) => (
                                    <div
                                        key={index}
                                        className={cn(
                                            'flex items-center gap-3 px-4 py-2 hover:bg-gray-50 cursor-pointer',
                                            selectedIndex === (quickResults.length + index) && 'bg-blue-50'
                                        )}
                                        onClick={() => {
                                            setQuery(suggestion.text);
                                            handleSearch(suggestion.text);
                                        }}
                                    >
                                        <div className="text-gray-400">
                                            {getSuggestionIcon(suggestion.type)}
                                        </div>
                                        <div className="flex-1">
                                            <span className="text-gray-900">{suggestion.text}</span>
                                            <Badge variant="secondary" className="ml-2 text-xs">
                                                {suggestion.category}
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}

                        {/* View All Results */}
                        {query && (
                            <div className="border-t">
                                <div
                                    className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 cursor-pointer text-blue-600"
                                    onClick={() => handleSearch()}
                                >
                                    <Search className="h-4 w-4" />
                                    <span>View all results for "{query}"</span>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            )}
        </div>
    );
}

interface SearchBarProps {
    className?: string;
}

export function SearchBar({ className }: SearchBarProps) {
    return (
        <div className={cn('w-full max-w-2xl', className)}>
            <SearchInput
                placeholder="Search for products, categories, or brands..."
                showQuickResults={true}
                showSuggestions={true}
            />
        </div>
    );
}
