import React, { useState } from 'react';
import { router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
    Filter, 
    X, 
    ChevronDown, 
    ChevronUp,
    Star,
    DollarSign,
    Folder,
    Tag,
    Package
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CategoryFacet {
    id: number;
    name: string;
    count: number;
}

interface PriceRangeFacet {
    min: number;
    max: number | null;
    label: string;
    count: number;
}

interface RatingFacet {
    rating: number;
    label: string;
    count: number;
}

interface TagFacet {
    tag: string;
    count: number;
}

interface AttributeFacet {
    value: boolean;
    label: string;
    count: number;
}

interface Facets {
    categories: CategoryFacet[];
    price_ranges: PriceRangeFacet[];
    ratings: RatingFacet[];
    tags: TagFacet[];
    attributes: {
        digital: AttributeFacet[];
        featured: AttributeFacet[];
        on_sale: AttributeFacet[];
    };
}

interface ActiveFilter {
    type: string;
    key: string;
    value: any;
    label: string;
    display: string;
}

interface SearchFiltersProps {
    facets: Facets;
    activeFilters: ActiveFilter[];
    currentQuery: string;
    onFilterChange?: (filters: Record<string, any>) => void;
    className?: string;
}

export function SearchFilters({
    facets,
    activeFilters,
    currentQuery,
    onFilterChange,
    className
}: SearchFiltersProps) {
    const [openSections, setOpenSections] = useState<Record<string, boolean>>({
        categories: true,
        price: true,
        rating: true,
        attributes: true,
        tags: false
    });

    const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);

    const toggleSection = (section: string) => {
        setOpenSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    const applyFilter = (key: string, value: any) => {
        const currentParams = new URLSearchParams(window.location.search);
        
        if (value === null || value === undefined || value === '') {
            currentParams.delete(key);
        } else if (Array.isArray(value)) {
            currentParams.delete(key);
            value.forEach(v => currentParams.append(`${key}[]`, v.toString()));
        } else {
            currentParams.set(key, value.toString());
        }

        const newUrl = `${window.location.pathname}?${currentParams.toString()}`;
        router.get(newUrl, {}, { preserveState: true, replace: true });
    };

    const removeFilter = (filter: ActiveFilter) => {
        if (filter.type === 'price_range') {
            applyFilter('min_price', null);
            applyFilter('max_price', null);
        } else if (filter.key === 'category_id') {
            const currentCategories = activeFilters
                .filter(f => f.key === 'category_id' && f.value !== filter.value)
                .map(f => f.value);
            applyFilter('category_id', currentCategories.length > 0 ? currentCategories : null);
        } else if (filter.key === 'tags') {
            const currentTags = activeFilters
                .filter(f => f.key === 'tags' && f.value !== filter.value)
                .map(f => f.value);
            applyFilter('tags', currentTags.length > 0 ? currentTags : null);
        } else {
            applyFilter(filter.key, null);
        }
    };

    const clearAllFilters = () => {
        const params = currentQuery ? { q: currentQuery } : {};
        router.get(route('search.index'), params);
    };

    const handleCategoryChange = (categoryId: number, checked: boolean) => {
        const currentCategories = activeFilters
            .filter(f => f.key === 'category_id')
            .map(f => f.value);
        
        let newCategories;
        if (checked) {
            newCategories = [...currentCategories, categoryId];
        } else {
            newCategories = currentCategories.filter(id => id !== categoryId);
        }
        
        applyFilter('category_id', newCategories.length > 0 ? newCategories : null);
    };

    const handleTagChange = (tag: string, checked: boolean) => {
        const currentTags = activeFilters
            .filter(f => f.key === 'tags')
            .map(f => f.value);
        
        let newTags;
        if (checked) {
            newTags = [...currentTags, tag];
        } else {
            newTags = currentTags.filter(t => t !== tag);
        }
        
        applyFilter('tags', newTags.length > 0 ? newTags : null);
    };

    const handlePriceRangeChange = () => {
        applyFilter('min_price', priceRange[0] > 0 ? priceRange[0] : null);
        applyFilter('max_price', priceRange[1] < 1000 ? priceRange[1] : null);
    };

    return (
        <div className={cn('space-y-4', className)}>
            {/* Active Filters */}
            {activeFilters.length > 0 && (
                <Card>
                    <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                            <CardTitle className="text-sm">Active Filters</CardTitle>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={clearAllFilters}
                                className="text-xs"
                            >
                                Clear All
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                        <div className="flex flex-wrap gap-2">
                            {activeFilters.map((filter, index) => (
                                <Badge
                                    key={index}
                                    variant="secondary"
                                    className="flex items-center gap-1"
                                >
                                    <span>{filter.display}</span>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => removeFilter(filter)}
                                        className="h-4 w-4 p-0 hover:bg-transparent"
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </Badge>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Categories */}
            {facets.categories && facets.categories.length > 0 && (
                <Card>
                    <Collapsible open={openSections.categories} onOpenChange={() => toggleSection('categories')}>
                        <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-gray-50">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-sm flex items-center gap-2">
                                        <Folder className="h-4 w-4" />
                                        Categories
                                    </CardTitle>
                                    {openSections.categories ? (
                                        <ChevronUp className="h-4 w-4" />
                                    ) : (
                                        <ChevronDown className="h-4 w-4" />
                                    )}
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <CardContent className="pt-0 space-y-2">
                                {facets.categories.slice(0, 10).map((category) => {
                                    const isChecked = activeFilters.some(
                                        f => f.key === 'category_id' && f.value === category.id
                                    );
                                    
                                    return (
                                        <div key={category.id} className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`category-${category.id}`}
                                                    checked={isChecked}
                                                    onCheckedChange={(checked) => 
                                                        handleCategoryChange(category.id, checked as boolean)
                                                    }
                                                />
                                                <Label
                                                    htmlFor={`category-${category.id}`}
                                                    className="text-sm cursor-pointer"
                                                >
                                                    {category.name}
                                                </Label>
                                            </div>
                                            <span className="text-xs text-gray-500">
                                                {category.count}
                                            </span>
                                        </div>
                                    );
                                })}
                            </CardContent>
                        </CollapsibleContent>
                    </Collapsible>
                </Card>
            )}

            {/* Price Range */}
            <Card>
                <Collapsible open={openSections.price} onOpenChange={() => toggleSection('price')}>
                    <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-sm flex items-center gap-2">
                                    <DollarSign className="h-4 w-4" />
                                    Price Range
                                </CardTitle>
                                {openSections.price ? (
                                    <ChevronUp className="h-4 w-4" />
                                ) : (
                                    <ChevronDown className="h-4 w-4" />
                                )}
                            </div>
                        </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                        <CardContent className="pt-0 space-y-4">
                            <div className="space-y-2">
                                <Slider
                                    value={priceRange}
                                    onValueChange={setPriceRange}
                                    max={1000}
                                    step={10}
                                    className="w-full"
                                />
                                <div className="flex items-center justify-between text-sm text-gray-600">
                                    <span>${priceRange[0]}</span>
                                    <span>${priceRange[1]}</span>
                                </div>
                            </div>
                            
                            <div className="flex items-center gap-2">
                                <Input
                                    type="number"
                                    placeholder="Min"
                                    value={priceRange[0]}
                                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                                    className="w-20 text-xs"
                                />
                                <span className="text-gray-500">-</span>
                                <Input
                                    type="number"
                                    placeholder="Max"
                                    value={priceRange[1]}
                                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000])}
                                    className="w-20 text-xs"
                                />
                                <Button
                                    size="sm"
                                    onClick={handlePriceRangeChange}
                                    className="text-xs"
                                >
                                    Apply
                                </Button>
                            </div>

                            {/* Predefined Price Ranges */}
                            {facets.price_ranges && facets.price_ranges.length > 0 && (
                                <div className="space-y-2">
                                    {facets.price_ranges.map((range, index) => (
                                        <div
                                            key={index}
                                            className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-1 rounded"
                                            onClick={() => {
                                                applyFilter('min_price', range.min);
                                                applyFilter('max_price', range.max);
                                            }}
                                        >
                                            <span className="text-sm">{range.label}</span>
                                            <span className="text-xs text-gray-500">{range.count}</span>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </CollapsibleContent>
                </Collapsible>
            </Card>

            {/* Rating */}
            {facets.ratings && facets.ratings.length > 0 && (
                <Card>
                    <Collapsible open={openSections.rating} onOpenChange={() => toggleSection('rating')}>
                        <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-gray-50">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-sm flex items-center gap-2">
                                        <Star className="h-4 w-4" />
                                        Customer Rating
                                    </CardTitle>
                                    {openSections.rating ? (
                                        <ChevronUp className="h-4 w-4" />
                                    ) : (
                                        <ChevronDown className="h-4 w-4" />
                                    )}
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <CardContent className="pt-0 space-y-2">
                                {facets.ratings.map((rating) => (
                                    <div
                                        key={rating.rating}
                                        className="flex items-center justify-between cursor-pointer hover:bg-gray-50 p-1 rounded"
                                        onClick={() => applyFilter('min_rating', rating.rating)}
                                    >
                                        <div className="flex items-center gap-1">
                                            {Array.from({ length: 5 }, (_, i) => (
                                                <Star
                                                    key={i}
                                                    className={cn(
                                                        'h-3 w-3',
                                                        i < rating.rating
                                                            ? 'fill-yellow-400 text-yellow-400'
                                                            : 'text-gray-300'
                                                    )}
                                                />
                                            ))}
                                            <span className="text-sm ml-1">{rating.label}</span>
                                        </div>
                                        <span className="text-xs text-gray-500">{rating.count}</span>
                                    </div>
                                ))}
                            </CardContent>
                        </CollapsibleContent>
                    </Collapsible>
                </Card>
            )}

            {/* Attributes */}
            <Card>
                <Collapsible open={openSections.attributes} onOpenChange={() => toggleSection('attributes')}>
                    <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50">
                            <div className="flex items-center justify-between">
                                <CardTitle className="text-sm flex items-center gap-2">
                                    <Package className="h-4 w-4" />
                                    Product Type
                                </CardTitle>
                                {openSections.attributes ? (
                                    <ChevronUp className="h-4 w-4" />
                                ) : (
                                    <ChevronDown className="h-4 w-4" />
                                )}
                            </div>
                        </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                        <CardContent className="pt-0 space-y-2">
                            {/* Digital/Physical */}
                            {facets.attributes.digital?.map((attr) => (
                                <div key={attr.label} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id={`digital-${attr.value}`}
                                            checked={activeFilters.some(f => f.key === 'digital' && f.value === attr.value)}
                                            onCheckedChange={(checked) => 
                                                applyFilter('digital', checked ? attr.value : null)
                                            }
                                        />
                                        <Label
                                            htmlFor={`digital-${attr.value}`}
                                            className="text-sm cursor-pointer"
                                        >
                                            {attr.label}
                                        </Label>
                                    </div>
                                    <span className="text-xs text-gray-500">{attr.count}</span>
                                </div>
                            ))}

                            {/* Featured */}
                            {facets.attributes.featured?.map((attr) => (
                                <div key={attr.label} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="featured"
                                            checked={activeFilters.some(f => f.key === 'featured')}
                                            onCheckedChange={(checked) => 
                                                applyFilter('featured', checked ? true : null)
                                            }
                                        />
                                        <Label htmlFor="featured" className="text-sm cursor-pointer">
                                            {attr.label}
                                        </Label>
                                    </div>
                                    <span className="text-xs text-gray-500">{attr.count}</span>
                                </div>
                            ))}

                            {/* On Sale */}
                            {facets.attributes.on_sale?.map((attr) => (
                                <div key={attr.label} className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="on_sale"
                                            checked={activeFilters.some(f => f.key === 'on_sale')}
                                            onCheckedChange={(checked) => 
                                                applyFilter('on_sale', checked ? true : null)
                                            }
                                        />
                                        <Label htmlFor="on_sale" className="text-sm cursor-pointer">
                                            {attr.label}
                                        </Label>
                                    </div>
                                    <span className="text-xs text-gray-500">{attr.count}</span>
                                </div>
                            ))}
                        </CardContent>
                    </CollapsibleContent>
                </Collapsible>
            </Card>

            {/* Tags */}
            {facets.tags && facets.tags.length > 0 && (
                <Card>
                    <Collapsible open={openSections.tags} onOpenChange={() => toggleSection('tags')}>
                        <CollapsibleTrigger asChild>
                            <CardHeader className="cursor-pointer hover:bg-gray-50">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-sm flex items-center gap-2">
                                        <Tag className="h-4 w-4" />
                                        Tags
                                    </CardTitle>
                                    {openSections.tags ? (
                                        <ChevronUp className="h-4 w-4" />
                                    ) : (
                                        <ChevronDown className="h-4 w-4" />
                                    )}
                                </div>
                            </CardHeader>
                        </CollapsibleTrigger>
                        <CollapsibleContent>
                            <CardContent className="pt-0 space-y-2">
                                {facets.tags.slice(0, 10).map((tagFacet) => {
                                    const isChecked = activeFilters.some(
                                        f => f.key === 'tags' && f.value === tagFacet.tag
                                    );
                                    
                                    return (
                                        <div key={tagFacet.tag} className="flex items-center justify-between">
                                            <div className="flex items-center space-x-2">
                                                <Checkbox
                                                    id={`tag-${tagFacet.tag}`}
                                                    checked={isChecked}
                                                    onCheckedChange={(checked) => 
                                                        handleTagChange(tagFacet.tag, checked as boolean)
                                                    }
                                                />
                                                <Label
                                                    htmlFor={`tag-${tagFacet.tag}`}
                                                    className="text-sm cursor-pointer"
                                                >
                                                    {tagFacet.tag}
                                                </Label>
                                            </div>
                                            <span className="text-xs text-gray-500">
                                                {tagFacet.count}
                                            </span>
                                        </div>
                                    );
                                })}
                            </CardContent>
                        </CollapsibleContent>
                    </Collapsible>
                </Card>
            )}
        </div>
    );
}
