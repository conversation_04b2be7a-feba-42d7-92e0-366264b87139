import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
    TrendingUp, 
    TrendingDown, 
    DollarSign, 
    ShoppingCart, 
    Users, 
    Target,
    ArrowUpRight,
    ArrowDownRight
} from 'lucide-react';
import { cn, formatCurrency } from '@/lib/utils';

interface SalesOverviewData {
    current: {
        total_revenue: number;
        net_revenue: number;
        total_orders: number;
        completed_orders: number;
        average_order_value: number;
        refunds: number;
        unique_customers: number;
        new_customers: number;
        conversion_rate: number;
    };
    previous: {
        total_revenue: number;
        net_revenue: number;
        total_orders: number;
        completed_orders: number;
        average_order_value: number;
        refunds: number;
        unique_customers: number;
        new_customers: number;
        conversion_rate: number;
    };
    growth: {
        total_revenue: number;
        net_revenue: number;
        total_orders: number;
        completed_orders: number;
        average_order_value: number;
        refunds: number;
        unique_customers: number;
        new_customers: number;
        conversion_rate: number;
    };
    period_days: number;
    start_date: string;
    end_date: string;
}

interface SalesOverviewProps {
    data: SalesOverviewData;
    className?: string;
}

export function SalesOverview({ data, className }: SalesOverviewProps) {
    const metrics = [
        {
            title: 'Total Revenue',
            value: data.current.total_revenue,
            growth: data.growth.total_revenue,
            icon: DollarSign,
            format: 'currency',
            color: 'blue',
        },
        {
            title: 'Net Revenue',
            value: data.current.net_revenue,
            growth: data.growth.net_revenue,
            icon: TrendingUp,
            format: 'currency',
            color: 'green',
        },
        {
            title: 'Total Orders',
            value: data.current.total_orders,
            growth: data.growth.total_orders,
            icon: ShoppingCart,
            format: 'number',
            color: 'purple',
        },
        {
            title: 'Unique Customers',
            value: data.current.unique_customers,
            growth: data.growth.unique_customers,
            icon: Users,
            format: 'number',
            color: 'orange',
        },
        {
            title: 'Average Order Value',
            value: data.current.average_order_value,
            growth: data.growth.average_order_value,
            icon: Target,
            format: 'currency',
            color: 'indigo',
        },
        {
            title: 'Conversion Rate',
            value: data.current.conversion_rate,
            growth: data.growth.conversion_rate,
            icon: TrendingUp,
            format: 'percentage',
            color: 'pink',
        },
    ];

    const formatValue = (value: number, format: string) => {
        switch (format) {
            case 'currency':
                return formatCurrency(value);
            case 'percentage':
                return `${value.toFixed(1)}%`;
            case 'number':
                return value.toLocaleString();
            default:
                return value.toString();
        }
    };

    const getGrowthColor = (growth: number) => {
        if (growth > 0) return 'text-green-600';
        if (growth < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    const getGrowthIcon = (growth: number) => {
        if (growth > 0) return ArrowUpRight;
        if (growth < 0) return ArrowDownRight;
        return null;
    };

    return (
        <div className={cn('space-y-6', className)}>
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900">Sales Overview</h2>
                    <p className="text-gray-600">
                        {data.period_days} days • {data.start_date} to {data.end_date}
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Badge variant="outline">
                        Last {data.period_days} days
                    </Badge>
                </div>
            </div>

            {/* Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {metrics.map((metric, index) => {
                    const Icon = metric.icon;
                    const GrowthIcon = getGrowthIcon(metric.growth);
                    
                    return (
                        <Card key={index} className="relative overflow-hidden">
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium text-gray-600">
                                    {metric.title}
                                </CardTitle>
                                <div className={cn(
                                    'p-2 rounded-lg',
                                    metric.color === 'blue' && 'bg-blue-100 text-blue-600',
                                    metric.color === 'green' && 'bg-green-100 text-green-600',
                                    metric.color === 'purple' && 'bg-purple-100 text-purple-600',
                                    metric.color === 'orange' && 'bg-orange-100 text-orange-600',
                                    metric.color === 'indigo' && 'bg-indigo-100 text-indigo-600',
                                    metric.color === 'pink' && 'bg-pink-100 text-pink-600',
                                )}>
                                    <Icon className="h-4 w-4" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <div className="text-2xl font-bold text-gray-900">
                                        {formatValue(metric.value, metric.format)}
                                    </div>
                                    
                                    {/* Growth Indicator */}
                                    <div className="flex items-center gap-1">
                                        {GrowthIcon && (
                                            <GrowthIcon className={cn(
                                                'h-3 w-3',
                                                getGrowthColor(metric.growth)
                                            )} />
                                        )}
                                        <span className={cn(
                                            'text-xs font-medium',
                                            getGrowthColor(metric.growth)
                                        )}>
                                            {metric.growth > 0 ? '+' : ''}{metric.growth.toFixed(1)}%
                                        </span>
                                        <span className="text-xs text-gray-500">
                                            vs previous period
                                        </span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    );
                })}
            </div>

            {/* Additional Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle className="text-sm font-medium text-gray-600">
                            Order Completion Rate
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            <div className="text-xl font-bold text-gray-900">
                                {data.current.total_orders > 0 ? 
                                    ((data.current.completed_orders / data.current.total_orders) * 100).toFixed(1) : 0}%
                            </div>
                            <div className="text-xs text-gray-500">
                                {data.current.completed_orders} of {data.current.total_orders} orders completed
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="text-sm font-medium text-gray-600">
                            New Customers
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            <div className="text-xl font-bold text-gray-900">
                                {data.current.new_customers}
                            </div>
                            <div className="flex items-center gap-1">
                                {data.growth.new_customers > 0 ? (
                                    <ArrowUpRight className="h-3 w-3 text-green-600" />
                                ) : data.growth.new_customers < 0 ? (
                                    <ArrowDownRight className="h-3 w-3 text-red-600" />
                                ) : null}
                                <span className={cn(
                                    'text-xs font-medium',
                                    getGrowthColor(data.growth.new_customers)
                                )}>
                                    {data.growth.new_customers > 0 ? '+' : ''}{data.growth.new_customers.toFixed(1)}%
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle className="text-sm font-medium text-gray-600">
                            Refunds
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-2">
                            <div className="text-xl font-bold text-gray-900">
                                {formatCurrency(data.current.refunds)}
                            </div>
                            <div className="text-xs text-gray-500">
                                {data.current.total_revenue > 0 ? 
                                    ((data.current.refunds / data.current.total_revenue) * 100).toFixed(1) : 0}% of total revenue
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

interface MetricCardProps {
    title: string;
    value: string | number;
    growth?: number;
    icon: React.ComponentType<{ className?: string }>;
    format?: 'currency' | 'number' | 'percentage';
    color?: string;
    className?: string;
}

export function MetricCard({
    title,
    value,
    growth,
    icon: Icon,
    format = 'number',
    color = 'blue',
    className
}: MetricCardProps) {
    const formatValue = (val: string | number) => {
        const numValue = typeof val === 'string' ? parseFloat(val) : val;
        
        switch (format) {
            case 'currency':
                return formatCurrency(numValue);
            case 'percentage':
                return `${numValue.toFixed(1)}%`;
            case 'number':
                return numValue.toLocaleString();
            default:
                return val.toString();
        }
    };

    const getGrowthColor = (growth?: number) => {
        if (!growth) return 'text-gray-600';
        if (growth > 0) return 'text-green-600';
        if (growth < 0) return 'text-red-600';
        return 'text-gray-600';
    };

    const getGrowthIcon = (growth?: number) => {
        if (!growth) return null;
        if (growth > 0) return ArrowUpRight;
        if (growth < 0) return ArrowDownRight;
        return null;
    };

    const GrowthIcon = getGrowthIcon(growth);

    return (
        <Card className={cn('', className)}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                    {title}
                </CardTitle>
                <div className={cn(
                    'p-2 rounded-lg',
                    color === 'blue' && 'bg-blue-100 text-blue-600',
                    color === 'green' && 'bg-green-100 text-green-600',
                    color === 'purple' && 'bg-purple-100 text-purple-600',
                    color === 'orange' && 'bg-orange-100 text-orange-600',
                    color === 'indigo' && 'bg-indigo-100 text-indigo-600',
                    color === 'pink' && 'bg-pink-100 text-pink-600',
                )}>
                    <Icon className="h-4 w-4" />
                </div>
            </CardHeader>
            <CardContent>
                <div className="space-y-2">
                    <div className="text-2xl font-bold text-gray-900">
                        {formatValue(value)}
                    </div>
                    
                    {growth !== undefined && (
                        <div className="flex items-center gap-1">
                            {GrowthIcon && (
                                <GrowthIcon className={cn(
                                    'h-3 w-3',
                                    getGrowthColor(growth)
                                )} />
                            )}
                            <span className={cn(
                                'text-xs font-medium',
                                getGrowthColor(growth)
                            )}>
                                {growth > 0 ? '+' : ''}{growth.toFixed(1)}%
                            </span>
                            <span className="text-xs text-gray-500">
                                vs previous period
                            </span>
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
