import React, { useState, useEffect } from 'react';
import { BarChart3, Loader2, Plus, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface ComparisonButtonProps {
    productId: number;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'default' | 'outline' | 'ghost';
    showText?: boolean;
    className?: string;
    onToggle?: (inComparison: boolean) => void;
}

export function ComparisonButton({
    productId,
    size = 'md',
    variant = 'outline',
    showText = true,
    className,
    onToggle
}: ComparisonButtonProps) {
    const [inComparison, setInComparison] = useState(false);
    const [loading, setLoading] = useState(false);
    const [comparisonCount, setComparisonCount] = useState(0);

    useEffect(() => {
        checkComparisonStatus();
    }, [productId]);

    const checkComparisonStatus = async () => {
        try {
            const response = await fetch(route('comparisons.current'));
            if (response.ok) {
                const data = await response.json();
                if (data.comparison) {
                    const productIds = data.comparison.product_ids || [];
                    setInComparison(productIds.includes(productId));
                    setComparisonCount(productIds.length);
                }
            }
        } catch (error) {
            console.error('Error checking comparison status:', error);
        }
    };

    const toggleComparison = async () => {
        setLoading(true);

        try {
            // Get current comparison first
            const currentResponse = await fetch(route('comparisons.current'));
            let comparisonId = null;

            if (currentResponse.ok) {
                const currentData = await currentResponse.json();
                comparisonId = currentData.comparison?.id;
            }

            // If no comparison exists and we're adding, create one
            if (!comparisonId && !inComparison) {
                const createResponse = await fetch(route('comparisons.store'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({
                        name: 'Product Comparison',
                        product_ids: [productId],
                    }),
                });

                if (createResponse.ok) {
                    setInComparison(true);
                    setComparisonCount(1);
                    onToggle?.(true);
                }
                return;
            }

            // Add or remove product from existing comparison
            if (comparisonId) {
                const endpoint = inComparison 
                    ? route('comparisons.remove-product', comparisonId)
                    : route('comparisons.add-product', comparisonId);

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({ product_id: productId }),
                });

                if (response.ok) {
                    const newStatus = !inComparison;
                    setInComparison(newStatus);
                    setComparisonCount(prev => newStatus ? prev + 1 : prev - 1);
                    onToggle?.(newStatus);
                } else {
                    const errorData = await response.json();
                    console.error('Comparison toggle error:', errorData.message);
                }
            }
        } catch (error) {
            console.error('Error toggling comparison:', error);
        } finally {
            setLoading(false);
        }
    };

    const sizeClasses = {
        sm: 'h-8 px-2',
        md: 'h-9 px-3',
        lg: 'h-10 px-4'
    };

    const iconSizes = {
        sm: 'h-3 w-3',
        md: 'h-4 w-4',
        lg: 'h-5 w-5'
    };

    return (
        <Button
            variant={variant}
            size={size}
            onClick={toggleComparison}
            disabled={loading || (!inComparison && comparisonCount >= 4)}
            className={cn(
                'transition-colors duration-200 relative',
                inComparison && 'text-blue-600 hover:text-blue-700',
                className
            )}
        >
            {loading ? (
                <Loader2 className={cn(iconSizes[size], 'animate-spin')} />
            ) : (
                <BarChart3 className={iconSizes[size]} />
            )}
            
            {showText && (
                <span className="ml-2">
                    {inComparison ? 'Remove from Compare' : 'Compare'}
                </span>
            )}

            {comparisonCount > 0 && !showText && (
                <Badge 
                    variant="secondary" 
                    className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                    {comparisonCount}
                </Badge>
            )}
        </Button>
    );
}

interface ComparisonIconButtonProps {
    productId: number;
    className?: string;
    onToggle?: (inComparison: boolean) => void;
}

export function ComparisonIconButton({
    productId,
    className,
    onToggle
}: ComparisonIconButtonProps) {
    const [inComparison, setInComparison] = useState(false);
    const [loading, setLoading] = useState(false);

    const toggleComparison = async () => {
        setLoading(true);

        try {
            // Similar logic to ComparisonButton but simplified
            const currentResponse = await fetch(route('comparisons.current'));
            let comparisonId = null;

            if (currentResponse.ok) {
                const currentData = await currentResponse.json();
                comparisonId = currentData.comparison?.id;
                if (currentData.comparison) {
                    const productIds = currentData.comparison.product_ids || [];
                    setInComparison(productIds.includes(productId));
                }
            }

            if (!comparisonId && !inComparison) {
                const createResponse = await fetch(route('comparisons.store'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({
                        name: 'Product Comparison',
                        product_ids: [productId],
                    }),
                });

                if (createResponse.ok) {
                    setInComparison(true);
                    onToggle?.(true);
                }
                return;
            }

            if (comparisonId) {
                const endpoint = inComparison 
                    ? route('comparisons.remove-product', comparisonId)
                    : route('comparisons.add-product', comparisonId);

                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    },
                    body: JSON.stringify({ product_id: productId }),
                });

                if (response.ok) {
                    const newStatus = !inComparison;
                    setInComparison(newStatus);
                    onToggle?.(newStatus);
                }
            }
        } catch (error) {
            console.error('Error toggling comparison:', error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <button
            onClick={toggleComparison}
            disabled={loading}
            className={cn(
                'p-2 rounded-full bg-white/80 hover:bg-white transition-all duration-200 shadow-sm hover:shadow-md',
                inComparison && 'text-blue-600',
                className
            )}
        >
            {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
                <BarChart3 className="h-4 w-4" />
            )}
        </button>
    );
}

interface ComparisonCounterProps {
    className?: string;
}

export function ComparisonCounter({ className }: ComparisonCounterProps) {
    const [count, setCount] = useState(0);

    useEffect(() => {
        fetchComparisonCount();
    }, []);

    const fetchComparisonCount = async () => {
        try {
            const response = await fetch(route('comparisons.current'));
            if (response.ok) {
                const data = await response.json();
                if (data.comparison) {
                    setCount(data.comparison.product_ids?.length || 0);
                }
            }
        } catch (error) {
            console.error('Error fetching comparison count:', error);
        }
    };

    if (count === 0) {
        return null;
    }

    return (
        <span className={cn(
            'inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-blue-600 rounded-full',
            className
        )}>
            {count}
        </span>
    );
}
