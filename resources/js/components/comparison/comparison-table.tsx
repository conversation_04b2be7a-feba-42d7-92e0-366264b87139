import React from 'react';
import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { StarRating } from '@/components/reviews/star-rating';
import { 
    ShoppingCart, 
    Eye, 
    X, 
    Check, 
    Minus,
    Package,
    Star,
    Heart
} from 'lucide-react';
import { cn, formatCurrency } from '@/lib/utils';

interface Product {
    id: number;
    name: string;
    slug: string;
    description?: string;
    short_description?: string;
    price: number;
    sale_price?: number;
    image?: string;
    average_rating: number;
    total_reviews: number;
    featured: boolean;
    status: string;
    category?: {
        name: string;
    };
}

interface ComparisonData {
    products: Product[];
    attributes: {
        [section: string]: {
            [key: string]: string;
        };
    };
    comparison_matrix: {
        [section: string]: {
            [key: string]: {
                label: string;
                values: Array<{
                    value: any;
                    formatted: string;
                    type: string;
                }>;
            };
        };
    };
}

interface ComparisonTableProps {
    comparisonData: ComparisonData;
    onRemoveProduct?: (productId: number) => void;
    onAddToCart?: (productId: number) => void;
    showActions?: boolean;
    className?: string;
}

export function ComparisonTable({
    comparisonData,
    onRemoveProduct,
    onAddToCart,
    showActions = true,
    className
}: ComparisonTableProps) {
    const { products, comparison_matrix } = comparisonData;

    if (!products || products.length === 0) {
        return (
            <Card className={cn('', className)}>
                <CardContent className="p-8 text-center">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        No Products to Compare
                    </h3>
                    <p className="text-gray-600">
                        Add products to your comparison to see them side by side.
                    </p>
                </CardContent>
            </Card>
        );
    }

    const renderValue = (value: any, type: string) => {
        switch (type) {
            case 'boolean':
                return value ? (
                    <Check className="h-4 w-4 text-green-600" />
                ) : (
                    <X className="h-4 w-4 text-red-600" />
                );
            
            case 'rating':
                const rating = parseFloat(value.toString());
                return (
                    <div className="flex items-center gap-1">
                        <StarRating rating={rating} size="sm" />
                        <span className="text-sm">{rating.toFixed(1)}</span>
                    </div>
                );
            
            case 'currency':
                return (
                    <span className="font-semibold">
                        {value ? formatCurrency(value) : 'N/A'}
                    </span>
                );
            
            case 'array':
                return Array.isArray(value) && value.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                        {value.slice(0, 3).map((item, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                                {item}
                            </Badge>
                        ))}
                        {value.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                                +{value.length - 3}
                            </Badge>
                        )}
                    </div>
                ) : (
                    <span className="text-gray-500">None</span>
                );
            
            default:
                return <span>{value || 'N/A'}</span>;
        }
    };

    return (
        <div className={cn('space-y-6', className)}>
            {/* Product Headers */}
            <div className="grid gap-4" style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}>
                <div></div> {/* Empty cell for attribute labels */}
                {products.map((product) => (
                    <Card key={product.id} className="relative">
                        {/* Remove Button */}
                        {showActions && onRemoveProduct && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="absolute top-2 right-2 h-6 w-6 p-0"
                                onClick={() => onRemoveProduct(product.id)}
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        )}

                        <CardContent className="p-4 text-center space-y-3">
                            {/* Product Image */}
                            <div className="aspect-square w-24 mx-auto overflow-hidden rounded-lg">
                                {product.image ? (
                                    <img
                                        src={`/storage/${product.image}`}
                                        alt={product.name}
                                        className="w-full h-full object-cover"
                                    />
                                ) : (
                                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                                        <Package className="h-8 w-8 text-gray-400" />
                                    </div>
                                )}
                            </div>

                            {/* Product Name */}
                            <div>
                                <Link href={route('products.show', product.slug)}>
                                    <h3 className="font-semibold text-sm hover:text-blue-600 transition-colors line-clamp-2">
                                        {product.name}
                                    </h3>
                                </Link>
                            </div>

                            {/* Price */}
                            <div className="space-y-1">
                                <div className="text-lg font-bold">
                                    {formatCurrency(product.sale_price || product.price)}
                                </div>
                                {product.sale_price && (
                                    <div className="text-sm text-gray-500 line-through">
                                        {formatCurrency(product.price)}
                                    </div>
                                )}
                            </div>

                            {/* Actions */}
                            {showActions && (
                                <div className="space-y-2">
                                    <Link href={route('products.show', product.slug)}>
                                        <Button variant="outline" size="sm" className="w-full">
                                            <Eye className="h-4 w-4 mr-2" />
                                            View
                                        </Button>
                                    </Link>
                                    
                                    {onAddToCart && (
                                        <Button 
                                            size="sm" 
                                            className="w-full"
                                            onClick={() => onAddToCart(product.id)}
                                        >
                                            <ShoppingCart className="h-4 w-4 mr-2" />
                                            Add to Cart
                                        </Button>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                ))}
            </div>

            {/* Comparison Matrix */}
            {Object.entries(comparison_matrix).map(([sectionKey, section]) => (
                <Card key={sectionKey}>
                    <CardHeader>
                        <CardTitle className="capitalize">
                            {sectionKey.replace('_', ' ')}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            {Object.entries(section).map(([attributeKey, attribute]) => (
                                <div 
                                    key={attributeKey}
                                    className="grid gap-4 py-2 border-b border-gray-100 last:border-b-0"
                                    style={{ gridTemplateColumns: `200px repeat(${products.length}, 1fr)` }}
                                >
                                    {/* Attribute Label */}
                                    <div className="font-medium text-gray-700 flex items-center">
                                        {attribute.label}
                                    </div>

                                    {/* Attribute Values */}
                                    {attribute.values.map((valueData, index) => (
                                        <div key={index} className="flex items-center justify-center text-center">
                                            {renderValue(valueData.value, valueData.type)}
                                        </div>
                                    ))}
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}

interface ComparisonSummaryProps {
    products: Product[];
    className?: string;
}

export function ComparisonSummary({ products, className }: ComparisonSummaryProps) {
    if (!products || products.length === 0) {
        return null;
    }

    const totalProducts = products.length;
    const averagePrice = products.reduce((sum, product) => 
        sum + (product.sale_price || product.price), 0) / totalProducts;
    const averageRating = products.reduce((sum, product) => 
        sum + product.average_rating, 0) / totalProducts;
    const featuredCount = products.filter(product => product.featured).length;

    return (
        <Card className={cn('', className)}>
            <CardHeader>
                <CardTitle>Comparison Summary</CardTitle>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {totalProducts}
                        </div>
                        <div className="text-sm text-gray-600">Products</div>
                    </div>
                    
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {formatCurrency(averagePrice)}
                        </div>
                        <div className="text-sm text-gray-600">Avg Price</div>
                    </div>
                    
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {averageRating.toFixed(1)}
                        </div>
                        <div className="text-sm text-gray-600">Avg Rating</div>
                    </div>
                    
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {featuredCount}
                        </div>
                        <div className="text-sm text-gray-600">Featured</div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
