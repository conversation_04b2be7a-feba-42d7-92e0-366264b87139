import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CartIconProps {
    itemCount?: number;
    onClick?: () => void;
    className?: string;
    showBadge?: boolean;
}

export function CartIcon({ 
    itemCount = 0, 
    onClick, 
    className,
    showBadge = true 
}: CartIconProps) {
    return (
        <Button
            variant="ghost"
            size="sm"
            onClick={onClick}
            className={cn("relative p-2", className)}
            aria-label={`Shopping cart with ${itemCount} items`}
        >
            <ShoppingCart className="h-5 w-5" />
            {showBadge && itemCount > 0 && (
                <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                    {itemCount > 99 ? '99+' : itemCount}
                </Badge>
            )}
        </Button>
    );
}
