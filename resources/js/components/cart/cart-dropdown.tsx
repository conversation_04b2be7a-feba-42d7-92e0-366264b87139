import React from 'react';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuHeader,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CartIcon } from './cart-icon';
import { ShoppingCart, Eye, CreditCard } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CartDropdownProps {
    cart: {
        items: Array<{
            id: number;
            product_id: number;
            product_name: string;
            product_image?: string;
            quantity: number;
            formatted_total_price: string;
            is_available: boolean;
        }>;
        totals: {
            formatted_total_amount: string;
        };
        item_count: number;
        is_empty: boolean;
    };
    onViewCart: () => void;
    onCheckout: () => void;
    onRemoveItem?: (itemId: number) => void;
    className?: string;
}

export function CartDropdown({
    cart,
    onViewCart,
    onCheckout,
    onRemoveItem,
    className
}: CartDropdownProps) {
    const maxDisplayItems = 3;
    const displayItems = cart.items.slice(0, maxDisplayItems);
    const remainingItems = Math.max(0, cart.items.length - maxDisplayItems);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <div className={className}>
                    <CartIcon 
                        itemCount={cart.item_count}
                        className="cursor-pointer"
                    />
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
                align="end" 
                className="w-80 p-0"
                sideOffset={8}
            >
                <div className="p-4">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-lg">Shopping Cart</h3>
                        <Badge variant="secondary">
                            {cart.item_count} {cart.item_count === 1 ? 'item' : 'items'}
                        </Badge>
                    </div>

                    {cart.is_empty ? (
                        <div className="text-center py-8">
                            <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                            <p className="text-gray-500 text-sm">Your cart is empty</p>
                        </div>
                    ) : (
                        <>
                            {/* Cart Items Preview */}
                            <div className="space-y-3 mb-4">
                                {displayItems.map((item) => (
                                    <div key={item.id} className="flex items-center space-x-3">
                                        {/* Product Image */}
                                        <div className="flex-shrink-0">
                                            {item.product_image ? (
                                                <img
                                                    src={item.product_image}
                                                    alt={item.product_name}
                                                    className="w-10 h-10 object-cover rounded"
                                                />
                                            ) : (
                                                <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                                                    <span className="text-gray-400 text-xs">No Image</span>
                                                </div>
                                            )}
                                        </div>

                                        {/* Product Details */}
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium text-gray-900 truncate">
                                                {item.product_name}
                                            </p>
                                            <div className="flex items-center justify-between">
                                                <span className="text-xs text-gray-500">
                                                    Qty: {item.quantity}
                                                </span>
                                                <span className="text-sm font-medium">
                                                    {item.formatted_total_price}
                                                </span>
                                            </div>
                                            {!item.is_available && (
                                                <Badge variant="destructive" className="text-xs mt-1">
                                                    Unavailable
                                                </Badge>
                                            )}
                                        </div>

                                        {/* Remove Button */}
                                        {onRemoveItem && (
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => onRemoveItem(item.id)}
                                                className="text-gray-400 hover:text-red-500 p-1 h-auto"
                                            >
                                                ×
                                            </Button>
                                        )}
                                    </div>
                                ))}

                                {remainingItems > 0 && (
                                    <div className="text-center py-2">
                                        <span className="text-sm text-gray-500">
                                            +{remainingItems} more {remainingItems === 1 ? 'item' : 'items'}
                                        </span>
                                    </div>
                                )}
                            </div>

                            <Separator className="my-4" />

                            {/* Total */}
                            <div className="flex justify-between items-center mb-4">
                                <span className="font-semibold">Total:</span>
                                <span className="font-semibold text-lg">
                                    {cart.totals.formatted_total_amount}
                                </span>
                            </div>

                            {/* Action Buttons */}
                            <div className="space-y-2">
                                <Button
                                    onClick={onCheckout}
                                    className="w-full"
                                    disabled={cart.items.some(item => !item.is_available)}
                                >
                                    <CreditCard className="mr-2 h-4 w-4" />
                                    Checkout
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={onViewCart}
                                    className="w-full"
                                >
                                    <Eye className="mr-2 h-4 w-4" />
                                    View Cart
                                </Button>
                            </div>
                        </>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
