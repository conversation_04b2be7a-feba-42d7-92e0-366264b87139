import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Minus, Plus, Trash2, Alert<PERSON>riangle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CartItemProps {
    item: {
        id: number;
        product_id: number;
        product_name: string;
        product_sku?: string;
        product_image?: string;
        quantity: number;
        unit_price: number;
        total_price: number;
        formatted_unit_price: string;
        formatted_total_price: string;
        is_available: boolean;
        options?: Record<string, any>;
    };
    onUpdateQuantity: (itemId: number, quantity: number) => void;
    onRemove: (itemId: number) => void;
    isUpdating?: boolean;
    className?: string;
}

export function CartItem({ 
    item, 
    onUpdateQuantity, 
    onRemove, 
    isUpdating = false,
    className 
}: CartItemProps) {
    const [quantity, setQuantity] = useState(item.quantity);
    const [isLocalUpdating, setIsLocalUpdating] = useState(false);

    const handleQuantityChange = async (newQuantity: number) => {
        if (newQuantity < 1 || newQuantity > 100) return;
        
        setQuantity(newQuantity);
        setIsLocalUpdating(true);
        
        try {
            await onUpdateQuantity(item.id, newQuantity);
        } finally {
            setIsLocalUpdating(false);
        }
    };

    const handleRemove = async () => {
        setIsLocalUpdating(true);
        try {
            await onRemove(item.id);
        } finally {
            setIsLocalUpdating(false);
        }
    };

    const isDisabled = isUpdating || isLocalUpdating || !item.is_available;

    return (
        <Card className={cn("w-full", className)}>
            <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                        {item.product_image ? (
                            <img
                                src={item.product_image}
                                alt={item.product_name}
                                className="w-16 h-16 object-cover rounded-md"
                            />
                        ) : (
                            <div className="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                <span className="text-gray-400 text-xs">No Image</span>
                            </div>
                        )}
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                            <div>
                                <h3 className="text-sm font-medium text-gray-900 truncate">
                                    {item.product_name}
                                </h3>
                                {item.product_sku && (
                                    <p className="text-xs text-gray-500 mt-1">
                                        SKU: {item.product_sku}
                                    </p>
                                )}
                                {!item.is_available && (
                                    <div className="flex items-center mt-2">
                                        <AlertTriangle className="h-4 w-4 text-amber-500 mr-1" />
                                        <Badge variant="secondary" className="text-xs">
                                            Unavailable
                                        </Badge>
                                    </div>
                                )}
                            </div>
                            
                            {/* Remove Button */}
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={handleRemove}
                                disabled={isDisabled}
                                className="text-gray-400 hover:text-red-500 p-1"
                                aria-label="Remove item"
                            >
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        </div>

                        {/* Options */}
                        {item.options && Object.keys(item.options).length > 0 && (
                            <div className="mt-2">
                                {Object.entries(item.options).map(([key, value]) => (
                                    <span key={key} className="text-xs text-gray-500 mr-2">
                                        {key}: {String(value)}
                                    </span>
                                ))}
                            </div>
                        )}

                        {/* Quantity and Price */}
                        <div className="flex items-center justify-between mt-3">
                            <div className="flex items-center space-x-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleQuantityChange(quantity - 1)}
                                    disabled={isDisabled || quantity <= 1}
                                    className="h-8 w-8 p-0"
                                >
                                    <Minus className="h-3 w-3" />
                                </Button>
                                
                                <Input
                                    type="number"
                                    min="1"
                                    max="100"
                                    value={quantity}
                                    onChange={(e) => {
                                        const newQuantity = parseInt(e.target.value) || 1;
                                        handleQuantityChange(newQuantity);
                                    }}
                                    disabled={isDisabled}
                                    className="h-8 w-16 text-center"
                                />
                                
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleQuantityChange(quantity + 1)}
                                    disabled={isDisabled || quantity >= 100}
                                    className="h-8 w-8 p-0"
                                >
                                    <Plus className="h-3 w-3" />
                                </Button>
                            </div>

                            <div className="text-right">
                                <div className="text-sm font-medium text-gray-900">
                                    {item.formatted_total_price}
                                </div>
                                <div className="text-xs text-gray-500">
                                    {item.formatted_unit_price} each
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
