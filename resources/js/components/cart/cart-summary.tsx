import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ShoppingCart, CreditCard } from 'lucide-react';

interface CartSummaryProps {
    totals: {
        subtotal: number;
        tax_amount: number;
        discount_amount: number;
        total_amount: number;
        formatted_subtotal: string;
        formatted_tax_amount: string;
        formatted_discount_amount: string;
        formatted_total_amount: string;
    };
    itemCount: number;
    onCheckout?: () => void;
    onContinueShopping?: () => void;
    isCheckoutDisabled?: boolean;
    className?: string;
}

export function CartSummary({
    totals,
    itemCount,
    onCheckout,
    onContinueShopping,
    isCheckoutDisabled = false,
    className
}: CartSummaryProps) {
    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center text-lg">
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    Order Summary
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                {/* Item Count */}
                <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                        {itemCount} {itemCount === 1 ? 'item' : 'items'}
                    </span>
                </div>

                <Separator />

                {/* Pricing Breakdown */}
                <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Subtotal</span>
                        <span className="font-medium">{totals.formatted_subtotal}</span>
                    </div>

                    {totals.tax_amount > 0 && (
                        <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Tax</span>
                            <span className="font-medium">{totals.formatted_tax_amount}</span>
                        </div>
                    )}

                    {totals.discount_amount > 0 && (
                        <div className="flex justify-between text-sm text-green-600">
                            <span>Discount</span>
                            <span className="font-medium">-{totals.formatted_discount_amount}</span>
                        </div>
                    )}
                </div>

                <Separator />

                {/* Total */}
                <div className="flex justify-between text-lg font-semibold">
                    <span>Total</span>
                    <span>{totals.formatted_total_amount}</span>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3 pt-4">
                    <Button
                        onClick={onCheckout}
                        disabled={isCheckoutDisabled || itemCount === 0}
                        className="w-full"
                        size="lg"
                    >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Proceed to Checkout
                    </Button>

                    {onContinueShopping && (
                        <Button
                            variant="outline"
                            onClick={onContinueShopping}
                            className="w-full"
                        >
                            Continue Shopping
                        </Button>
                    )}
                </div>

                {/* Additional Info */}
                <div className="text-xs text-gray-500 text-center pt-2">
                    <p>Secure checkout with SSL encryption</p>
                    <p className="mt-1">Digital products available immediately after purchase</p>
                </div>
            </CardContent>
        </Card>
    );
}
