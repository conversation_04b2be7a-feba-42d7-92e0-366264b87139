import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CartItem } from './cart-item';
import { CartSummary } from './cart-summary';
import { ShoppingCart, AlertCircle, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CartProps {
    cart: {
        items: Array<{
            id: number;
            product_id: number;
            product_name: string;
            product_sku?: string;
            product_image?: string;
            quantity: number;
            unit_price: number;
            total_price: number;
            formatted_unit_price: string;
            formatted_total_price: string;
            is_available: boolean;
            options?: Record<string, any>;
        }>;
        totals: {
            subtotal: number;
            tax_amount: number;
            discount_amount: number;
            total_amount: number;
            formatted_subtotal: string;
            formatted_tax_amount: string;
            formatted_discount_amount: string;
            formatted_total_amount: string;
        };
        item_count: number;
        is_empty: boolean;
    };
    onUpdateQuantity: (itemId: number, quantity: number) => Promise<void>;
    onRemoveItem: (itemId: number) => Promise<void>;
    onClearCart: () => Promise<void>;
    onCheckout: () => void;
    onContinueShopping?: () => void;
    isLoading?: boolean;
    className?: string;
}

export function Cart({
    cart,
    onUpdateQuantity,
    onRemoveItem,
    onClearCart,
    onCheckout,
    onContinueShopping,
    isLoading = false,
    className
}: CartProps) {
    const [isClearing, setIsClearing] = useState(false);

    const handleClearCart = async () => {
        if (window.confirm('Are you sure you want to clear your cart?')) {
            setIsClearing(true);
            try {
                await onClearCart();
            } finally {
                setIsClearing(false);
            }
        }
    };

    const hasUnavailableItems = cart.items.some(item => !item.is_available);

    if (cart.is_empty) {
        return (
            <div className={cn("max-w-4xl mx-auto p-6", className)}>
                <Card>
                    <CardContent className="flex flex-col items-center justify-center py-12">
                        <ShoppingCart className="h-16 w-16 text-gray-300 mb-4" />
                        <h2 className="text-xl font-semibold text-gray-900 mb-2">
                            Your cart is empty
                        </h2>
                        <p className="text-gray-500 text-center mb-6">
                            Looks like you haven't added any items to your cart yet.
                        </p>
                        {onContinueShopping && (
                            <Button onClick={onContinueShopping}>
                                Start Shopping
                            </Button>
                        )}
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className={cn("max-w-6xl mx-auto p-6", className)}>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Cart Items */}
                <div className="lg:col-span-2">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between">
                            <CardTitle className="flex items-center">
                                <ShoppingCart className="mr-2 h-5 w-5" />
                                Shopping Cart ({cart.item_count} {cart.item_count === 1 ? 'item' : 'items'})
                            </CardTitle>
                            {cart.items.length > 0 && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={handleClearCart}
                                    disabled={isClearing || isLoading}
                                    className="text-red-600 hover:text-red-700"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Clear Cart
                                </Button>
                            )}
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {/* Unavailable Items Warning */}
                            {hasUnavailableItems && (
                                <Alert>
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        Some items in your cart are no longer available. 
                                        Please review your cart before proceeding to checkout.
                                    </AlertDescription>
                                </Alert>
                            )}

                            {/* Cart Items List */}
                            <div className="space-y-4">
                                {cart.items.map((item) => (
                                    <CartItem
                                        key={item.id}
                                        item={item}
                                        onUpdateQuantity={onUpdateQuantity}
                                        onRemove={onRemoveItem}
                                        isUpdating={isLoading}
                                    />
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Cart Summary */}
                <div className="lg:col-span-1">
                    <div className="sticky top-6">
                        <CartSummary
                            totals={cart.totals}
                            itemCount={cart.item_count}
                            onCheckout={onCheckout}
                            onContinueShopping={onContinueShopping}
                            isCheckoutDisabled={hasUnavailableItems || isLoading}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}
