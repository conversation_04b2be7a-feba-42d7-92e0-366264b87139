import { AdminNavMain } from '@/components/nav-main-original';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import {
    LayoutGrid,
    Users,
    Package,
    FileText,
    BarChart3,
    CreditCard,
    Mail,
    Settings,
    Megaphone,
    Wrench,
    Upload
} from 'lucide-react';

const adminNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/admin',
        icon: LayoutGrid,
    },
    {
        title: 'Users',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'Products',
        href: '/admin/products',
        icon: Package,
    },
    {
        title: 'Prestock',
        href: '/admin/prestock',
        icon: Package,
    },
    {
        title: 'Files',
        href: '/admin/files',
        icon: FileText,
    },
    {
        title: 'Pages',
        href: '/admin/pages',
        icon: FileText,
    },
    {
        title: 'Articles',
        href: '/admin/articles',
        icon: FileText,
    },
    {
        title: 'Announcements',
        href: '/admin/announcements',
        icon: Megaphone,
    },
    {
        title: 'Billing',
        href: '/admin/billing',
        icon: CreditCard,
        items: [
            {
                title: 'Invoices',
                href: '/admin/billing/invoices',
                icon: FileText,
            },
            {
                title: 'Payments',
                href: '/admin/billing/payments',
                icon: CreditCard,
            },
        ],
    },
    {
        title: 'DataTables',
        href: '/admin/datatables',
        icon: BarChart3,
        items: [
            {
                title: 'Users Table',
                href: '/admin/datatables/users',
                icon: Users,
            },
            {
                title: 'Products Table',
                href: '/admin/datatables/products',
                icon: Package,
            },
        ],
    },
    {
        title: 'System',
        href: '/admin/system',
        icon: Settings,
        items: [
            {
                title: 'Settings',
                href: '/admin/system/settings',
                icon: Settings,
            },
            {
                title: 'Logs',
                href: '/admin/system/logs',
                icon: FileText,
            },
        ],
    },
    {
        title: 'Options',
        href: '/admin/options',
        icon: Settings,
        items: [
            {
                title: 'General',
                href: '/admin/options/general',
                icon: Settings,
            },
            {
                title: 'Email',
                href: '/admin/options/email',
                icon: Mail,
            },
        ],
    },
    {
        title: 'Tools',
        href: '/admin/tools',
        icon: Wrench,
        items: [
            {
                title: 'Import/Export',
                href: '/admin/tools/import-export',
                icon: Upload,
            },
            {
                title: 'Maintenance',
                href: '/admin/tools/maintenance',
                icon: Wrench,
            },
        ],
    },
];

export function AdminSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="bg-slate-700 border-slate-600">
            <SidebarHeader className="bg-purple-600 border-purple-700 px-4 py-3">
                <div className="text-white font-semibold text-lg">
                    Download Center
                </div>
            </SidebarHeader>

            <SidebarContent className="bg-slate-700 px-0">
                <AdminNavMain items={adminNavItems} />
            </SidebarContent>

            <SidebarFooter className="bg-slate-700 border-slate-600 px-4 py-3">
                <div className="text-slate-400 text-sm">
                    Version 1.8.0
                </div>
            </SidebarFooter>
        </Sidebar>
    );
}
