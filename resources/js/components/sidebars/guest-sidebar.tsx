import { GuestNavMain } from '@/components/nav-main-original';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import {
    Home,
    Package,
    Info,
    Mail,
    LogIn,
    UserPlus
} from 'lucide-react';

const guestNavItems: NavItem[] = [
    {
        title: 'Home',
        href: '/',
        icon: Home,
    },
    {
        title: 'Products',
        href: '/products',
        icon: Package,
    },
    {
        title: 'About',
        href: '/about',
        icon: Info,
    },
    {
        title: 'Contact',
        href: '/contact',
        icon: Mail,
    },
    {
        title: 'Login',
        href: '/login',
        icon: LogIn,
    },
    {
        title: 'Register',
        href: '/register',
        icon: UserPlus,
    },
];

export function GuestSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="bg-slate-700 border-slate-600">
            <SidebarHeader className="bg-purple-600 border-purple-700 px-4 py-3">
                <div className="text-white font-semibold text-lg">
                    Download Center
                </div>
            </SidebarHeader>

            <SidebarContent className="bg-slate-700 px-0">
                <GuestNavMain items={guestNavItems} />
            </SidebarContent>

            <SidebarFooter className="bg-slate-700 border-slate-600 px-4 py-3">
                <div className="text-slate-400 text-sm">
                    Version 1.8.0
                </div>
            </SidebarFooter>
        </Sidebar>
    );
}
