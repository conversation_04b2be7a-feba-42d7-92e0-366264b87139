import { SuperAdminNavMain } from '@/components/nav-main-original';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import {
    LayoutGrid,
    Users,
    Package,
    FolderTree,
    Settings,
    Database,
    Activity,
    FileText,
    BarChart3,
    CreditCard,
    Mail,
    Bell,
    TrendingUp,
    Download,
    Search,
    ShoppingCart
} from 'lucide-react';

const superAdminNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/super-admin/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Users',
        href: '/super-admin/users',
        icon: Users,
    },
    {
        title: 'Products',
        href: '/super-admin/products',
        icon: Package,
    },
    {
        title: 'Prestock',
        href: '/super-admin/prestock',
        icon: Package,
    },
    {
        title: 'Files',
        href: '/super-admin/files',
        icon: FolderTree,
    },
    {
        title: 'Pages',
        href: '/super-admin/pages',
        icon: FileText,
    },
    {
        title: 'Articles',
        href: '/super-admin/articles',
        icon: FileText,
    },
    {
        title: 'Announcements',
        href: '/super-admin/announcements',
        icon: Bell,
    },
    {
        title: 'Billing',
        href: '/super-admin/billing',
        icon: CreditCard,
        items: [
            {
                title: 'Orders',
                href: '/super-admin/billing/orders',
                icon: Package,
            },
            {
                title: 'Invoices',
                href: '/super-admin/billing/invoices',
                icon: FileText,
            },
            {
                title: 'Transactions',
                href: '/super-admin/billing/transactions',
                icon: CreditCard,
            },
        ],
    },
    {
        title: 'DataTables',
        href: '/super-admin/datatables',
        icon: BarChart3,
        items: [
            {
                title: 'Bill Items',
                href: '/super-admin/datatables/bill-items',
                icon: FileText,
            },
            {
                title: 'Transfers',
                href: '/super-admin/datatables/transfers',
                icon: CreditCard,
            },
            {
                title: 'Products',
                href: '/super-admin/datatables/products',
                icon: Package,
            },
            {
                title: 'Files',
                href: '/super-admin/datatables/files',
                icon: FolderTree,
            },
            {
                title: 'Articles',
                href: '/super-admin/datatables/articles',
                icon: FileText,
            },
            {
                title: 'Reviews',
                href: '/super-admin/datatables/reviews',
                icon: FileText,
            },
            {
                title: 'Users Packages',
                href: '/super-admin/datatables/users-packages',
                icon: Package,
            },
            {
                title: 'Users Downloads',
                href: '/super-admin/datatables/users-downloads',
                icon: FolderTree,
            },
            {
                title: 'Download Visitors',
                href: '/super-admin/datatables/download-visitors',
                icon: Users,
            },
            {
                title: 'Staff Activity',
                href: '/super-admin/datatables/staff-activity',
                icon: Activity,
            },
        ],
    },
    {
        title: 'System',
        href: '/super-admin/system',
        icon: Database,
        items: [
            {
                title: 'Payment Gateways',
                href: '/super-admin/system/payment-gateways',
                icon: CreditCard,
            },
            {
                title: 'Modules',
                href: '/super-admin/system/modules',
                icon: Package,
            },
            {
                title: 'Shipping Rates',
                href: '/super-admin/system/shipping-rates',
                icon: Package,
            },
            {
                title: 'Tax Classes',
                href: '/super-admin/system/tax-classes',
                icon: FileText,
            },
            {
                title: 'Coupons',
                href: '/super-admin/system/coupons',
                icon: FileText,
            },
            {
                title: 'Mail Templates',
                href: '/super-admin/system/mail-templates',
                icon: Mail,
            },
            {
                title: 'Permissions Templates',
                href: '/super-admin/system/permissions-templates',
                icon: Settings,
            },
            {
                title: 'User Groups',
                href: '/super-admin/system/user-groups',
                icon: Users,
            },
            {
                title: 'API Manager',
                href: '/super-admin/system/api-manager',
                icon: Settings,
            },
            {
                title: 'Download Packages',
                href: '/super-admin/system/download-packages',
                icon: Package,
            },
            {
                title: 'Download Servers',
                href: '/super-admin/system/download-servers',
                icon: Database,
            },
            {
                title: 'IP Blacklist',
                href: '/super-admin/system/ip-blacklist',
                icon: Settings,
            },
            {
                title: 'Cron Tasks',
                href: '/super-admin/system/cron-tasks',
                icon: Settings,
            },
            {
                title: 'S3 Dashboard',
                href: '/super-admin/system/s3-dashboard',
                icon: Database,
            },
            {
                title: 'Software License',
                href: '/super-admin/system/software-license',
                icon: FileText,
            },
        ],
    },
    {
        title: 'Options',
        href: '/super-admin/options',
        icon: Settings,
        items: [
            {
                title: 'System Options',
                href: '/super-admin/options/system-options',
                icon: Settings,
            },
            {
                title: 'Interface Template',
                href: '/super-admin/options/interface-template',
                icon: FileText,
            },
            {
                title: 'Dashboard Template',
                href: '/super-admin/options/dashboard-template',
                icon: LayoutGrid,
            },
            {
                title: 'E-Mail / SMTP',
                href: '/super-admin/options/email-smtp',
                icon: Mail,
            },
            {
                title: 'Download',
                href: '/super-admin/options/download',
                icon: FolderTree,
            },
            {
                title: 'Cron Tasks',
                href: '/super-admin/options/cron-tasks',
                icon: Settings,
            },
            {
                title: 'S3 Integration',
                href: '/super-admin/options/s3-integration',
                icon: Database,
            },
        ],
    },
    {
        title: 'Tools',
        href: '/super-admin/tools',
        icon: Settings,
        items: [
            {
                title: 'Reports',
                href: '/super-admin/tools/reports',
                icon: BarChart3,
            },
            {
                title: 'Analytics',
                href: '/super-admin/analytics',
                icon: TrendingUp,
                items: [
                    {
                        title: 'Sales Analytics',
                        href: '/super-admin/analytics/sales',
                        icon: ShoppingCart,
                    },
                    {
                        title: 'Product Performance',
                        href: '/super-admin/analytics/products',
                        icon: Package,
                    },
                    {
                        title: 'Download Statistics',
                        href: '/super-admin/analytics/downloads',
                        icon: Download,
                    },
                    {
                        title: 'Customer Analytics',
                        href: '/super-admin/analytics/customers',
                        icon: Users,
                    },
                    {
                        title: 'Search Analytics',
                        href: '/super-admin/analytics/search',
                        icon: Search,
                    },
                ],
            },
            {
                title: 'Bulk Mail',
                href: '/super-admin/tools/bulk-mail',
                icon: Mail,
            },
            {
                title: 'Balance Transfer',
                href: '/super-admin/tools/balance-transfer',
                icon: CreditCard,
            },
        ],
    },
];



export function SuperAdminSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="bg-slate-700 border-slate-600">
            <SidebarHeader className="bg-purple-600 border-purple-700 px-4 py-3">
                <div className="text-white font-semibold text-lg">
                    Download Center
                </div>
            </SidebarHeader>

            <SidebarContent className="bg-slate-700 px-0">
                <SuperAdminNavMain items={superAdminNavItems} />
            </SidebarContent>

            <SidebarFooter className="bg-slate-700 border-slate-600 px-4 py-3">
                <div className="text-slate-400 text-sm">
                    Version 1.8.0
                </div>
            </SidebarFooter>
        </Sidebar>
    );
}
