import { UserNavMain } from '@/components/nav-main-original';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import {
    LayoutGrid,
    ShoppingCart,
    Download,
    User,
    Heart,
    CreditCard,
    FileText,
    Package
} from 'lucide-react';

const userNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'My Products',
        href: '/my-products',
        icon: Package,
    },
    {
        title: 'Downloads',
        href: '/downloads',
        icon: Download,
    },
    {
        title: 'Orders',
        href: '/orders',
        icon: ShoppingCart,
    },
    {
        title: 'Wishlist',
        href: '/wishlist',
        icon: Heart,
    },
    {
        title: 'Invoices',
        href: '/invoices',
        icon: FileText,
    },
    {
        title: 'Payment Methods',
        href: '/payment-methods',
        icon: CreditCard,
    },
    {
        title: 'Profile',
        href: '/profile',
        icon: User,
    },
];

export function UserSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset" className="bg-slate-700 border-slate-600">
            <SidebarHeader className="bg-purple-600 border-purple-700 px-4 py-3">
                <div className="text-white font-semibold text-lg">
                    Download Center
                </div>
            </SidebarHeader>

            <SidebarContent className="bg-slate-700 px-0">
                <UserNavMain items={userNavItems} />
            </SidebarContent>

            <SidebarFooter className="bg-slate-700 border-slate-600 px-4 py-3">
                <div className="text-slate-400 text-sm">
                    Version 1.8.0
                </div>
            </SidebarFooter>
        </Sidebar>
    );
}
