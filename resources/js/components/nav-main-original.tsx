import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { ChevronRight } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface NavMainOriginalProps {
    items: NavItem[];
}

export function SuperAdminNavMain({ items }: NavMainOriginalProps) {
    const page = usePage();
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (itemTitle: string) => {
        setExpandedItems(prev => 
            prev.includes(itemTitle) 
                ? prev.filter(title => title !== itemTitle)
                : [...prev, itemTitle]
        );
    };

    return (
        <div className="py-2">
            {items.map((item) => {
                const hasSubItems = item.items && item.items.length > 0;
                const isActive = page.url.startsWith(item.href);
                const hasActiveSubItem = hasSubItems && item.items?.some(subItem => page.url.startsWith(subItem.href));
                const isExpanded = expandedItems.includes(item.title);
                const shouldShowAsActive = isActive || hasActiveSubItem;

                if (hasSubItems) {
                    return (
                        <div key={item.title}>
                            <button
                                onClick={() => toggleExpanded(item.title)}
                                className={cn(
                                    "w-full flex items-center justify-between px-4 py-2.5 text-left text-sm transition-colors",
                                    shouldShowAsActive 
                                        ? "text-white bg-slate-600/50" 
                                        : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                                )}
                            >
                                <div className="flex items-center gap-3">
                                    {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                                    <span>{item.title}</span>
                                </div>
                                <ChevronRight 
                                    className={cn(
                                        "h-4 w-4 transition-transform duration-200",
                                        isExpanded ? "rotate-90" : ""
                                    )} 
                                />
                            </button>
                            
                            {isExpanded && (
                                <div className="bg-slate-800/50">
                                    {item.items?.map((subItem) => (
                                        <Link
                                            key={subItem.title}
                                            href={subItem.href}
                                            className={cn(
                                                "flex items-center gap-3 px-4 py-2 pl-11 text-sm transition-colors",
                                                page.url.startsWith(subItem.href)
                                                    ? "text-white bg-slate-600/50"
                                                    : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                                            )}
                                        >
                                            {subItem.icon && <subItem.icon className="h-4 w-4 flex-shrink-0" />}
                                            <span>{subItem.title}</span>
                                        </Link>
                                    ))}
                                </div>
                            )}
                        </div>
                    );
                }

                return (
                    <Link
                        key={item.title}
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 px-4 py-2.5 text-sm transition-colors",
                            isActive 
                                ? "text-white bg-slate-600/50" 
                                : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                        )}
                    >
                        {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                        <span>{item.title}</span>
                    </Link>
                );
            })}
        </div>
    );
}

export function AdminNavMain({ items }: NavMainOriginalProps) {
    const page = usePage();
    const [expandedItems, setExpandedItems] = useState<string[]>([]);

    const toggleExpanded = (itemTitle: string) => {
        setExpandedItems(prev => 
            prev.includes(itemTitle) 
                ? prev.filter(title => title !== itemTitle)
                : [...prev, itemTitle]
        );
    };

    return (
        <div className="py-2">
            {items.map((item) => {
                const hasSubItems = item.items && item.items.length > 0;
                const isActive = page.url.startsWith(item.href);
                const hasActiveSubItem = hasSubItems && item.items?.some(subItem => page.url.startsWith(subItem.href));
                const isExpanded = expandedItems.includes(item.title);
                const shouldShowAsActive = isActive || hasActiveSubItem;

                if (hasSubItems) {
                    return (
                        <div key={item.title}>
                            <button
                                onClick={() => toggleExpanded(item.title)}
                                className={cn(
                                    "w-full flex items-center justify-between px-4 py-2.5 text-left text-sm transition-colors",
                                    shouldShowAsActive 
                                        ? "text-white bg-slate-600/50" 
                                        : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                                )}
                            >
                                <div className="flex items-center gap-3">
                                    {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                                    <span>{item.title}</span>
                                </div>
                                <ChevronRight 
                                    className={cn(
                                        "h-4 w-4 transition-transform duration-200",
                                        isExpanded ? "rotate-90" : ""
                                    )} 
                                />
                            </button>
                            
                            {isExpanded && (
                                <div className="bg-slate-800/50">
                                    {item.items?.map((subItem) => (
                                        <Link
                                            key={subItem.title}
                                            href={subItem.href}
                                            className={cn(
                                                "flex items-center gap-3 px-4 py-2 pl-11 text-sm transition-colors",
                                                page.url.startsWith(subItem.href)
                                                    ? "text-white bg-slate-600/50"
                                                    : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                                            )}
                                        >
                                            {subItem.icon && <subItem.icon className="h-4 w-4 flex-shrink-0" />}
                                            <span>{subItem.title}</span>
                                        </Link>
                                    ))}
                                </div>
                            )}
                        </div>
                    );
                }

                return (
                    <Link
                        key={item.title}
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 px-4 py-2.5 text-sm transition-colors",
                            isActive 
                                ? "text-white bg-slate-600/50" 
                                : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                        )}
                    >
                        {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                        <span>{item.title}</span>
                    </Link>
                );
            })}
        </div>
    );
}

export function UserNavMain({ items }: NavMainOriginalProps) {
    const page = usePage();

    return (
        <div className="py-2">
            {items.map((item) => {
                const isActive = page.url.startsWith(item.href);

                return (
                    <Link
                        key={item.title}
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 px-4 py-2.5 text-sm transition-colors",
                            isActive 
                                ? "text-white bg-slate-600/50" 
                                : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                        )}
                    >
                        {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                        <span>{item.title}</span>
                    </Link>
                );
            })}
        </div>
    );
}

export function GuestNavMain({ items }: NavMainOriginalProps) {
    const page = usePage();

    return (
        <div className="py-2">
            {items.map((item) => {
                const isActive = page.url.startsWith(item.href);

                return (
                    <Link
                        key={item.title}
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 px-4 py-2.5 text-sm transition-colors",
                            isActive 
                                ? "text-white bg-slate-600/50" 
                                : "text-slate-300 hover:text-white hover:bg-slate-600/30"
                        )}
                    >
                        {item.icon && <item.icon className="h-4 w-4 flex-shrink-0" />}
                        <span>{item.title}</span>
                    </Link>
                );
            })}
        </div>
    );
}
