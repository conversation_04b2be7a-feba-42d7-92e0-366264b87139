import React from 'react';
import { useForm } from '@inertiajs/react';
import { StarRating } from './star-rating';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

interface ReviewFormData {
    title: string;
    content: string;
    rating: number;
}

interface ReviewFormProps {
    productId: number;
    initialData?: Partial<ReviewFormData>;
    isEditing?: boolean;
    onSuccess?: () => void;
    onCancel?: () => void;
    className?: string;
}

export function ReviewForm({
    productId,
    initialData,
    isEditing = false,
    onSuccess,
    onCancel,
    className
}: ReviewFormProps) {
    const { data, setData, post, put, processing, errors, reset } = useForm<ReviewFormData>({
        title: initialData?.title || '',
        content: initialData?.content || '',
        rating: initialData?.rating || 0,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (data.rating === 0) {
            return;
        }

        const url = isEditing 
            ? route('products.reviews.update', { product: productId, review: initialData?.id })
            : route('products.reviews.store', productId);

        const method = isEditing ? put : post;

        method(url, {
            onSuccess: () => {
                if (!isEditing) {
                    reset();
                }
                onSuccess?.();
            }
        });
    };

    const handleRatingChange = (rating: number) => {
        setData('rating', rating);
    };

    return (
        <Card className={cn('w-full', className)}>
            <CardHeader>
                <CardTitle>
                    {isEditing ? 'Edit Your Review' : 'Write a Review'}
                </CardTitle>
            </CardHeader>
            
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Rating */}
                    <div className="space-y-2">
                        <Label>Rating *</Label>
                        <div className="flex items-center gap-2">
                            <StarRating
                                rating={data.rating}
                                interactive
                                onRatingChange={handleRatingChange}
                                size="lg"
                            />
                            <span className="text-sm text-gray-600">
                                {data.rating > 0 ? `${data.rating} star${data.rating !== 1 ? 's' : ''}` : 'Click to rate'}
                            </span>
                        </div>
                        {errors.rating && (
                            <p className="text-sm text-red-600">{errors.rating}</p>
                        )}
                    </div>

                    {/* Title */}
                    <div className="space-y-2">
                        <Label htmlFor="title">Review Title *</Label>
                        <Input
                            id="title"
                            type="text"
                            value={data.title}
                            onChange={(e) => setData('title', e.target.value)}
                            placeholder="Summarize your experience"
                            maxLength={255}
                            className={errors.title ? 'border-red-500' : ''}
                        />
                        {errors.title && (
                            <p className="text-sm text-red-600">{errors.title}</p>
                        )}
                    </div>

                    {/* Content */}
                    <div className="space-y-2">
                        <Label htmlFor="content">Review Content *</Label>
                        <Textarea
                            id="content"
                            value={data.content}
                            onChange={(e) => setData('content', e.target.value)}
                            placeholder="Share your thoughts about this product..."
                            rows={6}
                            maxLength={2000}
                            className={errors.content ? 'border-red-500' : ''}
                        />
                        <div className="flex justify-between text-sm text-gray-500">
                            <span>{errors.content && <span className="text-red-600">{errors.content}</span>}</span>
                            <span>{data.content.length}/2000</span>
                        </div>
                    </div>

                    {/* Info Alert */}
                    <Alert>
                        <AlertDescription>
                            Your review will be published after approval by our moderation team. 
                            Please ensure your review is honest, helpful, and follows our community guidelines.
                        </AlertDescription>
                    </Alert>

                    {/* Actions */}
                    <div className="flex items-center gap-3">
                        <Button
                            type="submit"
                            disabled={processing || data.rating === 0}
                            className="min-w-[120px]"
                        >
                            {processing ? 'Submitting...' : (isEditing ? 'Update Review' : 'Submit Review')}
                        </Button>
                        
                        {onCancel && (
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onCancel}
                                disabled={processing}
                            >
                                Cancel
                            </Button>
                        )}
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}

interface ReviewPromptProps {
    productName: string;
    canReview: boolean;
    hasReviewed: boolean;
    onWriteReview: () => void;
    className?: string;
}

export function ReviewPrompt({
    productName,
    canReview,
    hasReviewed,
    onWriteReview,
    className
}: ReviewPromptProps) {
    if (hasReviewed) {
        return (
            <Alert className={cn('', className)}>
                <AlertDescription>
                    You have already reviewed this product. You can edit your review if needed.
                </AlertDescription>
            </Alert>
        );
    }

    if (!canReview) {
        return (
            <Alert className={cn('', className)}>
                <AlertDescription>
                    You need to purchase this product before you can write a review.
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <Card className={cn('', className)}>
            <CardContent className="pt-6">
                <div className="text-center space-y-4">
                    <div>
                        <h3 className="text-lg font-semibold">Share Your Experience</h3>
                        <p className="text-gray-600">
                            Help others by writing a review for {productName}
                        </p>
                    </div>
                    
                    <Button onClick={onWriteReview}>
                        Write a Review
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
