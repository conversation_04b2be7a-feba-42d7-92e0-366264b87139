import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
    rating: number;
    maxRating?: number;
    size?: 'sm' | 'md' | 'lg';
    showValue?: boolean;
    interactive?: boolean;
    onRatingChange?: (rating: number) => void;
    className?: string;
}

export function StarRating({
    rating,
    maxRating = 5,
    size = 'md',
    showValue = false,
    interactive = false,
    onRatingChange,
    className
}: StarRatingProps) {
    const [hoverRating, setHoverRating] = React.useState(0);

    const sizeClasses = {
        sm: 'h-4 w-4',
        md: 'h-5 w-5',
        lg: 'h-6 w-6'
    };

    const handleStarClick = (starRating: number) => {
        if (interactive && onRatingChange) {
            onRatingChange(starRating);
        }
    };

    const handleStarHover = (starRating: number) => {
        if (interactive) {
            setHoverRating(starRating);
        }
    };

    const handleMouseLeave = () => {
        if (interactive) {
            setHoverRating(0);
        }
    };

    const getStarFill = (starIndex: number) => {
        const currentRating = interactive && hoverRating > 0 ? hoverRating : rating;
        
        if (starIndex <= currentRating) {
            return 'fill-yellow-400 text-yellow-400';
        } else if (starIndex - 0.5 <= currentRating) {
            return 'fill-yellow-400/50 text-yellow-400';
        } else {
            return 'fill-gray-200 text-gray-200';
        }
    };

    return (
        <div className={cn('flex items-center gap-1', className)}>
            <div 
                className="flex items-center"
                onMouseLeave={handleMouseLeave}
            >
                {Array.from({ length: maxRating }, (_, index) => {
                    const starIndex = index + 1;
                    return (
                        <Star
                            key={starIndex}
                            className={cn(
                                sizeClasses[size],
                                getStarFill(starIndex),
                                interactive && 'cursor-pointer hover:scale-110 transition-transform'
                            )}
                            onClick={() => handleStarClick(starIndex)}
                            onMouseEnter={() => handleStarHover(starIndex)}
                        />
                    );
                })}
            </div>
            
            {showValue && (
                <span className="text-sm text-gray-600 ml-1">
                    {rating.toFixed(1)} / {maxRating}
                </span>
            )}
        </div>
    );
}

interface RatingBreakdownProps {
    ratingBreakdown: Record<number, number>;
    totalReviews: number;
    className?: string;
}

export function RatingBreakdown({ ratingBreakdown, totalReviews, className }: RatingBreakdownProps) {
    return (
        <div className={cn('space-y-2', className)}>
            {[5, 4, 3, 2, 1].map((rating) => {
                const count = ratingBreakdown[rating] || 0;
                const percentage = totalReviews > 0 ? (count / totalReviews) * 100 : 0;
                
                return (
                    <div key={rating} className="flex items-center gap-2 text-sm">
                        <div className="flex items-center gap-1 w-12">
                            <span>{rating}</span>
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        </div>
                        
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                                className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${percentage}%` }}
                            />
                        </div>
                        
                        <span className="text-gray-600 w-8 text-right">
                            {count}
                        </span>
                    </div>
                );
            })}
        </div>
    );
}

interface RatingSummaryProps {
    averageRating: number;
    totalReviews: number;
    ratingBreakdown: Record<number, number>;
    className?: string;
}

export function RatingSummary({ 
    averageRating, 
    totalReviews, 
    ratingBreakdown, 
    className 
}: RatingSummaryProps) {
    return (
        <div className={cn('bg-white rounded-lg border p-6', className)}>
            <div className="flex items-start gap-6">
                <div className="text-center">
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                        {averageRating.toFixed(1)}
                    </div>
                    <StarRating rating={averageRating} size="lg" className="mb-2" />
                    <div className="text-sm text-gray-600">
                        {totalReviews} {totalReviews === 1 ? 'review' : 'reviews'}
                    </div>
                </div>
                
                <div className="flex-1">
                    <RatingBreakdown 
                        ratingBreakdown={ratingBreakdown}
                        totalReviews={totalReviews}
                    />
                </div>
            </div>
        </div>
    );
}
