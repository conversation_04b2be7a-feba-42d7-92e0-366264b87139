import React from 'react';
import { ThumbsUp, ThumbsDown, Shield, Edit, Trash2 } from 'lucide-react';
import { StarRating } from './star-rating';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface Review {
    id: number;
    uuid: string;
    title: string;
    content: string;
    rating: number;
    is_verified_purchase: boolean;
    is_featured: boolean;
    created_at: string;
    user: {
        id: number;
        name: string;
    };
    helpful_votes_count?: number;
    not_helpful_votes_count?: number;
    user_helpful_vote?: {
        is_helpful: boolean;
    } | null;
}

interface ReviewCardProps {
    review: Review;
    currentUserId?: number;
    onHelpfulVote?: (reviewId: number, isHelpful: boolean) => void;
    onEdit?: (review: Review) => void;
    onDelete?: (reviewId: number) => void;
    showActions?: boolean;
    className?: string;
}

export function ReviewCard({
    review,
    currentUserId,
    onHelpfulVote,
    onEdit,
    onDelete,
    showActions = true,
    className
}: ReviewCardProps) {
    const isOwnReview = currentUserId === review.user.id;
    const userVote = review.user_helpful_vote;

    const handleHelpfulVote = (isHelpful: boolean) => {
        if (onHelpfulVote && !isOwnReview) {
            onHelpfulVote(review.id, isHelpful);
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className={cn(
            'bg-white rounded-lg border p-6 space-y-4',
            review.is_featured && 'ring-2 ring-blue-500 ring-opacity-50',
            className
        )}>
            {/* Header */}
            <div className="flex items-start justify-between">
                <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                        <StarRating rating={review.rating} size="sm" />
                        <span className="text-sm text-gray-600">
                            {formatDate(review.created_at)}
                        </span>
                        {review.is_verified_purchase && (
                            <Badge variant="secondary" className="text-xs">
                                <Shield className="h-3 w-3 mr-1" />
                                Verified Purchase
                            </Badge>
                        )}
                        {review.is_featured && (
                            <Badge variant="default" className="text-xs">
                                Featured
                            </Badge>
                        )}
                    </div>
                    
                    <h4 className="font-semibold text-gray-900 mb-1">
                        {review.title}
                    </h4>
                    
                    <p className="text-sm text-gray-600 mb-2">
                        by {review.user.name}
                    </p>
                </div>

                {/* Own review actions */}
                {isOwnReview && showActions && (
                    <div className="flex items-center gap-2">
                        {onEdit && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onEdit(review)}
                            >
                                <Edit className="h-4 w-4" />
                            </Button>
                        )}
                        {onDelete && (
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onDelete(review.id)}
                                className="text-red-600 hover:text-red-700"
                            >
                                <Trash2 className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                )}
            </div>

            {/* Content */}
            <div className="prose prose-sm max-w-none">
                <p className="text-gray-700 whitespace-pre-wrap">
                    {review.content}
                </p>
            </div>

            {/* Footer with helpful votes */}
            {showActions && !isOwnReview && (
                <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center gap-4">
                        <span className="text-sm text-gray-600">
                            Was this review helpful?
                        </span>
                        
                        <div className="flex items-center gap-2">
                            <Button
                                variant={userVote?.is_helpful ? "default" : "outline"}
                                size="sm"
                                onClick={() => handleHelpfulVote(true)}
                                className="flex items-center gap-1"
                            >
                                <ThumbsUp className="h-4 w-4" />
                                <span>{review.helpful_votes_count || 0}</span>
                            </Button>
                            
                            <Button
                                variant={userVote?.is_helpful === false ? "default" : "outline"}
                                size="sm"
                                onClick={() => handleHelpfulVote(false)}
                                className="flex items-center gap-1"
                            >
                                <ThumbsDown className="h-4 w-4" />
                                <span>{review.not_helpful_votes_count || 0}</span>
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

interface ReviewListProps {
    reviews: Review[];
    currentUserId?: number;
    onHelpfulVote?: (reviewId: number, isHelpful: boolean) => void;
    onEdit?: (review: Review) => void;
    onDelete?: (reviewId: number) => void;
    showActions?: boolean;
    className?: string;
}

export function ReviewList({
    reviews,
    currentUserId,
    onHelpfulVote,
    onEdit,
    onDelete,
    showActions = true,
    className
}: ReviewListProps) {
    if (reviews.length === 0) {
        return (
            <div className={cn('text-center py-8', className)}>
                <p className="text-gray-500">No reviews yet. Be the first to review this product!</p>
            </div>
        );
    }

    return (
        <div className={cn('space-y-6', className)}>
            {reviews.map((review) => (
                <ReviewCard
                    key={review.id}
                    review={review}
                    currentUserId={currentUserId}
                    onHelpfulVote={onHelpfulVote}
                    onEdit={onEdit}
                    onDelete={onDelete}
                    showActions={showActions}
                />
            ))}
        </div>
    );
}
