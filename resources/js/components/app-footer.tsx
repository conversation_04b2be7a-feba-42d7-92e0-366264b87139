import { Link } from '@inertiajs/react';
import { Heart, Github, Twitter, Mail, Shield, FileText, HelpCircle } from 'lucide-react';

export function AppFooter() {
    const currentYear = new Date().getFullYear();

    const footerLinks = {
        product: [
            { name: 'Browse Products', href: '/products' },
            { name: 'Categories', href: '/categories' },
            { name: 'New Releases', href: '/new' },
            { name: 'Popular Downloads', href: '/popular' },
        ],
        support: [
            { name: 'Help Center', href: '/help' },
            { name: 'Contact Support', href: '/support' },
            { name: 'Documentation', href: '/docs' },
            { name: 'API Reference', href: '/api-docs' },
        ],
        legal: [
            { name: 'Privacy Policy', href: '/privacy' },
            { name: 'Terms of Service', href: '/terms' },
            { name: 'License Agreement', href: '/license' },
            { name: 'Refund Policy', href: '/refunds' },
        ],
        company: [
            { name: 'About Us', href: '/about' },
            { name: 'Blog', href: '/blog' },
            { name: 'Careers', href: '/careers' },
            { name: 'Press Kit', href: '/press' },
        ],
    };

    const socialLinks = [
        { name: 'GitHub', href: '#', icon: Github },
        { name: 'Twitter', href: '#', icon: Twitter },
        { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },
    ];

    return (
        <footer className="bg-white dark:bg-slate-900 border-t border-slate-200 dark:border-slate-800">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Main Footer Content */}
                <div className="py-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
                    {/* Brand Section */}
                    <div className="lg:col-span-1">
                        <div className="flex items-center space-x-2 mb-4">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <span className="text-white font-bold text-sm">DD</span>
                            </div>
                            <span className="text-lg font-bold text-slate-900 dark:text-white">
                                Digital Downloads
                            </span>
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mb-4 leading-relaxed">
                            Your trusted marketplace for premium digital products, templates, and resources.
                        </p>
                        <div className="flex items-center space-x-1 text-xs text-slate-500 dark:text-slate-400">
                            <Shield className="h-3 w-3" />
                            <span>Secure & Trusted Platform</span>
                        </div>
                    </div>

                    {/* Product Links */}
                    <div>
                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-4">
                            Products
                        </h3>
                        <ul className="space-y-3">
                            {footerLinks.product.map((link) => (
                                <li key={link.name}>
                                    <Link
                                        href={link.href}
                                        className="text-sm text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors"
                                    >
                                        {link.name}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Support Links */}
                    <div>
                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-4">
                            Support
                        </h3>
                        <ul className="space-y-3">
                            {footerLinks.support.map((link) => (
                                <li key={link.name}>
                                    <Link
                                        href={link.href}
                                        className="text-sm text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors"
                                    >
                                        {link.name}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Legal Links */}
                    <div>
                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-4">
                            Legal
                        </h3>
                        <ul className="space-y-3">
                            {footerLinks.legal.map((link) => (
                                <li key={link.name}>
                                    <Link
                                        href={link.href}
                                        className="text-sm text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors"
                                    >
                                        {link.name}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Company Links */}
                    <div>
                        <h3 className="text-sm font-semibold text-slate-900 dark:text-white mb-4">
                            Company
                        </h3>
                        <ul className="space-y-3">
                            {footerLinks.company.map((link) => (
                                <li key={link.name}>
                                    <Link
                                        href={link.href}
                                        className="text-sm text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white transition-colors"
                                    >
                                        {link.name}
                                    </Link>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>

                {/* Bottom Footer */}
                <div className="py-6 border-t border-slate-200 dark:border-slate-800">
                    <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        {/* Copyright */}
                        <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400">
                            <span>© {currentYear} Digital Downloads Platform.</span>
                            <span className="hidden md:inline">All rights reserved.</span>
                            <div className="flex items-center space-x-1">
                                <span>Made with</span>
                                <Heart className="h-3 w-3 text-red-500" />
                                <span>for creators</span>
                            </div>
                        </div>

                        {/* Social Links */}
                        <div className="flex items-center space-x-4">
                            {socialLinks.map((social) => (
                                <a
                                    key={social.name}
                                    href={social.href}
                                    className="text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                                    aria-label={social.name}
                                >
                                    <social.icon className="h-5 w-5" />
                                </a>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Trust Indicators */}
                <div className="py-4 border-t border-slate-100 dark:border-slate-800">
                    <div className="flex flex-wrap justify-center items-center space-x-6 text-xs text-slate-500 dark:text-slate-400">
                        <div className="flex items-center space-x-1">
                            <Shield className="h-3 w-3" />
                            <span>SSL Secured</span>
                        </div>
                        <div className="flex items-center space-x-1">
                            <FileText className="h-3 w-3" />
                            <span>GDPR Compliant</span>
                        </div>
                        <div className="flex items-center space-x-1">
                            <HelpCircle className="h-3 w-3" />
                            <span>24/7 Support</span>
                        </div>
                        <span>•</span>
                        <span>99.9% Uptime</span>
                        <span>•</span>
                        <span>Instant Downloads</span>
                    </div>
                </div>
            </div>
        </footer>
    );
}
