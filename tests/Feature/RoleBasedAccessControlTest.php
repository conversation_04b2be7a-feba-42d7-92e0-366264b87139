<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RoleBasedAccessControlTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'super_admin', 'display_name' => 'Super Admin']);
        Role::create(['name' => 'admin', 'display_name' => 'Admin']);
        Role::create(['name' => 'user', 'display_name' => 'User']);
    }

    public function test_super_admin_redirects_to_super_admin_dashboard()
    {
        $superAdmin = User::factory()->create();
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->actingAs($superAdmin)->get('/dashboard');

        $response->assertRedirect('/super-admin');
    }

    public function test_admin_redirects_to_admin_dashboard()
    {
        $admin = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        $response = $this->actingAs($admin)->get('/dashboard');

        $response->assertRedirect('/admin');
    }

    public function test_regular_user_sees_user_dashboard()
    {
        $user = User::factory()->create();
        $userRole = Role::where('name', 'user')->first();
        $user->roles()->attach($userRole);

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => $page->component('dashboard'));
    }

    public function test_super_admin_can_access_super_admin_routes()
    {
        $superAdmin = User::factory()->create();
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->actingAs($superAdmin)->get('/super-admin/dashboard');

        $response->assertOk();
    }

    public function test_admin_can_access_admin_routes()
    {
        $admin = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertOk();
    }

    public function test_super_admin_can_access_admin_routes()
    {
        $superAdmin = User::factory()->create();
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->actingAs($superAdmin)->get('/admin/dashboard');

        $response->assertOk();
    }

    public function test_regular_user_cannot_access_admin_routes()
    {
        $user = User::factory()->create();
        $userRole = Role::where('name', 'user')->first();
        $user->roles()->attach($userRole);

        $response = $this->actingAs($user)->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_regular_user_cannot_access_super_admin_routes()
    {
        $user = User::factory()->create();
        $userRole = Role::where('name', 'user')->first();
        $user->roles()->attach($userRole);

        $response = $this->actingAs($user)->get('/super-admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_admin_cannot_access_super_admin_routes()
    {
        $admin = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        $response = $this->actingAs($admin)->get('/super-admin/dashboard');

        $response->assertStatus(403);
    }

    public function test_login_redirects_super_admin_to_super_admin_dashboard()
    {
        $superAdmin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/super-admin');
    }

    public function test_login_redirects_admin_to_admin_dashboard()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/admin');
    }

    public function test_login_redirects_user_to_user_dashboard()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $userRole = Role::where('name', 'user')->first();
        $user->roles()->attach($userRole);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $response->assertRedirect('/dashboard');
    }

    public function test_smart_layout_renders_correct_layout_for_super_admin()
    {
        $superAdmin = User::factory()->create();
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->actingAs($superAdmin)->get('/super-admin/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => $page->component('super-admin/dashboard'));
    }

    public function test_smart_layout_renders_correct_layout_for_admin()
    {
        $admin = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $admin->roles()->attach($adminRole);

        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => $page->component('admin/dashboard'));
    }

    public function test_user_roles_are_loaded_in_inertia_requests()
    {
        $superAdmin = User::factory()->create();
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->roles()->attach($superAdminRole);

        $response = $this->actingAs($superAdmin)->get('/super-admin/dashboard');

        $response->assertOk();
        $response->assertInertia(fn ($page) => 
            $page->has('auth.user.roles')
                 ->where('auth.user.roles.0.name', 'super_admin')
        );
    }

    public function test_guest_user_redirected_to_login()
    {
        $response = $this->get('/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_guest_user_cannot_access_admin_routes()
    {
        $response = $this->get('/admin/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_guest_user_cannot_access_super_admin_routes()
    {
        $response = $this->get('/super-admin/dashboard');

        $response->assertRedirect('/login');
    }
}
