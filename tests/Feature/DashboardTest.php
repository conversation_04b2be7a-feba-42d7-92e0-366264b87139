<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Role;
use App\Services\DashboardService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'super_admin', 'display_name' => 'Super Admin']);
        Role::create(['name' => 'admin', 'display_name' => 'Admin']);
        Role::create(['name' => 'user', 'display_name' => 'User']);
    }

    public function test_guests_are_redirected_to_the_login_page()
    {
        $this->get('/dashboard')->assertRedirect('/login');
    }

    public function test_authenticated_users_can_visit_the_dashboard()
    {
        $this->actingAs($user = User::factory()->create());

        $this->get('/dashboard')->assertOk();
    }

    public function test_dashboard_returns_correct_data_structure()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->has('dashboardData')
                ->has('dashboardData.stats')
                ->has('dashboardData.activities')
                ->has('dashboardData.transactions')
        );
    }

    public function test_admin_can_access_admin_dashboard()
    {
        $user = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $user->roles()->attach($adminRole);

        $response = $this->actingAs($user)->get('/admin');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('admin/dashboard'));
    }

    public function test_regular_user_cannot_access_admin_dashboard()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/admin');

        $response->assertStatus(403);
    }

    public function test_dashboard_api_returns_json_data()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'stats',
            'activities',
            'transactions'
        ]);
    }

    public function test_dashboard_service_returns_different_data_for_admin_and_user()
    {
        $service = new DashboardService();

        // Test regular user
        $user = User::factory()->create();
        $userData = $service->getDashboardData($user);

        $this->assertArrayHasKey('stats', $userData);
        $this->assertArrayHasKey('myProducts', $userData['stats']);

        // Test admin user
        $adminUser = User::factory()->create();
        $adminRole = Role::where('name', 'admin')->first();
        $adminUser->roles()->attach($adminRole);

        $adminData = $service->getDashboardData($adminUser);

        $this->assertArrayHasKey('stats', $adminData);
        $this->assertArrayHasKey('products', $adminData['stats']);
        $this->assertArrayHasKey('users', $adminData['stats']);
    }
}
