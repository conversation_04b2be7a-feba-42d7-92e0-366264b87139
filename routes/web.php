<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

// Shop Routes
Route::get('/shop', [App\Http\Controllers\ShopController::class, 'index'])->name('shop.index');
Route::get('/shop/category/{slug}', [App\Http\Controllers\ShopController::class, 'category'])->name('shop.category');
Route::get('/shop/product/{slug}', [App\Http\Controllers\ShopController::class, 'show'])->name('shop.product');
Route::get('/api/shop/featured', [App\Http\Controllers\ShopController::class, 'featured'])->name('shop.featured');
Route::get('/api/shop/new-arrivals', [App\Http\Controllers\ShopController::class, 'newArrivals'])->name('shop.new-arrivals');
Route::get('/api/shop/categories', [App\Http\Controllers\ShopController::class, 'categories'])->name('shop.categories');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('api/dashboard/data', [App\Http\Controllers\DashboardController::class, 'data'])->name('dashboard.data');
    Route::get('api/dashboard/activities', [App\Http\Controllers\DashboardController::class, 'activities'])->name('dashboard.activities');
    Route::get('api/dashboard/transactions', [App\Http\Controllers\DashboardController::class, 'transactions'])->name('dashboard.transactions');

    // Customer Account Routes
    Route::prefix('account')->name('customer.')->group(function () {
        Route::get('/', [App\Http\Controllers\CustomerController::class, 'dashboard'])->name('dashboard');
        Route::get('/orders', [App\Http\Controllers\CustomerController::class, 'orders'])->name('orders');
        Route::get('/orders/{orderNumber}', [App\Http\Controllers\CustomerController::class, 'order'])->name('order');
        Route::get('/downloads', [App\Http\Controllers\CustomerController::class, 'downloads'])->name('downloads');
        Route::get('/profile', [App\Http\Controllers\CustomerController::class, 'profile'])->name('profile');
        Route::put('/profile', [App\Http\Controllers\CustomerController::class, 'updateProfile'])->name('profile.update');
        Route::get('/support', [App\Http\Controllers\CustomerController::class, 'support'])->name('support');
        Route::post('/support', [App\Http\Controllers\CustomerController::class, 'submitSupportTicket'])->name('support.submit');

        // API routes for customer data
        Route::get('/api/statistics', [App\Http\Controllers\CustomerController::class, 'statistics'])->name('api.statistics');
        Route::get('/api/recent-activity', [App\Http\Controllers\CustomerController::class, 'recentActivity'])->name('api.recent-activity');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

Route::get('email-verified', function () {
    return Inertia::render('auth/email-verified');
})->middleware('auth')->name('verification.success');

// Secure download routes
Route::get('/download/{token}', [App\Http\Controllers\SecureDownloadController::class, 'download'])
    ->name('secure.download');

// Product Reviews Routes
Route::prefix('products/{product}')->group(function () {
    Route::get('/reviews', [App\Http\Controllers\ProductReviewController::class, 'index'])->name('products.reviews.index');
    Route::get('/reviews/stats', [App\Http\Controllers\ProductReviewController::class, 'stats'])->name('products.reviews.stats');

    // Related Products Routes
    Route::get('/related', [App\Http\Controllers\RelatedProductsController::class, 'getRelatedProducts'])->name('products.related');
    Route::get('/frequently-bought-together', [App\Http\Controllers\RelatedProductsController::class, 'getFrequentlyBoughtTogether'])->name('products.frequently-bought-together');

    Route::middleware('auth')->group(function () {
        Route::post('/reviews', [App\Http\Controllers\ProductReviewController::class, 'store'])->name('products.reviews.store');
        Route::put('/reviews/{review}', [App\Http\Controllers\ProductReviewController::class, 'update'])->name('products.reviews.update');
        Route::delete('/reviews/{review}', [App\Http\Controllers\ProductReviewController::class, 'destroy'])->name('products.reviews.destroy');
        Route::post('/reviews/{review}/vote', [App\Http\Controllers\ProductReviewController::class, 'voteHelpful'])->name('products.reviews.vote');
    });
});

// General Recommendation Routes
Route::get('/recommendations/personalized', [App\Http\Controllers\RelatedProductsController::class, 'getPersonalizedRecommendations'])->name('recommendations.personalized');
Route::get('/recommendations/trending', [App\Http\Controllers\RelatedProductsController::class, 'getTrendingProducts'])->name('recommendations.trending');
Route::get('/categories/{category}/products', [App\Http\Controllers\RelatedProductsController::class, 'getProductsByCategory'])->name('categories.products');

// Wishlist Routes
Route::middleware('auth')->group(function () {
    Route::resource('wishlists', App\Http\Controllers\WishlistController::class);
    Route::post('/wishlists/{wishlist}/add-product', [App\Http\Controllers\WishlistController::class, 'addProduct'])->name('wishlists.add-product');
    Route::post('/wishlists/{wishlist}/remove-product', [App\Http\Controllers\WishlistController::class, 'removeProduct'])->name('wishlists.remove-product');
    Route::post('/wishlists/{wishlist}/move-to-cart', [App\Http\Controllers\WishlistController::class, 'moveToCart'])->name('wishlists.move-to-cart');
    Route::post('/wishlist/quick-add', [App\Http\Controllers\WishlistController::class, 'quickAdd'])->name('wishlists.quick-add');
    Route::post('/wishlist/quick-remove', [App\Http\Controllers\WishlistController::class, 'quickRemove'])->name('wishlists.quick-remove');
    Route::post('/wishlist/check-product', [App\Http\Controllers\WishlistController::class, 'checkProduct'])->name('wishlists.check-product');
});

// Public Wishlist Routes
Route::get('/wishlist/{uuid}', [App\Http\Controllers\WishlistController::class, 'viewPublic'])->name('wishlists.public');

// Product Comparison Routes
Route::resource('comparisons', App\Http\Controllers\ProductComparisonController::class);
Route::post('/comparisons/{comparison}/add-product', [App\Http\Controllers\ProductComparisonController::class, 'addProduct'])->name('comparisons.add-product');
Route::post('/comparisons/{comparison}/remove-product', [App\Http\Controllers\ProductComparisonController::class, 'removeProduct'])->name('comparisons.remove-product');
Route::post('/compare/quick', [App\Http\Controllers\ProductComparisonController::class, 'quickCompare'])->name('comparisons.quick');
Route::get('/compare/current', [App\Http\Controllers\ProductComparisonController::class, 'getCurrent'])->name('comparisons.current');

// Public Comparison Routes
Route::get('/compare/{uuid}', [App\Http\Controllers\ProductComparisonController::class, 'viewPublic'])->name('comparisons.public');

// Product Bundle Routes
Route::resource('bundles', App\Http\Controllers\ProductBundleController::class)->only(['index', 'show']);
Route::get('/bundles/{bundle}/data', [App\Http\Controllers\ProductBundleController::class, 'getBundleData'])->name('bundles.data');
Route::get('/api/bundles/featured', [App\Http\Controllers\ProductBundleController::class, 'getFeatured'])->name('bundles.featured');
Route::get('/api/bundles/for-product/{product}', [App\Http\Controllers\ProductBundleController::class, 'getBundlesForProduct'])->name('bundles.for-product');
Route::post('/api/bundles/calculate-price', [App\Http\Controllers\ProductBundleController::class, 'calculateBundlePrice'])->name('bundles.calculate-price');
Route::get('/api/bundles/recommendations', [App\Http\Controllers\ProductBundleController::class, 'getRecommendations'])->name('bundles.recommendations');

// Search Routes
Route::get('/search', [App\Http\Controllers\SearchController::class, 'index'])->name('search.index');
Route::get('/api/search', [App\Http\Controllers\SearchController::class, 'search'])->name('search.api');
Route::get('/api/search/suggestions', [App\Http\Controllers\SearchController::class, 'suggestions'])->name('search.suggestions');
Route::get('/api/search/quick', [App\Http\Controllers\SearchController::class, 'quick'])->name('search.quick');
Route::get('/api/search/facets', [App\Http\Controllers\SearchController::class, 'facets'])->name('search.facets');
Route::get('/api/search/popular', [App\Http\Controllers\SearchController::class, 'popular'])->name('search.popular');
Route::post('/api/search/clear-filters', [App\Http\Controllers\SearchController::class, 'clearFilters'])->name('search.clear-filters');
Route::post('/api/search/record-click', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'recordClick'])->name('search.record-click');

// Popular Products Routes
Route::get('/popular', [App\Http\Controllers\PopularProductsController::class, 'index'])->name('popular.index');
Route::get('/api/popular/products', [App\Http\Controllers\PopularProductsController::class, 'getPopular'])->name('popular.products');
Route::get('/api/trending/products', [App\Http\Controllers\PopularProductsController::class, 'getTrending'])->name('trending.products');
Route::get('/api/bestsellers/products', [App\Http\Controllers\PopularProductsController::class, 'getBestSellers'])->name('bestsellers.products');
Route::get('/api/most-viewed/products', [App\Http\Controllers\PopularProductsController::class, 'getMostViewed'])->name('most-viewed.products');
Route::get('/api/recently-viewed/products', [App\Http\Controllers\PopularProductsController::class, 'getRecentlyViewed'])->name('recently-viewed.products');
Route::post('/api/products/record-view', [App\Http\Controllers\PopularProductsController::class, 'recordView'])->name('products.record-view');
Route::post('/api/products/update-view', [App\Http\Controllers\PopularProductsController::class, 'updateView'])->name('products.update-view');
Route::get('/api/products/{product}/stats', [App\Http\Controllers\PopularProductsController::class, 'getProductStats'])->name('products.stats');

// Admin Analytics Routes (require admin authentication)
Route::middleware(['auth', 'admin'])->prefix('admin/api')->group(function () {
    // Sales Analytics
    Route::get('/analytics/sales', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'index'])->name('admin.analytics.sales');
    Route::get('/analytics/sales/overview', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'overview'])->name('admin.analytics.sales.overview');
    Route::get('/analytics/sales/trends', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'dailyTrends'])->name('admin.analytics.sales.trends');
    Route::get('/analytics/sales/hourly', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'hourlyPattern'])->name('admin.analytics.sales.hourly');
    Route::get('/analytics/sales/top-products', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'topProducts'])->name('admin.analytics.sales.top-products');
    Route::get('/analytics/sales/categories', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'revenueByCategory'])->name('admin.analytics.sales.categories');
    Route::get('/analytics/sales/customers', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'customerAnalytics'])->name('admin.analytics.sales.customers');
    Route::get('/analytics/sales/export', [App\Http\Controllers\Admin\SalesAnalyticsController::class, 'export'])->name('admin.analytics.sales.export');

    // Product Performance Analytics
    Route::get('/analytics/products', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'index'])->name('admin.analytics.products');
    Route::get('/analytics/products/{product}', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'show'])->name('admin.analytics.products.show');
    Route::get('/analytics/products/top/performing', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'topProducts'])->name('admin.analytics.products.top');
    Route::post('/analytics/products/compare', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'compare'])->name('admin.analytics.products.compare');
    Route::get('/analytics/products/{product}/trends', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'trends'])->name('admin.analytics.products.trends');
    Route::get('/analytics/products/{product}/funnel', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'conversionFunnel'])->name('admin.analytics.products.funnel');
    Route::get('/analytics/products/category/performance', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'byCategory'])->name('admin.analytics.products.category');
    Route::get('/analytics/products/underperforming', [App\Http\Controllers\Admin\ProductPerformanceController::class, 'underperforming'])->name('admin.analytics.products.underperforming');

    // Search Analytics
    Route::get('/analytics/search', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'index'])->name('admin.analytics.search');
    Route::get('/analytics/search/metrics', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'metrics'])->name('admin.analytics.search.metrics');
    Route::get('/analytics/search/popular', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'popularQueries'])->name('admin.analytics.search.popular');
    Route::get('/analytics/search/no-results', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'noResultQueries'])->name('admin.analytics.search.no-results');
    Route::get('/analytics/search/trends', [App\Http\Controllers\Admin\SearchAnalyticsController::class, 'trends'])->name('admin.analytics.search.trends');

    // Download Analytics
    Route::get('/analytics/downloads', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'index'])->name('admin.analytics.downloads');
    Route::get('/analytics/downloads/overview', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'overview'])->name('admin.analytics.downloads.overview');
    Route::get('/analytics/downloads/trends', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'dailyTrends'])->name('admin.analytics.downloads.trends');
    Route::get('/analytics/downloads/hourly', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'hourlyPattern'])->name('admin.analytics.downloads.hourly');
    Route::get('/analytics/downloads/most-downloaded', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'mostDownloaded'])->name('admin.analytics.downloads.most-downloaded');
    Route::get('/analytics/downloads/file-types', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'byFileType'])->name('admin.analytics.downloads.file-types');
    Route::get('/analytics/downloads/top-users', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'topUsers'])->name('admin.analytics.downloads.top-users');
    Route::get('/analytics/downloads/failures', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'failureAnalysis'])->name('admin.analytics.downloads.failures');
    Route::get('/analytics/downloads/bandwidth', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'bandwidthUsage'])->name('admin.analytics.downloads.bandwidth');
    Route::get('/analytics/downloads/export', [App\Http\Controllers\Admin\DownloadAnalyticsController::class, 'export'])->name('admin.analytics.downloads.export');
});

require __DIR__.'/super-admin.php';
require __DIR__.'/admin.php';

// Two-Factor Authentication Routes
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('two-factor', [App\Http\Controllers\TwoFactorController::class, 'show'])->name('two-factor.show');
    Route::post('two-factor/enable', [App\Http\Controllers\TwoFactorController::class, 'enable'])->name('two-factor.enable');
    Route::delete('two-factor/disable', [App\Http\Controllers\TwoFactorController::class, 'disable'])->name('two-factor.disable');
    Route::post('two-factor/recovery-codes', [App\Http\Controllers\TwoFactorController::class, 'generateRecoveryCodes'])->name('two-factor.recovery-codes');
    Route::post('two-factor/generate-secret', [App\Http\Controllers\TwoFactorController::class, 'generateSecret'])->name('two-factor.generate-secret');
});

Route::middleware('guest')->group(function () {
    Route::get('two-factor/challenge', [App\Http\Controllers\TwoFactorController::class, 'challenge'])->name('two-factor.challenge');
    Route::post('two-factor/verify', [App\Http\Controllers\TwoFactorController::class, 'verify'])->name('two-factor.verify');
});
