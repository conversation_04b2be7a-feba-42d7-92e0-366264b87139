# S3 Integration API Reference
## Complete API Documentation for S3 File Management

---

## Overview

This document provides comprehensive API reference for the S3 integration components, including classes, methods, and configuration options.

## Core Classes

### S3FileManager Class

**File**: `includes/libs/S3FileManager.class.php`

#### Constructor

```php
public function __construct(array $config)
```

**Parameters**:
- `$config` (array): S3 configuration array

**Example**:
```php
$s3Manager = new S3FileManager([
    'region' => 'us-east-1',
    'bucket' => 'my-bucket',
    'access_key' => 'AKIAIOSFODNN7EXAMPLE',
    'secret_key' => 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY'
]);
```

#### Methods

##### uploadFile()

```php
public function uploadFile(string $localPath, string $s3Key, array $metadata = []): array
```

Uploads a local file to S3 with optional metadata.

**Parameters**:
- `$localPath` (string): Path to local file
- `$s3Key` (string): S3 object key
- `$metadata` (array): Optional metadata

**Returns**:
```php
[
    'success' => bool,
    'url' => string|null,
    'etag' => string|null,
    'error' => string|null
]
```

**Example**:
```php
$result = $s3Manager->uploadFile(
    '/path/to/local/file.zip',
    'files/2024/01/123/software.zip',
    ['file-id' => '123', 'category' => 'software']
);

if ($result['success']) {
    echo "File uploaded: " . $result['url'];
} else {
    echo "Upload failed: " . $result['error'];
}
```

##### generatePresignedUrl()

```php
public function generatePresignedUrl(string $s3Key, string $expiresIn = '+24 hours'): string|false
```

Generates a presigned URL for secure file downloads.

**Parameters**:
- `$s3Key` (string): S3 object key
- `$expiresIn` (string): Expiration time (PHP strtotime format)

**Returns**: Presigned URL string or false on failure

**Example**:
```php
$url = $s3Manager->generatePresignedUrl(
    'files/2024/01/123/software.zip',
    '+2 hours'
);

if ($url) {
    echo "Download URL: " . $url;
}
```

##### generateSecurePresignedUrl()

```php
public function generateSecurePresignedUrl(string $s3Key, string $expiresIn, string $ipAddress, int $userId = null): string|false
```

Generates a presigned URL with IP and user binding.

**Parameters**:
- `$s3Key` (string): S3 object key
- `$expiresIn` (string): Expiration time
- `$ipAddress` (string): Client IP address
- `$userId` (int|null): User ID for binding

**Returns**: Presigned URL string or false on failure

**Example**:
```php
$secureUrl = $s3Manager->generateSecurePresignedUrl(
    'files/2024/01/123/software.zip',
    '+24 hours',
    '*************',
    12345
);
```

##### fileExists()

```php
public function fileExists(string $s3Key): bool
```

Checks if a file exists in S3.

**Parameters**:
- `$s3Key` (string): S3 object key

**Returns**: True if file exists, false otherwise

##### deleteFile()

```php
public function deleteFile(string $s3Key): bool
```

Deletes a file from S3.

**Parameters**:
- `$s3Key` (string): S3 object key

**Returns**: True on success, false on failure

##### getFileMetadata()

```php
public function getFileMetadata(string $s3Key): array|false
```

Retrieves file metadata from S3.

**Parameters**:
- `$s3Key` (string): S3 object key

**Returns**: Metadata array or false on failure

**Example**:
```php
$metadata = $s3Manager->getFileMetadata('files/2024/01/123/software.zip');
if ($metadata) {
    echo "File size: " . $metadata['ContentLength'];
    echo "Last modified: " . $metadata['LastModified'];
}
```

---

### SecureDownloadValidator Class

**File**: `includes/libs/SecureDownloadValidator.class.php`

#### Methods

##### validateDownloadRequest()

```php
public static function validateDownloadRequest(int $fileId, int $userId = null, string $ipAddress): array
```

Performs comprehensive download validation.

**Parameters**:
- `$fileId` (int): File ID to validate
- `$userId` (int|null): User ID (null for guests)
- `$ipAddress` (string): Client IP address

**Returns**:
```php
[
    'valid' => bool,
    'reason' => string|null,
    'details' => array
]
```

**Example**:
```php
$validation = SecureDownloadValidator::validateDownloadRequest(
    123,
    $userId,
    '*************'
);

if (!$validation['valid']) {
    echo "Access denied: " . $validation['reason'];
}
```

##### verifyUserAccess()

```php
public static function verifyUserAccess(int $fileId, int $userId = null): bool
```

Verifies user has access to the file.

##### validateIPAddress()

```php
public static function validateIPAddress(string $ipAddress): bool
```

Validates IP address against blacklists and restrictions.

##### checkRateLimit()

```php
public static function checkRateLimit(int $userId = null, string $ipAddress): bool
```

Checks if user/IP has exceeded rate limits.

---

### IPAddressValidator Class

**File**: `includes/libs/IPAddressValidator.class.php`

#### Methods

##### detectClientIP()

```php
public static function detectClientIP(): string
```

Detects the real client IP address, handling proxies and load balancers.

**Returns**: Client IP address

**Example**:
```php
$clientIP = IPAddressValidator::detectClientIP();
echo "Client IP: " . $clientIP;
```

##### isValidIP()

```php
public static function isValidIP(string $ip): bool
```

Validates IP address format and excludes private/reserved ranges.

##### validateIPBinding()

```php
public static function validateIPBinding(string $downloadHash, string $currentIP): bool
```

Validates IP binding for a download request.

##### blockIPAddress()

```php
public static function blockIPAddress(string $ip, string $reason): void
```

Blocks an IP address with specified reason.

---

### S3DownloadToken Class

**File**: `includes/libs/S3DownloadToken.class.php`

#### Methods

##### generateSecureToken()

```php
public static function generateSecureToken(int $fileId, int $userId = null, string $ipAddress, int $expiresIn): string
```

Generates cryptographically secure download token.

**Parameters**:
- `$fileId` (int): File ID
- `$userId` (int|null): User ID
- `$ipAddress` (string): Client IP
- `$expiresIn` (int): Expiration time in seconds

**Returns**: Base64 encoded secure token

##### validateToken()

```php
public static function validateToken(string $token, string $currentIp): array|false
```

Validates and decodes a secure token.

**Returns**: Token payload array or false if invalid

---

## Configuration Reference

### S3 Configuration Array

```php
$CONFIG['s3'] = [
    // Required settings
    'enabled' => true,
    'region' => 'us-east-1',
    'bucket' => 'your-bucket-name',
    'access_key' => 'YOUR_ACCESS_KEY',
    'secret_key' => 'YOUR_SECRET_KEY',
    
    // Security settings
    'presigned_url_expiry' => 86400, // 24 hours in seconds
    'ip_restriction_enabled' => true,
    'user_binding_enabled' => true,
    
    // Performance settings
    'cdn_domain' => 'https://cdn.yourdomain.com',
    'multipart_threshold' => 104857600, // 100MB
    'multipart_chunksize' => 8388608,   // 8MB
    
    // Storage settings
    'storage_class' => 'STANDARD',
    'server_side_encryption' => 'AES256',
    
    // Debug settings
    'debug' => false,
    'log_level' => 'error'
];
```

### Security Configuration

```php
$CONFIG['security'] = [
    // IP validation
    'ip_binding_enabled' => true,
    'ip_whitelist' => [],
    'ip_blacklist' => [],
    'proxy_detection_enabled' => true,
    
    // Rate limiting
    'rate_limiting_enabled' => true,
    'max_downloads_per_hour' => [
        'guest' => 5,
        'user' => 50,
        'premium' => 200
    ],
    'max_bandwidth_per_hour' => [
        'guest' => 1073741824,    // 1GB
        'user' => 10737418240,   // 10GB
        'premium' => 53687091200 // 50GB
    ],
    
    // Device fingerprinting
    'device_fingerprinting_enabled' => true,
    'max_devices_per_user' => [
        'basic' => 2,
        'premium' => 5,
        'enterprise' => 10
    ],
    
    // Token security
    'token_algorithm' => 'sha256',
    'token_expiry' => 86400,
    
    // Monitoring
    'security_monitoring_enabled' => true,
    'alert_threshold' => 'high',
    'auto_block_enabled' => true
];
```

---

## Database Schema Reference

### s3_downloads Table

```sql
CREATE TABLE `res_s3_downloads` (
    `s3_download_id` int(11) NOT NULL AUTO_INCREMENT,
    `file_id` int(11) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) NOT NULL,
    `s3_key` varchar(500) NOT NULL,
    `presigned_url` text NOT NULL,
    `url_hash` varchar(64) NOT NULL,
    `expires_at` datetime NOT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `download_count` int(11) DEFAULT 0,
    `last_accessed` datetime DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`s3_download_id`),
    KEY `idx_file_user` (`file_id`, `user_id`),
    KEY `idx_ip_expires` (`ip_address`, `expires_at`),
    KEY `idx_url_hash` (`url_hash`)
);
```

### Enhanced files Table

```sql
ALTER TABLE `res_files` 
ADD COLUMN `s3_key` varchar(500) DEFAULT NULL,
ADD COLUMN `s3_bucket` varchar(100) DEFAULT NULL,
ADD COLUMN `storage_type` enum('local','s3','hybrid') DEFAULT 'local',
ADD COLUMN `s3_metadata` json DEFAULT NULL,
ADD COLUMN `migration_status` enum('pending','in_progress','completed','failed') DEFAULT 'pending',
ADD COLUMN `migrated_at` datetime DEFAULT NULL;
```

### security_events Table

```sql
CREATE TABLE `res_security_events` (
    `event_id` int(11) NOT NULL AUTO_INCREMENT,
    `event_type` varchar(100) NOT NULL,
    `severity` enum('low','medium','high','critical') DEFAULT 'medium',
    `details` json DEFAULT NULL,
    `ip_address` varchar(45) NOT NULL,
    `user_agent` text,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`event_id`),
    KEY `idx_type_severity` (`event_type`, `severity`),
    KEY `idx_ip_time` (`ip_address`, `created_at`)
);
```

---

## Error Codes and Messages

### S3 Error Codes

| Code | Message | Description |
|------|---------|-------------|
| `S3_UPLOAD_FAILED` | File upload to S3 failed | General upload error |
| `S3_INVALID_CREDENTIALS` | Invalid AWS credentials | Authentication failed |
| `S3_BUCKET_NOT_FOUND` | S3 bucket not found | Bucket doesn't exist |
| `S3_ACCESS_DENIED` | Access denied to S3 resource | Permission error |
| `S3_FILE_NOT_FOUND` | File not found in S3 | Object doesn't exist |
| `S3_PRESIGNED_URL_FAILED` | Failed to generate presigned URL | URL generation error |

### Security Error Codes

| Code | Message | Description |
|------|---------|-------------|
| `SEC_IP_MISMATCH` | IP address mismatch | IP binding violation |
| `SEC_TOKEN_INVALID` | Invalid security token | Token validation failed |
| `SEC_TOKEN_EXPIRED` | Security token expired | Token past expiration |
| `SEC_RATE_LIMIT_EXCEEDED` | Rate limit exceeded | Too many requests |
| `SEC_USER_ACCESS_DENIED` | User access denied | User lacks permission |
| `SEC_DEVICE_LIMIT_EXCEEDED` | Device limit exceeded | Too many devices |
| `SEC_IP_BLOCKED` | IP address blocked | IP in blacklist |

---

## Usage Examples

### Basic File Upload and Download

```php
// Initialize S3 manager
$s3Manager = new S3FileManager($_OPTIONS['s3']);

// Upload file
$uploadResult = $s3Manager->uploadFile(
    '/tmp/uploaded_file.zip',
    'files/2024/01/123/software.zip',
    ['file-id' => '123', 'category' => 'software']
);

if ($uploadResult['success']) {
    // Update database
    $db->where('file_id', 123)->update('files', [
        's3_key' => 'files/2024/01/123/software.zip',
        's3_bucket' => $_OPTIONS['s3']['bucket'],
        'storage_type' => 's3',
        's3_metadata' => json_encode($uploadResult['metadata'])
    ]);
    
    // Generate secure download URL
    $downloadUrl = $s3Manager->generateSecurePresignedUrl(
        'files/2024/01/123/software.zip',
        '+24 hours',
        $userIP,
        $userId
    );
    
    echo "Download URL: " . $downloadUrl;
}
```

### Security Validation Workflow

```php
// Validate download request
$validation = SecureDownloadValidator::validateDownloadRequest(
    $fileId,
    $userId,
    $clientIP
);

if (!$validation['valid']) {
    // Handle security violation
    SecurityMonitor::logSecurityEvent(
        'download_access_denied',
        [
            'file_id' => $fileId,
            'user_id' => $userId,
            'ip_address' => $clientIP,
            'reason' => $validation['reason']
        ],
        'medium'
    );
    
    // Return appropriate error
    switch ($validation['reason']) {
        case 'rate_limit_exceeded':
            http_response_code(429);
            echo "Too many requests";
            break;
        case 'ip_blocked':
            http_response_code(403);
            echo "Access denied";
            break;
        default:
            http_response_code(403);
            echo "Access denied";
    }
    exit;
}

// Proceed with download
```

### Batch File Migration

```php
// Migrate files in batches
$migrator = new S3MigrationTool(50); // 50 files per batch

$result = $migrator->startMigration();

echo "Migration completed:\n";
echo "Total: " . $result['total'] . "\n";
echo "Successful: " . $result['successful'] . "\n";
echo "Failed: " . $result['failed'] . "\n";

// Validate migration
$validator = new MigrationValidator();
$validation = $validator->validateMigration();

if ($validation['validation_errors']) {
    echo "Validation errors found for files: " . 
         implode(', ', $validation['validation_errors']);
}
```

This API reference provides comprehensive documentation for implementing and using the S3 integration features in your Digital Download Center.
