# S3 Migration Guide
## Step-by-Step Migration from Local Storage to Amazon S3

---

## Overview

This guide provides detailed instructions for migrating your existing Digital Download Center from local file storage to Amazon S3, ensuring zero downtime and data integrity throughout the process.

## Pre-Migration Checklist

### System Requirements

- [ ] PHP 7.4+ with cURL extension
- [ ] Composer for dependency management
- [ ] MySQL 5.7+ or MariaDB 10.2+
- [ ] Sufficient disk space for temporary files
- [ ] AWS account with S3 access
- [ ] Backup of current system and database

### AWS Prerequisites

- [ ] S3 bucket created
- [ ] IAM user with appropriate permissions
- [ ] CloudFront distribution (optional)
- [ ] SSL certificate configured

## Migration Phases

### Phase 1: Infrastructure Setup

#### 1.1 Install AWS SDK

```bash
cd /path/to/your/application
composer require aws/aws-sdk-php
```

#### 1.2 Create S3 Bucket

```bash
# Using AWS CLI
aws s3 mb s3://your-download-center-bucket --region us-east-1

# Set bucket versioning (recommended)
aws s3api put-bucket-versioning \
    --bucket your-download-center-bucket \
    --versioning-configuration Status=Enabled
```

#### 1.3 Configure Bucket Policy

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "AllowApplicationAccess",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/download-center-user"
            },
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject"
            ],
            "Resource": "arn:aws:s3:::your-download-center-bucket/*"
        },
        {
            "Sid": "AllowListBucket",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::YOUR_ACCOUNT_ID:user/download-center-user"
            },
            "Action": "s3:ListBucket",
            "Resource": "arn:aws:s3:::your-download-center-bucket"
        }
    ]
}
```

#### 1.4 Database Schema Updates

```sql
-- Add S3 columns to files table
ALTER TABLE `res_files` 
ADD COLUMN `s3_key` varchar(500) DEFAULT NULL,
ADD COLUMN `s3_bucket` varchar(100) DEFAULT NULL,
ADD COLUMN `storage_type` enum('local','s3','hybrid') DEFAULT 'local',
ADD COLUMN `s3_metadata` json DEFAULT NULL,
ADD COLUMN `migration_status` enum('pending','in_progress','completed','failed') DEFAULT 'pending',
ADD COLUMN `migrated_at` datetime DEFAULT NULL;

-- Create S3 downloads tracking table
CREATE TABLE `res_s3_downloads` (
    `s3_download_id` int(11) NOT NULL AUTO_INCREMENT,
    `file_id` int(11) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `ip_address` varchar(45) NOT NULL,
    `s3_key` varchar(500) NOT NULL,
    `presigned_url` text NOT NULL,
    `url_hash` varchar(64) NOT NULL,
    `expires_at` datetime NOT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `download_count` int(11) DEFAULT 0,
    `last_accessed` datetime DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`s3_download_id`),
    KEY `idx_file_user` (`file_id`, `user_id`),
    KEY `idx_ip_expires` (`ip_address`, `expires_at`),
    KEY `idx_url_hash` (`url_hash`)
);

-- Create migration log table
CREATE TABLE `res_migration_log` (
    `log_id` int(11) NOT NULL AUTO_INCREMENT,
    `file_id` int(11) NOT NULL,
    `action` varchar(50) NOT NULL,
    `status` enum('success','error','warning') NOT NULL,
    `message` text,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`log_id`),
    KEY `idx_file_status` (`file_id`, `status`)
);
```

### Phase 2: Core Implementation

#### 2.1 S3 File Manager Class

Create `includes/libs/S3FileManager.class.php`:

```php
<?php
require_once 'vendor/autoload.php';
use Aws\S3\S3Client;
use Aws\Exception\AwsException;

class S3FileManager {
    private $s3Client;
    private $bucket;
    private $region;
    
    public function __construct($config) {
        $this->s3Client = new S3Client([
            'version' => 'latest',
            'region' => $config['region'],
            'credentials' => [
                'key' => $config['access_key'],
                'secret' => $config['secret_key']
            ]
        ]);
        $this->bucket = $config['bucket'];
        $this->region = $config['region'];
    }
    
    public function uploadFile($localPath, $s3Key, $metadata = []) {
        try {
            $result = $this->s3Client->putObject([
                'Bucket' => $this->bucket,
                'Key' => $s3Key,
                'SourceFile' => $localPath,
                'Metadata' => $metadata,
                'ServerSideEncryption' => 'AES256',
                'StorageClass' => 'STANDARD'
            ]);
            
            return [
                'success' => true,
                'url' => $result['ObjectURL'],
                'etag' => $result['ETag']
            ];
        } catch (AwsException $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'code' => $e->getAwsErrorCode()
            ];
        }
    }
    
    public function generatePresignedUrl($s3Key, $expiresIn = '+24 hours') {
        try {
            $command = $this->s3Client->getCommand('GetObject', [
                'Bucket' => $this->bucket,
                'Key' => $s3Key,
                'ResponseContentDisposition' => 'attachment; filename="' . basename($s3Key) . '"'
            ]);
            
            $request = $this->s3Client->createPresignedRequest($command, $expiresIn);
            return (string) $request->getUri();
        } catch (AwsException $e) {
            error_log("S3 Presigned URL Error: " . $e->getMessage());
            return false;
        }
    }
    
    public function fileExists($s3Key) {
        try {
            $this->s3Client->headObject([
                'Bucket' => $this->bucket,
                'Key' => $s3Key
            ]);
            return true;
        } catch (AwsException $e) {
            return false;
        }
    }
    
    public function deleteFile($s3Key) {
        try {
            $this->s3Client->deleteObject([
                'Bucket' => $this->bucket,
                'Key' => $s3Key
            ]);
            return true;
        } catch (AwsException $e) {
            error_log("S3 Delete Error: " . $e->getMessage());
            return false;
        }
    }
}
```

#### 2.2 Migration Script

Create `admin/tools/migrate-to-s3.php`:

```php
<?php
require_once dirname(__FILE__) . '/../../includes/config.php';
require_once dirname(__FILE__) . '/../../includes/settings.php';
require_once dirname(__FILE__) . '/../../includes/functions.php';

class S3MigrationTool {
    private $db;
    private $s3Manager;
    private $batchSize;
    private $logFile;
    
    public function __construct($batchSize = 10) {
        global $db, $_OPTIONS;
        
        $this->db = $db;
        $this->s3Manager = new S3FileManager($_OPTIONS['s3']);
        $this->batchSize = $batchSize;
        $this->logFile = dirname(__FILE__) . '/migration.log';
    }
    
    public function startMigration() {
        $this->log("Starting S3 migration process");
        
        // Get total files to migrate
        $totalFiles = $this->db->where('storage_type', 'local')
                              ->where('migration_status', 'pending')
                              ->getValue('files', 'count(*)');
        
        $this->log("Total files to migrate: " . $totalFiles);
        
        $processed = 0;
        $successful = 0;
        $failed = 0;
        
        while ($processed < $totalFiles) {
            $files = $this->getNextBatch();
            
            if (empty($files)) {
                break;
            }
            
            foreach ($files as $file) {
                $result = $this->migrateFile($file);
                
                if ($result['success']) {
                    $successful++;
                    $this->log("Successfully migrated file ID: " . $file['file_id']);
                } else {
                    $failed++;
                    $this->log("Failed to migrate file ID: " . $file['file_id'] . " - " . $result['error']);
                }
                
                $processed++;
                
                // Progress update
                if ($processed % 10 == 0) {
                    $percentage = round(($processed / $totalFiles) * 100, 2);
                    $this->log("Progress: {$processed}/{$totalFiles} ({$percentage}%)");
                }
            }
            
            // Small delay to prevent overwhelming the system
            usleep(100000); // 0.1 seconds
        }
        
        $this->log("Migration completed. Successful: {$successful}, Failed: {$failed}");
        
        return [
            'total' => $totalFiles,
            'successful' => $successful,
            'failed' => $failed
        ];
    }
    
    private function getNextBatch() {
        return $this->db->where('storage_type', 'local')
                       ->where('migration_status', 'pending')
                       ->limit($this->batchSize)
                       ->get('files');
    }
    
    private function migrateFile($file) {
        // Mark as in progress
        $this->updateMigrationStatus($file['file_id'], 'in_progress');
        
        $localPath = $this->getLocalFilePath($file);
        
        if (!file_exists($localPath)) {
            $this->updateMigrationStatus($file['file_id'], 'failed', 'Local file not found');
            return ['success' => false, 'error' => 'Local file not found'];
        }
        
        // Generate S3 key
        $s3Key = $this->generateS3Key($file);
        
        // Prepare metadata
        $metadata = [
            'file-id' => (string)$file['file_id'],
            'original-name' => basename($file['url']),
            'upload-date' => date('Y-m-d H:i:s'),
            'file-size' => (string)filesize($localPath),
            'content-type' => mime_content_type($localPath)
        ];
        
        // Upload to S3
        $uploadResult = $this->s3Manager->uploadFile($localPath, $s3Key, $metadata);
        
        if ($uploadResult['success']) {
            // Update database
            $updateData = [
                's3_key' => $s3Key,
                's3_bucket' => $_OPTIONS['s3']['bucket'],
                'storage_type' => 'hybrid', // Keep local copy initially
                's3_metadata' => json_encode($metadata),
                'migration_status' => 'completed',
                'migrated_at' => date('Y-m-d H:i:s')
            ];
            
            $this->db->where('file_id', $file['file_id'])->update('files', $updateData);
            
            // Log success
            $this->logMigration($file['file_id'], 'upload', 'success', 'File uploaded to S3');
            
            return ['success' => true];
        } else {
            $this->updateMigrationStatus($file['file_id'], 'failed', $uploadResult['error']);
            $this->logMigration($file['file_id'], 'upload', 'error', $uploadResult['error']);
            
            return ['success' => false, 'error' => $uploadResult['error']];
        }
    }
    
    private function generateS3Key($file) {
        $extension = pathinfo($file['url'], PATHINFO_EXTENSION);
        $filename = pathinfo($file['url'], PATHINFO_FILENAME);
        
        // Create organized structure: files/year/month/file_id/filename.ext
        $year = date('Y', strtotime($file['date_create']));
        $month = date('m', strtotime($file['date_create']));
        
        return "files/{$year}/{$month}/{$file['file_id']}/{$filename}.{$extension}";
    }
    
    private function getLocalFilePath($file) {
        global $__DIR;
        return $__DIR . '/' . trim($file['url'], '/');
    }
    
    private function updateMigrationStatus($fileId, $status, $message = null) {
        $updateData = ['migration_status' => $status];
        
        if ($status === 'completed') {
            $updateData['migrated_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('file_id', $fileId)->update('files', $updateData);
        
        if ($message) {
            $this->logMigration($fileId, 'status_update', $status, $message);
        }
    }
    
    private function logMigration($fileId, $action, $status, $message) {
        $this->db->insert('migration_log', [
            'file_id' => $fileId,
            'action' => $action,
            'status' => $status,
            'message' => $message,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    private function log($message) {
        $logEntry = date('Y-m-d H:i:s') . " - " . $message . "\n";
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
        echo $logEntry;
    }
}

// CLI execution
if (php_sapi_name() === 'cli') {
    $batchSize = isset($argv[1]) ? (int)$argv[1] : 10;
    
    echo "Starting S3 migration with batch size: {$batchSize}\n";
    
    $migrator = new S3MigrationTool($batchSize);
    $result = $migrator->startMigration();
    
    echo "\nMigration Summary:\n";
    echo "Total files: " . $result['total'] . "\n";
    echo "Successful: " . $result['successful'] . "\n";
    echo "Failed: " . $result['failed'] . "\n";
    
    if ($result['failed'] > 0) {
        echo "\nCheck migration.log for details on failed migrations.\n";
        exit(1);
    }
    
    exit(0);
}
```

### Phase 3: Download System Integration

#### 3.1 Enhanced Download Controller

Create `controllers/downloads/file/sub_helpers/download/fileData/s3.php`:

```php
<?php
if (!defined("DB_HOST")) exit();

// Initialize S3 Manager
$s3Manager = new S3FileManager($_OPTIONS['s3']);

// Security validation
$securityValidator = new SecureDownloadValidator();
$validation = $securityValidator->validateDownloadRequest(
    $fileData['file_id'],
    $__USER['user_id'] ?? null,
    $_OPTIONS["inline"]["client_ip"]
);

if (!$validation['valid']) {
    $errorMessages = [
        'access_denied' => $_LANGUAGE['error_access_denied'],
        'ip_blocked' => $_LANGUAGE['error_ip_blocked'],
        'rate_limit_exceeded' => $_LANGUAGE['error_rate_limit'],
        'device_limit_exceeded' => $_LANGUAGE['error_device_limit']
    ];
    
    $errorMessage = $errorMessages[$validation['reason']] ?? $_LANGUAGE['error_download_unavailable'];
    
    do_update_pop_alert($_LANGUAGE['error_title'], $errorMessage, 'danger', 'red');
    exit(header("Location: " . do_format_url("downloads", "file", null, ["id" => $fileData['file_id']])));
}

// Generate secure presigned URL
$expiresIn = '+' . $_OPTIONS['s3']['presigned_url_expiry'] . ' seconds';
$ipAddress = $_OPTIONS["inline"]["client_ip"];
$userId = $__USER ? $__USER['user_id'] : null;

// Create unique hash for this download request
$urlHash = hash('sha256', $fileData['s3_key'] . $ipAddress . $userId . time() . uniqid());

try {
    $presignedUrl = $s3Manager->generatePresignedUrl($fileData['s3_key'], $expiresIn);
    
    if (!$presignedUrl) {
        throw new Exception("Failed to generate presigned URL");
    }
    
    // Store download record with restrictions
    $downloadRecord = [
        'file_id' => $fileData['file_id'],
        'user_id' => $userId,
        'ip_address' => $ipAddress,
        's3_key' => $fileData['s3_key'],
        'presigned_url' => $presignedUrl,
        'url_hash' => $urlHash,
        'expires_at' => date('Y-m-d H:i:s', strtotime($expiresIn)),
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('s3_downloads', $downloadRecord);
    
    // Generate secure token for proxy access
    $securityToken = hash_hmac('sha256', $urlHash, $_OPTIONS["system"]["security_hash"]);
    
    // Redirect to secure download proxy
    $proxyUrl = do_format_url('downloads', 'secure-proxy', null, [
        'hash' => $urlHash,
        'token' => $securityToken
    ]);
    
    // Update download statistics
    $db->where('file_id', $fileData['file_id'])
       ->update('files', ['downloads' => $db->inc(1)]);
    
    exit(header("Location: " . $proxyUrl));
    
} catch (Exception $e) {
    error_log("S3 Download Error: " . $e->getMessage());
    
    // Fallback to local file if available
    if ($fileData['storage_type'] === 'hybrid') {
        $localController = dirname(__FILE__) . '/local.php';
        if (file_exists($localController)) {
            include($localController);
            exit();
        }
    }
    
    do_update_pop_alert($_LANGUAGE['error_title'], $_LANGUAGE['error_download_unavailable'], 'danger', 'red');
    exit(header("Location: " . do_format_url("downloads", "file", null, ["id" => $fileData['file_id']])));
}
```

#### 3.2 Secure Download Proxy

Create `controllers/downloads/secure-proxy.php`:

```php
<?php
if (!defined("DB_HOST")) exit();

$hash = $_REQUEST['hash'] ?? '';
$token = $_REQUEST['token'] ?? '';

// Validate security token
$expectedToken = hash_hmac('sha256', $hash, $_OPTIONS["system"]["security_hash"]);

if (!hash_equals($expectedToken, $token)) {
    http_response_code(403);
    exit("Invalid access token");
}

// Validate download request
$s3Manager = new S3FileManager($_OPTIONS['s3']);
$ipAddress = $_OPTIONS["inline"]["client_ip"];
$userId = $__USER ? $__USER['user_id'] : null;

// Get download record
$downloadRecord = $db->where('url_hash', $hash)
                    ->where('is_active', 1)
                    ->getOne('s3_downloads');

if (!$downloadRecord) {
    exit(header("Location: " . do_format_url("downloads", "link-expired")));
}

// Validate IP binding
if ($downloadRecord['ip_address'] !== $ipAddress) {
    // Log security violation
    $db->insert('security_events', [
        'event_type' => 'ip_binding_violation',
        'severity' => 'high',
        'details' => json_encode([
            'expected_ip' => $downloadRecord['ip_address'],
            'actual_ip' => $ipAddress,
            'download_id' => $downloadRecord['s3_download_id']
        ]),
        'ip_address' => $ipAddress,
        'created_at' => date('Y-m-d H:i:s')
    ]);
    
    http_response_code(403);
    exit("Access denied: IP address mismatch");
}

// Check expiration
if (strtotime($downloadRecord['expires_at']) < time()) {
    exit(header("Location: " . do_format_url("downloads", "link-expired")));
}

// Validate user binding (if applicable)
if ($downloadRecord['user_id'] && $downloadRecord['user_id'] != $userId) {
    http_response_code(403);
    exit("Access denied: User mismatch");
}

// Update access tracking
$db->where('s3_download_id', $downloadRecord['s3_download_id'])
   ->update('s3_downloads', [
       'download_count' => $db->inc(1),
       'last_accessed' => date('Y-m-d H:i:s')
   ]);

// Log download analytics
$db->insert('download_analytics', [
    's3_download_id' => $downloadRecord['s3_download_id'],
    'file_id' => $downloadRecord['file_id'],
    'user_id' => $userId,
    'ip_address' => $ipAddress,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
    'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
    'download_time' => date('Y-m-d H:i:s')
]);

// Redirect to the actual S3 presigned URL
exit(header("Location: " . $downloadRecord['presigned_url']));
```

### Phase 4: Testing and Validation

#### 4.1 Migration Validation Script

Create `admin/tools/validate-migration.php`:

```php
<?php
class MigrationValidator {
    
    public function validateMigration() {
        $results = [
            'total_files' => 0,
            'migrated_files' => 0,
            'validation_errors' => [],
            'missing_files' => [],
            'size_mismatches' => []
        ];
        
        // Get all migrated files
        $migratedFiles = $this->db->where('storage_type', ['s3', 'hybrid'], 'IN')
                                 ->where('migration_status', 'completed')
                                 ->get('files');
        
        $results['total_files'] = count($migratedFiles);
        
        foreach ($migratedFiles as $file) {
            if ($this->validateFileInS3($file)) {
                $results['migrated_files']++;
            } else {
                $results['validation_errors'][] = $file['file_id'];
            }
        }
        
        return $results;
    }
    
    private function validateFileInS3($file) {
        $s3Manager = new S3FileManager($_OPTIONS['s3']);
        
        // Check if file exists in S3
        if (!$s3Manager->fileExists($file['s3_key'])) {
            return false;
        }
        
        // Validate file size (if possible)
        $localPath = $this->getLocalFilePath($file);
        if (file_exists($localPath)) {
            $localSize = filesize($localPath);
            // Note: S3 size validation would require additional API call
            // This is a simplified check
        }
        
        return true;
    }
}
```

## Post-Migration Tasks

### 1. Performance Optimization

#### Enable CloudFront CDN

```bash
# Create CloudFront distribution
aws cloudfront create-distribution --distribution-config file://cloudfront-config.json
```

#### CloudFront Configuration

```json
{
    "CallerReference": "download-center-cdn-2024",
    "Comment": "Download Center CDN",
    "DefaultRootObject": "",
    "Origins": {
        "Quantity": 1,
        "Items": [
            {
                "Id": "S3-download-center",
                "DomainName": "your-download-center-bucket.s3.amazonaws.com",
                "S3OriginConfig": {
                    "OriginAccessIdentity": ""
                }
            }
        ]
    },
    "DefaultCacheBehavior": {
        "TargetOriginId": "S3-download-center",
        "ViewerProtocolPolicy": "redirect-to-https",
        "TrustedSigners": {
            "Enabled": false,
            "Quantity": 0
        },
        "ForwardedValues": {
            "QueryString": true,
            "Cookies": {
                "Forward": "none"
            }
        },
        "MinTTL": 0,
        "DefaultTTL": 86400,
        "MaxTTL": 31536000
    },
    "Enabled": true,
    "PriceClass": "PriceClass_All"
}
```

### 2. Cleanup Local Files (Optional)

```php
// Script to remove local files after successful migration
class LocalFileCleanup {
    
    public function cleanupMigratedFiles($dryRun = true) {
        $files = $this->db->where('storage_type', 's3')
                         ->where('migration_status', 'completed')
                         ->get('files');
        
        $cleaned = 0;
        $errors = 0;
        
        foreach ($files as $file) {
            $localPath = $this->getLocalFilePath($file);
            
            if (file_exists($localPath)) {
                if (!$dryRun) {
                    if (unlink($localPath)) {
                        $cleaned++;
                        $this->log("Removed local file: " . $localPath);
                    } else {
                        $errors++;
                        $this->log("Failed to remove: " . $localPath);
                    }
                } else {
                    $this->log("Would remove: " . $localPath);
                    $cleaned++;
                }
            }
        }
        
        return ['cleaned' => $cleaned, 'errors' => $errors];
    }
}
```

### 3. Monitoring Setup

#### CloudWatch Alarms

```bash
# Create S3 monitoring alarms
aws cloudwatch put-metric-alarm \
    --alarm-name "S3-HighErrorRate" \
    --alarm-description "S3 error rate too high" \
    --metric-name "4xxErrors" \
    --namespace "AWS/S3" \
    --statistic "Sum" \
    --period 300 \
    --threshold 10 \
    --comparison-operator "GreaterThanThreshold" \
    --evaluation-periods 2
```

## Rollback Plan

### Emergency Rollback Procedure

1. **Immediate Rollback**:
   ```php
   // Update all files to use local storage
   $db->where('storage_type', ['s3', 'hybrid'], 'IN')
      ->update('files', ['storage_type' => 'local']);
   ```

2. **Restore Download Controllers**:
   - Rename S3 controllers to `.backup`
   - Restore original local controllers

3. **Database Rollback**:
   ```sql
   -- Reset migration status
   UPDATE res_files SET 
       storage_type = 'local',
       migration_status = 'pending',
       s3_key = NULL,
       s3_bucket = NULL,
       s3_metadata = NULL,
       migrated_at = NULL;
   ```

## Troubleshooting Common Issues

### Issue 1: Upload Failures

**Symptoms**: Files fail to upload to S3
**Solutions**:
- Check AWS credentials and permissions
- Verify S3 bucket policy
- Check file size limits
- Monitor AWS service status

### Issue 2: Presigned URL Generation Fails

**Symptoms**: Download links return 403 errors
**Solutions**:
- Verify IAM permissions for GetObject
- Check bucket policy allows presigned URLs
- Validate S3 key format
- Check AWS region configuration

### Issue 3: High AWS Costs

**Symptoms**: Unexpected AWS charges
**Solutions**:
- Implement CloudFront CDN
- Use S3 Intelligent Tiering
- Monitor data transfer patterns
- Optimize file sizes

This migration guide provides a comprehensive approach to moving your Digital Download Center to S3 while maintaining security, performance, and reliability.
