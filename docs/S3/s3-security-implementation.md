# S3 Security Implementation Guide
## Multi-Layer Security for Digital Downloads

---

## Overview

This document provides detailed implementation guidelines for the security features of the S3 integration, focusing on IP address binding, user account binding, and comprehensive security validation.

## Security Architecture

### Multi-Layer Security Model

```
┌─────────────────────────────────────────────────────────────┐
│                    Security Layers                          │
├─────────────────────────────────────────────────────────────┤
│ Layer 1: User Authentication & Purchase Verification       │
│ Layer 2: IP Address Validation & Binding                   │
│ Layer 3: Rate Limiting & Abuse Prevention                  │
│ Layer 4: Device Fingerprinting (Premium Users)             │
│ Layer 5: Token Signature Validation                        │
│ Layer 6: AWS S3 Bucket Policy Enforcement                  │
└─────────────────────────────────────────────────────────────┘
```

## Layer 1: User Authentication & Purchase Verification

### Implementation

```php
class UserAuthenticationValidator {
    
    public static function validateUserAccess($fileId, $userId) {
        global $db;
        
        // Check if user exists and is active
        if (!$userId) {
            return self::validateGuestAccess($fileId);
        }
        
        $user = $db->where('user_id', $userId)
                  ->where('is_active', 1)
                  ->getOne('users');
        
        if (!$user) {
            self::logSecurityEvent('invalid_user', [
                'user_id' => $userId,
                'file_id' => $fileId
            ]);
            return false;
        }
        
        // Verify purchase for paid files
        return self::verifyFilePurchase($fileId, $userId);
    }
    
    private static function verifyFilePurchase($fileId, $userId) {
        global $db;
        
        $file = $db->where('file_id', $fileId)->getOne('files');
        
        if ($file['price'] > 0) {
            $purchase = $db->where('user_id', $userId)
                          ->where('file_id', $fileId)
                          ->where('is_active', 1)
                          ->getOne('ufiles');
            
            if (!$purchase) {
                self::logSecurityEvent('unpaid_access_attempt', [
                    'user_id' => $userId,
                    'file_id' => $fileId,
                    'file_price' => $file['price']
                ]);
                return false;
            }
            
            // Check if purchase has expired
            if ($purchase['date_expire'] && strtotime($purchase['date_expire']) < time()) {
                self::logSecurityEvent('expired_purchase_access', [
                    'user_id' => $userId,
                    'file_id' => $fileId,
                    'expired_date' => $purchase['date_expire']
                ]);
                return false;
            }
        }
        
        return true;
    }
    
    private static function validateGuestAccess($fileId) {
        global $db;
        
        $file = $db->where('file_id', $fileId)->getOne('files');
        
        // Only allow free files for guests
        if ($file['price'] > 0) {
            self::logSecurityEvent('guest_paid_access_attempt', [
                'file_id' => $fileId,
                'file_price' => $file['price']
            ]);
            return false;
        }
        
        return true;
    }
}
```

## Layer 2: IP Address Validation & Binding

### IP Detection and Validation

```php
class IPAddressValidator {
    
    public static function detectClientIP() {
        $ipSources = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancers/proxies
            'HTTP_X_REAL_IP',            // Nginx proxy
            'HTTP_CLIENT_IP',            // Proxy servers
            'REMOTE_ADDR'                // Direct connection
        ];
        
        foreach ($ipSources as $source) {
            if (!empty($_SERVER[$source])) {
                $ip = $_SERVER[$source];
                
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (self::isValidIP($ip)) {
                    return $ip;
                }
            }
        }
        
        return '0.0.0.0'; // Fallback
    }
    
    public static function isValidIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP, 
            FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE
        ) !== false;
    }
    
    public static function validateIPBinding($downloadHash, $currentIP) {
        global $db;
        
        $download = $db->where('url_hash', $downloadHash)
                      ->where('is_active', 1)
                      ->getOne('s3_downloads');
        
        if (!$download) {
            self::logSecurityEvent('invalid_download_hash', [
                'hash' => $downloadHash,
                'ip' => $currentIP
            ]);
            return false;
        }
        
        // Strict IP matching
        if (!hash_equals($download['ip_address'], $currentIP)) {
            self::logSecurityEvent('ip_binding_violation', [
                'expected_ip' => $download['ip_address'],
                'actual_ip' => $currentIP,
                'download_id' => $download['s3_download_id'],
                'user_id' => $download['user_id']
            ]);
            
            // Increment violation counter
            self::incrementIPViolation($currentIP);
            
            return false;
        }
        
        return true;
    }
    
    private static function incrementIPViolation($ip) {
        global $db;
        
        $violation = $db->where('ip_address', $ip)
                       ->where('date_created', date('Y-m-d'), '>=')
                       ->getOne('ip_violations');
        
        if ($violation) {
            $db->where('violation_id', $violation['violation_id'])
               ->update('ip_violations', ['count' => $db->inc(1)]);
        } else {
            $db->insert('ip_violations', [
                'ip_address' => $ip,
                'count' => 1,
                'date_created' => date('Y-m-d H:i:s')
            ]);
        }
        
        // Auto-block IP after 10 violations
        if (($violation['count'] ?? 0) >= 10) {
            self::blockIPAddress($ip, 'automatic_violation_threshold');
        }
    }
    
    public static function blockIPAddress($ip, $reason) {
        global $db;
        
        $db->insert('ip_blacklist', [
            'ip_address' => $ip,
            'reason' => $reason,
            'blocked_at' => date('Y-m-d H:i:s'),
            'is_active' => 1
        ]);
        
        self::logSecurityEvent('ip_blocked', [
            'ip_address' => $ip,
            'reason' => $reason
        ]);
    }
}
```

### IP Geolocation and Risk Assessment

```php
class IPRiskAssessment {
    
    public static function assessIPRisk($ip) {
        $riskScore = 0;
        $riskFactors = [];
        
        // Check if IP is from high-risk country
        $country = self::getCountryFromIP($ip);
        if (in_array($country, self::getHighRiskCountries())) {
            $riskScore += 30;
            $riskFactors[] = 'high_risk_country';
        }
        
        // Check if IP is a known proxy/VPN
        if (self::isProxyIP($ip)) {
            $riskScore += 50;
            $riskFactors[] = 'proxy_vpn';
        }
        
        // Check download history from this IP
        $recentDownloads = self::getRecentDownloads($ip);
        if ($recentDownloads > 100) {
            $riskScore += 40;
            $riskFactors[] = 'high_download_volume';
        }
        
        // Check if IP has multiple user associations
        $userCount = self::getUniqueUsersFromIP($ip);
        if ($userCount > 10) {
            $riskScore += 25;
            $riskFactors[] = 'multiple_users';
        }
        
        return [
            'risk_score' => $riskScore,
            'risk_level' => self::getRiskLevel($riskScore),
            'risk_factors' => $riskFactors
        ];
    }
    
    private static function getRiskLevel($score) {
        if ($score >= 80) return 'high';
        if ($score >= 50) return 'medium';
        if ($score >= 20) return 'low';
        return 'minimal';
    }
    
    private static function getHighRiskCountries() {
        return ['CN', 'RU', 'IR', 'KP']; // Example high-risk countries
    }
}
```

## Layer 3: Rate Limiting & Abuse Prevention

### Advanced Rate Limiting

```php
class RateLimitingSystem {
    
    private static $limits = [
        'guest' => [
            'downloads_per_hour' => 5,
            'downloads_per_day' => 20,
            'bandwidth_per_hour' => 1024 * 1024 * 1024, // 1GB
        ],
        'user' => [
            'downloads_per_hour' => 50,
            'downloads_per_day' => 200,
            'bandwidth_per_hour' => 10 * 1024 * 1024 * 1024, // 10GB
        ],
        'premium' => [
            'downloads_per_hour' => 200,
            'downloads_per_day' => 1000,
            'bandwidth_per_hour' => 50 * 1024 * 1024 * 1024, // 50GB
        ]
    ];
    
    public static function checkRateLimit($userId, $ipAddress, $fileSize) {
        $userType = self::getUserType($userId);
        $limits = self::$limits[$userType];
        
        // Check hourly download count
        if (!self::checkDownloadCount($userId, $ipAddress, 'hour', $limits['downloads_per_hour'])) {
            return ['allowed' => false, 'reason' => 'hourly_download_limit'];
        }
        
        // Check daily download count
        if (!self::checkDownloadCount($userId, $ipAddress, 'day', $limits['downloads_per_day'])) {
            return ['allowed' => false, 'reason' => 'daily_download_limit'];
        }
        
        // Check bandwidth limits
        if (!self::checkBandwidthLimit($userId, $ipAddress, $fileSize, $limits['bandwidth_per_hour'])) {
            return ['allowed' => false, 'reason' => 'bandwidth_limit'];
        }
        
        return ['allowed' => true];
    }
    
    private static function checkDownloadCount($userId, $ipAddress, $period, $limit) {
        global $db;
        
        $timeCondition = $period === 'hour' ? 
            date('Y-m-d H:i:s', strtotime('-1 hour')) :
            date('Y-m-d H:i:s', strtotime('-1 day'));
        
        $query = $db->where('created_at', $timeCondition, '>=');
        
        if ($userId) {
            $query->where('user_id', $userId);
        } else {
            $query->where('ip_address', $ipAddress);
        }
        
        $count = $query->getValue('s3_downloads', 'count(*)');
        
        return $count < $limit;
    }
    
    private static function checkBandwidthLimit($userId, $ipAddress, $fileSize, $limit) {
        global $db;
        
        $timeCondition = date('Y-m-d H:i:s', strtotime('-1 hour'));
        
        $query = $db->where('created_at', $timeCondition, '>=');
        
        if ($userId) {
            $query->where('user_id', $userId);
        } else {
            $query->where('ip_address', $ipAddress);
        }
        
        $downloads = $query->get('s3_downloads');
        
        $totalBandwidth = 0;
        foreach ($downloads as $download) {
            $file = $db->where('file_id', $download['file_id'])->getOne('files');
            $totalBandwidth += $file['size'];
        }
        
        return ($totalBandwidth + $fileSize) <= $limit;
    }
}
```

## Layer 4: Device Fingerprinting

### Advanced Device Fingerprinting

```php
class DeviceFingerprintValidator {
    
    public static function generateFingerprint($userAgent, $acceptLanguage, $acceptEncoding) {
        $components = [
            'user_agent' => $userAgent,
            'accept_language' => $acceptLanguage,
            'accept_encoding' => $acceptEncoding,
            'screen_resolution' => $_POST['screen_resolution'] ?? '',
            'timezone' => $_POST['timezone'] ?? '',
            'plugins' => $_POST['plugins'] ?? ''
        ];
        
        return hash('sha256', json_encode($components));
    }
    
    public static function validateDeviceLimit($userId, $currentFingerprint) {
        global $db;
        
        if (!$userId) return true; // Skip for guests
        
        $user = $db->where('user_id', $userId)->getOne('users');
        $userType = self::getUserType($user);
        
        $deviceLimits = [
            'basic' => 2,
            'premium' => 5,
            'enterprise' => 10
        ];
        
        $maxDevices = $deviceLimits[$userType] ?? 2;
        
        // Get active devices for user
        $activeDevices = $db->where('user_id', $userId)
                           ->where('last_seen', date('Y-m-d H:i:s', strtotime('-30 days')), '>')
                           ->get('user_devices');
        
        // Check if current device is already registered
        $currentDevice = null;
        foreach ($activeDevices as $device) {
            if ($device['fingerprint'] === $currentFingerprint) {
                $currentDevice = $device;
                break;
            }
        }
        
        if ($currentDevice) {
            // Update last seen
            $db->where('device_id', $currentDevice['device_id'])
               ->update('user_devices', ['last_seen' => date('Y-m-d H:i:s')]);
            return true;
        }
        
        // Check if user has reached device limit
        if (count($activeDevices) >= $maxDevices) {
            self::logSecurityEvent('device_limit_exceeded', [
                'user_id' => $userId,
                'current_devices' => count($activeDevices),
                'max_devices' => $maxDevices,
                'fingerprint' => $currentFingerprint
            ]);
            return false;
        }
        
        // Register new device
        $db->insert('user_devices', [
            'user_id' => $userId,
            'fingerprint' => $currentFingerprint,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'ip_address' => IPAddressValidator::detectClientIP(),
            'first_seen' => date('Y-m-d H:i:s'),
            'last_seen' => date('Y-m-d H:i:s'),
            'is_active' => 1
        ]);
        
        return true;
    }
}
```

## Layer 5: Token Signature Validation

### Cryptographic Token System

```php
class SecureTokenSystem {
    
    private static $algorithm = 'sha256';
    private static $tokenVersion = 'v2';
    
    public static function generateSecureToken($payload) {
        global $_OPTIONS;
        
        $tokenData = [
            'version' => self::$tokenVersion,
            'issued_at' => time(),
            'payload' => $payload,
            'nonce' => bin2hex(random_bytes(16))
        ];
        
        $signature = self::signData($tokenData, $_OPTIONS["system"]["security_hash"]);
        
        return base64_encode(json_encode([
            'data' => $tokenData,
            'signature' => $signature
        ]));
    }
    
    public static function validateToken($token) {
        global $_OPTIONS;
        
        try {
            $decoded = json_decode(base64_decode($token), true);
            
            if (!$decoded || !isset($decoded['data']) || !isset($decoded['signature'])) {
                return false;
            }
            
            // Verify signature
            $expectedSignature = self::signData($decoded['data'], $_OPTIONS["system"]["security_hash"]);
            
            if (!hash_equals($expectedSignature, $decoded['signature'])) {
                self::logSecurityEvent('token_signature_invalid', [
                    'token_preview' => substr($token, 0, 50) . '...'
                ]);
                return false;
            }
            
            // Check token version
            if ($decoded['data']['version'] !== self::$tokenVersion) {
                self::logSecurityEvent('token_version_mismatch', [
                    'expected' => self::$tokenVersion,
                    'received' => $decoded['data']['version']
                ]);
                return false;
            }
            
            // Check expiration
            $payload = $decoded['data']['payload'];
            if (isset($payload['expires']) && time() > $payload['expires']) {
                return false;
            }
            
            return $payload;
            
        } catch (Exception $e) {
            self::logSecurityEvent('token_validation_error', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    private static function signData($data, $secret) {
        return hash_hmac(self::$algorithm, json_encode($data), $secret);
    }
}
```

## Security Monitoring and Alerting

### Real-time Security Monitoring

```php
class SecurityMonitor {
    
    public static function logSecurityEvent($type, $details, $severity = 'medium') {
        global $db;
        
        $event = [
            'event_type' => $type,
            'severity' => $severity,
            'details' => json_encode($details),
            'ip_address' => IPAddressValidator::detectClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'user_id' => $_SESSION['user_id'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $db->insert('security_events', $event);
        
        // Send alerts for high severity events
        if ($severity === 'high') {
            self::sendSecurityAlert($event);
        }
        
        // Auto-response for critical events
        if ($severity === 'critical') {
            self::triggerAutoResponse($type, $details);
        }
    }
    
    private static function sendSecurityAlert($event) {
        // Send email/SMS alert to administrators
        $message = "Security Alert: {$event['event_type']}\n";
        $message .= "Severity: {$event['severity']}\n";
        $message .= "IP: {$event['ip_address']}\n";
        $message .= "Time: {$event['created_at']}\n";
        $message .= "Details: {$event['details']}\n";
        
        // Send via email/SMS/Slack
        self::sendAlert($message);
    }
    
    private static function triggerAutoResponse($type, $details) {
        switch ($type) {
            case 'multiple_ip_violations':
                IPAddressValidator::blockIPAddress($details['ip_address'], 'auto_block_violations');
                break;
                
            case 'brute_force_attempt':
                self::implementRateLimit($details['ip_address'], 3600); // 1 hour block
                break;
                
            case 'suspicious_download_pattern':
                self::flagUserForReview($details['user_id']);
                break;
        }
    }
}
```

## Security Configuration

### Security Settings

```php
// Security configuration in includes/settings.php
$CONFIG['security'] = [
    'ip_binding_enabled' => true,
    'device_fingerprinting_enabled' => true,
    'rate_limiting_enabled' => true,
    'geolocation_blocking_enabled' => true,
    'proxy_detection_enabled' => true,
    
    'max_download_attempts' => 5,
    'ip_violation_threshold' => 10,
    'auto_block_duration' => 3600, // 1 hour
    
    'token_expiry' => 86400, // 24 hours
    'token_algorithm' => 'sha256',
    
    'high_risk_countries' => ['CN', 'RU', 'IR', 'KP'],
    'blocked_user_agents' => ['bot', 'crawler', 'spider'],
    
    'monitoring_enabled' => true,
    'alert_email' => '<EMAIL>',
    'alert_threshold' => 'high'
];
```

## Database Schema for Security

```sql
-- Security events table
CREATE TABLE `res_security_events` (
    `event_id` int(11) NOT NULL AUTO_INCREMENT,
    `event_type` varchar(100) NOT NULL,
    `severity` enum('low','medium','high','critical') DEFAULT 'medium',
    `details` json DEFAULT NULL,
    `ip_address` varchar(45) NOT NULL,
    `user_agent` text,
    `user_id` int(11) DEFAULT NULL,
    `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`event_id`),
    KEY `idx_type_severity` (`event_type`, `severity`),
    KEY `idx_ip_time` (`ip_address`, `created_at`),
    KEY `idx_user_time` (`user_id`, `created_at`)
);

-- IP violations table
CREATE TABLE `res_ip_violations` (
    `violation_id` int(11) NOT NULL AUTO_INCREMENT,
    `ip_address` varchar(45) NOT NULL,
    `count` int(11) DEFAULT 1,
    `date_created` date NOT NULL,
    PRIMARY KEY (`violation_id`),
    UNIQUE KEY `idx_ip_date` (`ip_address`, `date_created`)
);

-- User devices table
CREATE TABLE `res_user_devices` (
    `device_id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `fingerprint` varchar(64) NOT NULL,
    `user_agent` text,
    `ip_address` varchar(45),
    `first_seen` datetime DEFAULT CURRENT_TIMESTAMP,
    `last_seen` datetime DEFAULT CURRENT_TIMESTAMP,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`device_id`),
    KEY `idx_user_active` (`user_id`, `is_active`),
    KEY `idx_fingerprint` (`fingerprint`)
);

-- IP blacklist table
CREATE TABLE `res_ip_blacklist` (
    `blacklist_id` int(11) NOT NULL AUTO_INCREMENT,
    `ip_address` varchar(45) NOT NULL,
    `reason` varchar(255),
    `blocked_at` datetime DEFAULT CURRENT_TIMESTAMP,
    `expires_at` datetime DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`blacklist_id`),
    UNIQUE KEY `idx_ip_active` (`ip_address`, `is_active`)
);
```

## Testing Security Implementation

### Security Test Suite

```php
class SecurityTestSuite {
    
    public function runAllTests() {
        $results = [];
        
        $results['ip_binding'] = $this->testIPBinding();
        $results['token_validation'] = $this->testTokenValidation();
        $results['rate_limiting'] = $this->testRateLimiting();
        $results['device_fingerprinting'] = $this->testDeviceFingerprinting();
        
        return $results;
    }
    
    public function testIPBinding() {
        // Test IP binding enforcement
        $testIP1 = '***********';
        $testIP2 = '***********';
        
        // Generate token for IP1
        $token = SecureTokenSystem::generateSecureToken([
            'file_id' => 1,
            'user_id' => 1,
            'ip_address' => $testIP1,
            'expires' => time() + 3600
        ]);
        
        // Simulate access from IP2
        $_SERVER['REMOTE_ADDR'] = $testIP2;
        $result = IPAddressValidator::validateIPBinding(md5($token), $testIP2);
        
        return !$result; // Should return false (blocked)
    }
    
    public function testTokenValidation() {
        // Test token signature validation
        $validToken = SecureTokenSystem::generateSecureToken([
            'file_id' => 1,
            'expires' => time() + 3600
        ]);
        
        $invalidToken = str_replace('a', 'b', $validToken); // Corrupt token
        
        $validResult = SecureTokenSystem::validateToken($validToken);
        $invalidResult = SecureTokenSystem::validateToken($invalidToken);
        
        return $validResult !== false && $invalidResult === false;
    }
}
```

This security implementation provides comprehensive protection for your S3-integrated download system with multiple layers of validation and real-time monitoring capabilities.
